name: app_developer
description: A new Flutter package project.
version: 0.0.1
publish_to: "none"
homepage: https://example.com/

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # Core
  equatable: ^2.0.3
  # state management
  flutter_bloc: ^8.1.1
  shared_preferences: ^2.0.15

  # dependency
  get_it: ^7.2.0

  # imagewidget: ^1.0.11
  easy_device_info: 1.1.4
  adaptive_theme: ^3.0.0
  expansion_widget: ^0.0.3

  meditaguru:
    path: ../../

dependency_overrides:
#  intl: ^0.17.0
#  intl_translation: ^0.17.0
#  provider: ^6.0.2
#  flutter_html: ^2.2.1
  flutter_svg: 2.0.7
  http: ^0.13.5
  vector_math: ^2.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
# flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
