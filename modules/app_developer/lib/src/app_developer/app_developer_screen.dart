import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'configs/app_dev_config.dart';
import 'drawer_menu/drawer_menu.dart';
import 'views/widgets/group_widget.dart';
import 'views/widgets/list_item_widget.dart';

part 'views/dev_components.dart';
part 'views/dev_features.dart';
part 'views/dev_screens.dart';
part 'views/dev_widgets.dart';

class AppDeveloperScreen extends StatefulWidget {
  static const String routeName = 'devmode';

  const AppDeveloperScreen({Key? key}) : super(key: key);

  @override
  State<AppDeveloperScreen> createState() => _AppDeveloperScreenState();
}

class _AppDeveloperScreenState extends State<AppDeveloperScreen> {
  @override
  Widget build(BuildContext context) {
    return Material(
      child: DefaultTabController(
        length: 4,
        child: Scaffold(
          backgroundColor: AppDevUIConfig.backgroundColor ??
              Theme.of(context).colorScheme.surface,
          drawer: DrawerMenu(
            header: Container(
              padding: EdgeInsets.only(
                  left: 16, right: 16, top: MediaQuery.of(context).padding.top),
              child: Container(
                margin: const EdgeInsets.all(10),
                child: FittedBox(
                  fit: BoxFit.contain,
                  child: Row(
                    children: [
                      const SizedBox(width: 10),
                      Text(
                        'Medita App',
                        style: Theme.of(context).textTheme.displayLarge,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            items: listSetting.map(DrawerMenuItem.fromJson).toList(),
          ),
          appBar: AppBar(
            backgroundColor: AppDevUIConfig.backgroundColor ??
                Theme.of(context).colorScheme.surface,
            title: Text(
              'Developer Mode',
              style: TextStyle(
                color: AppDevUIConfig.textColor,
              ),
            ),
            actions: [
              IconButton(
                onPressed: Navigator.of(context).pop,
                icon: const Icon(Icons.close),
              ),
            ],
            centerTitle: true,
            bottom: TabBar(
              labelStyle: TextStyle(
                fontSize: 15,
                color: AppDevUIConfig.textColor,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.secondary,
              ),
              tabs: const [
                Tab(text: 'Widgets'),
                Tab(text: 'Components'),
                Tab(text: 'Features'),
                Tab(text: 'Screens'),
              ],
            ),
          ),
          body: const Stack(
            children: [
              Padding(
                padding: EdgeInsets.all(8.0),
                child: TabBarView(
                  children: [
                    DevWidgets(),
                    DevComponents(),
                    DevFeatures(),
                    DevScreens(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

final listSetting = [
  {
    'title': 'Overview',
    'action': (BuildContext context) {
      Navigator.of(context).pop();
      Navigator.of(context).push(PageRouteBuilder(
        opaque: false,
        barrierColor: Colors.transparent,
        barrierDismissible: true,
        transitionsBuilder: (c, anim, a2, child) =>
            FadeTransition(opacity: anim, child: child),
        transitionDuration: const Duration(milliseconds: 200),
        pageBuilder: (_, ___, context) => Material(
          color: Colors.transparent,
          child: Scaffold(
            appBar: AppBar(
              title: const Text('ImageList'),
            ),
            // body: const DemoImageListPage()
          ),
        ),
      ));
    },
  },
  {
    'title': 'UI',
    'action': () {
      debugPrint('-----> tap menu: Components');
    },
    'items': [
      {
        'title': 'Button',
        'action': () {
          debugPrint('-----> tap menu: Button');
        },
      },
      {
        'title': 'TextBox',
        'action': () {
          debugPrint('-----> tap menu: TextBox');
        },
      },
    ]
  },
  {
    'title': 'Components',
    'action': () {
      debugPrint('-----> tap menu: Components');
    },
    'items': [
      {
        'title': 'ImageList',
        'action': (BuildContext context) {
          Navigator.of(context).push(PageRouteBuilder(
            opaque: false,
            barrierColor: Colors.transparent,
            barrierDismissible: true,
            transitionsBuilder: (c, anim, a2, child) =>
                FadeTransition(opacity: anim, child: child),
            transitionDuration: const Duration(milliseconds: 200),
            pageBuilder: (_, ___, context) => Material(
              color: Colors.transparent,
              child: Scaffold(
                appBar: AppBar(
                  title: const Text('ImageList'),
                ),
                // body: const DemoImageListPage(),
              ),
            ),
          ));
        },
      },
      {
        'title': 'TextBox',
        'action': () {
          debugPrint('-----> tap menu: TextBox');
        },
      },
    ]
  },
];

class HeaederDevWidget extends StatelessWidget {
  final String text;
  const HeaederDevWidget(this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        color: AppDevUIConfig.textColor,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}
