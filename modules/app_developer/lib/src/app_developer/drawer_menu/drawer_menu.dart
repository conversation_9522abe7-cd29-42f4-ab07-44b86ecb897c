import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:imagewidget/imagewidget.dart';

typedef MenuAction = void Function(BuildContext context);

class DrawerMenuItem {
  final String title;
  final String? icon;
  final MenuAction? action;
  final List<DrawerMenuItem>? items;

  DrawerMenuItem({
    required this.title,
    this.icon,
    this.action,
    this.items,
  });

  factory DrawerMenuItem.fromJson(Map<String, dynamic> map) {
    return DrawerMenuItem(
      title: map['title'] ?? '',
      icon: map['icon'],
      action: map['action'] != null && (map['action'] is Function(BuildContext))
          ? (map['action'] as Function(BuildContext))
          : null,
      items: map['items'] != null
          ? List<DrawerMenuItem>.from(
              map['items']?.map(DrawerMenuItem.fromJson))
          : null,
    );
  }

  @override
  String toString() {
    return 'DrawerMenuItem(title: $title, icon: $icon,'
        ' action: $action, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is DrawerMenuItem &&
        other.title == title &&
        other.icon == icon &&
        other.action == action &&
        listEquals(other.items, items);
  }

  @override
  int get hashCode {
    return title.hashCode ^ icon.hashCode ^ action.hashCode ^ items.hashCode;
  }
}

class DrawerMenu extends StatelessWidget {
  final List<DrawerMenuItem> items;
  final Widget? header;

  const DrawerMenu({
    Key? key,
    this.header,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      child: Column(
        children: [
          if (header != null) header!,
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListExpansion(
                textColor: Colors.black,
                items: List.generate(
                  items.length,
                  (index) {
                    final item = items[index];

                    return ItemListExpantion(
                      title: item.title,
                      onTap: () {
                        item.action?.call(context);
                      },
                      icon: (item.icon?.isNotEmpty ?? false)
                          ? Image.asset(
                              item.icon!,
                              fit: BoxFit.contain,
                            )
                          : null,
                      items: (item.items?.isNotEmpty ?? false)
                          ? List.generate(
                              item.items!.length,
                              (indexChild) {
                                final itemChild = item.items![indexChild];

                                return ItemListExpantion(
                                  title: itemChild.title,
                                  onTap: () {
                                    itemChild.action?.call(context);
                                  },
                                );
                              },
                            )
                          : null,
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ListExpansion extends StatefulWidget {
  final List<ItemListExpantion> items;
  final double sizeIcon;
  final Duration durationExpand;
  final Color? textColor;

  const ListExpansion({
    Key? key,
    required this.items,
    this.sizeIcon = 25.0,
    this.durationExpand = const Duration(milliseconds: 200),
    required this.textColor,
  }) : super(key: key);

  @override
  State<ListExpansion> createState() => _ListExpansionState();
}

class _ListExpansionState extends State<ListExpansion> {
  void enableExpand(ItemListExpantion item) {
    setState(() {
      item.isExpanded = !item.isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(0),
      itemCount: widget.items.length,
      separatorBuilder: _separatorBuider,
      itemBuilder: (context, index) {
        final item = widget.items[index];
        final avaliableChild = item.items?.isNotEmpty ?? false;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            InkWell(
              onTap: () {
                avaliableChild ? enableExpand(item) : item.onTap?.call();
              },
              child: ListTile(
                leading: item.icon != null ? _renderIcon(item) : null,
                title: Text(
                  item.title,
                  style: TextStyle(
                    fontSize: 14,
                    color: item.isExpanded
                        ? Theme.of(context).colorScheme.secondary
                        : null,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                trailing: avaliableChild
                    ? ExpandIcon(
                        onPressed: (bool value) => enableExpand(item),
                        color: widget.textColor,
                        isExpanded: item.isExpanded,
                      )
                    : null,
              ),
            ),
            AnimatedSize(
              duration: widget.durationExpand,
              curve: Curves.fastOutSlowIn,
              child: (item.isExpanded && avaliableChild)
                  ? ListView.separated(
                      padding: const EdgeInsets.all(0),
                      itemBuilder: (context, indexItem) {
                        final childItem = item.items![indexItem];

                        return InkWell(
                          onTap: childItem.onTap,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 12.0),
                            child: ListTile(
                              leading: childItem.icon,
                              title: Text(
                                childItem.title,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      separatorBuilder: _separatorBuider,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: item.items!.length,
                      shrinkWrap: true,
                    )
                  : const SizedBox.shrink(),
            )
          ],
        );
      },
    );
  }

  Widget _separatorBuider(BuildContext ct, index) => Container(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        height: 1.5,
      );

  Widget _renderIcon(ItemListExpantion item) {
    final colorIcon = item.isExpanded
        ? Theme.of(context).colorScheme.secondary
        : widget.textColor;
    // Theme.of(context).themeColor.textColor;

    // if (item.icon is Image) {
    //   return SizedBox(
    //     width: widget.sizeIcon,
    //     height: widget.sizeIcon,
    //     child: (item.icon as Image).copyWith(color: colorIcon),
    //   );
    // }

    if (item.icon is Icon) {
      final iconOld = item.icon as Icon;
      final iconNew = Icon(
        iconOld.icon,
        color: colorIcon,
        size: widget.sizeIcon - 5,
      );

      return iconNew;
    }

    return SizedBox(width: widget.sizeIcon, height: widget.sizeIcon);
  }
}

class ItemListExpantion {
  ItemListExpantion({
    required this.title,
    this.onTap,
    this.icon,
    this.isExpanded = false,
    this.items,
  });

  final String title;
  final Widget? icon;
  final List<ItemListExpantion>? items;
  final Function()? onTap;
  bool isExpanded;
}
