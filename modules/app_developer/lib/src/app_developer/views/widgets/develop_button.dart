import 'package:flutter/material.dart';

import '../../../app_developer_routes.dart';

class DeveloperButton extends StatelessWidget {
  const DeveloperButton({super.key, this.hasBottomBar = false});
  final bool hasBottomBar;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          hasBottomBar ? const EdgeInsets.only(bottom: 60) : EdgeInsets.zero,
      child: FloatingActionButton(
        onPressed: context.startDevMode,
        child: const Text('Dev'),
      ),
    );
  }
}
