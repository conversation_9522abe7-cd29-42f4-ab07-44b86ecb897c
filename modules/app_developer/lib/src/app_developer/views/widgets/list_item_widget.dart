import 'package:flutter/material.dart';

import '../../configs/app_dev_config.dart';

class ListItemWidget extends StatelessWidget {
  const ListItemWidget({
    Key? key,
    required this.items,
    this.padding = const EdgeInsets.symmetric(vertical: 16),
    this.backgroundColor,
  }) : super(key: key);

  final List<Widget> items;
  final EdgeInsets padding;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ??
          AppDevUIConfig.backgroundColor ??
          Theme.of(context).colorScheme.surface,
      child: ListView.separated(
        itemBuilder: (context, index) => items[index],
        separatorBuilder: (context, index) => const SizedBox(height: 10),
        itemCount: items.length,
      ),
    );
  }
}
