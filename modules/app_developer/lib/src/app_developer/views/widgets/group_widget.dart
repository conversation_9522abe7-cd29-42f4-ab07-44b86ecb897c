import 'package:flutter/material.dart';

import '../../configs/app_dev_config.dart';

class GroupWidget extends StatelessWidget {
  const GroupWidget({
    super.key,
    required this.children,
    required this.name,
    this.shrinkWrapChild = true,
    this.paddingContent = const EdgeInsets.symmetric(vertical: 10),
    this.separator,
    this.backgroundColor,
    this.isMaxSizeHorizontal = true,
    this.alignment,
  });

  final String name;
  final List<Widget> children;
  final bool shrinkWrapChild;
  final EdgeInsets paddingContent;
  final Widget? separator;
  final Color? backgroundColor;
  final bool isMaxSizeHorizontal;
  final AlignmentGeometry? alignment;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: paddingContent,
      child: InputDecorator(
        decoration: InputDecoration(
          filled: true,
          fillColor: backgroundColor ??
              AppDevUIConfig.backgroundColor ??
              Theme.of(context).scaffoldBackgroundColor,
          labelText: name,
          labelStyle: const TextStyle(fontSize: 20, color: Colors.white),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(
              color: AppDevUIConfig.borderColor ?? Colors.black,
              width: 1.5,
            ),
          ),
        ),
        child: ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: shrinkWrapChild,
          itemBuilder: (context, index) {
            if (isMaxSizeHorizontal) {
              if (alignment != null) {
                return Align(
                  alignment: alignment!,
                  child: children[index],
                );
              }

              return children[index];
            }

            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (alignment != null)
                  Align(
                    alignment: alignment!,
                    child: children[index],
                  )
                else
                  children[index],
              ],
            );
          },
          separatorBuilder: (context, index) =>
              separator ?? const SizedBox(height: 10),
          itemCount: children.length,
        ),
      ),
    );
  }
}
