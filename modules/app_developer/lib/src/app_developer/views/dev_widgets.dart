part of '../app_developer_screen.dart';

class DevWidgets extends StatefulWidget {
  const DevWidgets({Key? key, this.backgroundColor}) : super(key: key);

  final Color? backgroundColor;

  @override
  State<DevWidgets> createState() => _DevWidgetsState();
}

class _DevWidgetsState extends State<DevWidgets> {
  @override
  Widget build(BuildContext context) {
    return ListItemWidget(
      backgroundColor: widget.backgroundColor,
      items: [
        GroupWidget(
          name: 'ButtonGradiantWidget',
          children: [
            const HeaederDevWidget('Stretch width:'),
            ButtonGradiantWidget(
              title: '<PERSON><PERSON><PERSON>hiề<PERSON> thức tỉnh',
              onPressed: () {},
              colors: const [
                AppColors.purple90,
                AppColors.secondary4Color,
              ],
            ),
            const HeaederDevWidget('Min size:'),
            Center(
              child: ButtonGradiantWidget(
                title: '<PERSON><PERSON><PERSON> thức tỉnh',
                onPressed: () {},
                colors: const [
                  AppColors.purple90,
                  AppColors.secondary4Color,
                ],
              ),
            ),
            const HeaederDevWidget('Disable:'),
            const Center(
              child: ButtonGradiantWidget(
                title: '<PERSON><PERSON><PERSON>hiền thức tỉnh',
                onPressed: null,
                colors: [
                  AppColors.purple90,
                  AppColors.secondary4Color,
                ],
              ),
            ),
            const HeaederDevWidget('ButtonGradiantWidget.primary:'),
            Center(
              child: ButtonGradiantWidget.primary(
                title: 'Khoá Thiền thức tỉnh',
                onPressed: () {},
              ),
            ),
            const HeaederDevWidget('ButtonGradiantWidget.secondary01:'),
            const Center(
              child: ButtonGradiantWidget.secondary01(title: 'Mới'),
            ),
            const HeaederDevWidget('ButtonGradiantWidget.secondary02:'),
            Center(
              child: ButtonGradiantWidget.secondary02(
                title: 'Thiền ngay',
                onPressed: () {},
              ),
            ),
            const HeaederDevWidget('ButtonGradiantWidget.icon:'),
            Center(
              child: ButtonGradiantWidget.icon(
                const ImageWidget(IconConstants.lock),
                title: 'Mở khoá và thiền ngay',
                onPressed: () {},
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'ButtonSelectorWidget',
          children: [
            const HeaederDevWidget('Stretch width:'),
            ButtonSelectorWidget(
              labelText: 'Khoá Thiền thức tỉnh',
              iconUrl: IconConstants.bottomBarAlarm,
              onPressed: () {},
              isSelected: true,
            ),
            const HeaederDevWidget('Min size:'),
            Center(
              child: ButtonSelectorWidget(
                labelText: 'Khoá Thiền thức tỉnh',
                iconUrl: IconConstants.bottomBarAlarm,
                onPressed: () {},
              ),
            ),
            const HeaederDevWidget('Disable & isSelected = true:'),
            const Center(
              child: ButtonSelectorWidget(
                labelText: 'Khoá Thiền thức tỉnh',
                iconUrl: IconConstants.bottomBarAlarm,
                isSelected: true,
                onPressed: null,
              ),
            ),
            const HeaederDevWidget('Disable & isSelected = false:'),
            const Center(
              child: ButtonSelectorWidget(
                labelText: 'Khoá Thiền thức tỉnh',
                iconUrl: IconConstants.bottomBarAlarm,
                isSelected: false,
                onPressed: null,
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'MeditationDetailWidget',
          children: [
            MeditationDetailWidget(
              title: 'Khoá Thiền thức tỉnh',
              gradiantPrimaryColor: HexColor('A50E31'),
              gradiantSecondaryColor: HexColor('FF7192'),
            ),
          ],
        ),
        GroupWidget(
          name: 'AppBarCustom',
          children: [AppBarCustom()],
        ),
        GroupWidget(
          name: 'MButtonGradientCircle',
          children: [
            const HeaederDevWidget('Stretch width:'),
            MButtonGradientCircle(
              child: const Text('Button'),
              onTap: () {},
            ),
            const HeaederDevWidget('Min size:'),
            Center(
              child: MButtonGradientCircle(
                child: const Text('Button'),
                onTap: () {},
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'MButtonGradient',
          children: [
            const HeaederDevWidget('Stretch width:'),
            MButtonGradient(
              title: 'Button',
              onTap: () {},
            ),
            const HeaederDevWidget('Min size:'),
            Center(
              child: MButtonGradient(
                title: 'Button',
                onTap: () {},
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'CButton',
          children: [
            const HeaederDevWidget('Stretch width:'),
            CButton(
              'Button',
              onTap: () {},
            ),
            const HeaederDevWidget('Min size, enabled = false:'),
            Center(
              child: CButton(
                'Button',
                enabled: false,
                onTap: () {},
              ),
            ),
            const HeaederDevWidget('CButton.negative:'),
            Center(
              child: CButton.negative(
                'Button',
                enabled: false,
                onTap: () {},
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'MButtonNegative',
          children: [
            const HeaederDevWidget('Stretch width:'),
            MButtonNegative(
              'Button',
              onTap: () {},
            ),
            const HeaederDevWidget('Min size:'),
            Center(
              child: MButtonNegative(
                'Button',
                onTap: () {},
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'MButtonContainer',
          children: [
            const HeaederDevWidget('Stretch width:'),
            MButtonContainer(
              child: const SizedBox(
                height: 30,
                child: ImageWidget('https://share.cleanshot.com/rGbD31BF+'),
              ),
              onTap: () {},
            ),
            const HeaederDevWidget('Min size:'),
            Center(
              child: MButtonContainer(
                child: const SizedBox(
                  height: 30,
                  child: ImageWidget('https://share.cleanshot.com/rGbD31BF+'),
                ),

                // const Text(
                //   'Button',
                //   style: TextStyle(color: Colors.white),
                // ),
                onTap: () {},
              ),
            ),
          ],
        ),
        GroupWidget(
          name: 'MButtonSquare',
          children: [
            const HeaederDevWidget('Stretch width:'),
            MButtonSquare(
              'Button',
              onTap: () {},
            ),
            const HeaederDevWidget('Stretch width:'),
            const HeaederDevWidget('Min size:'),
            Center(
              child: MButtonSquare(
                'Button',
                onTap: () {},
              ),
            ),
          ],
        ),
        const GroupWidget(
          name: 'MDividerVertical & MDivider',
          children: [
            HeaederDevWidget('MDividerVertical:'),
            SizedBox(
              height: 50,
              child: MDividerVertical(
                color: Colors.white,
              ),
            ),
            HeaederDevWidget('MDivider:'),
            SizedBox(
              height: 50,
              child: MDivider(
                color: Colors.white,
              ),
            ),
          ],
        ),
        const GroupWidget(
          name: 'MIcon',
          children: [
            HeaederDevWidget('Stretch width:'),
            MIcon(
              size: 50,
              background: Colors.yellow,
              child: Icon(
                Icons.abc,
                color: Colors.white,
              ),
            ),
            HeaederDevWidget('Min size:'),
            Center(
              child: MIcon(
                size: 50,
                background: Colors.yellow,
                child: Icon(
                  Icons.abc,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
