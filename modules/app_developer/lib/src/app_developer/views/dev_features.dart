part of '../app_developer_screen.dart';

class DevFeatures extends StatefulWidget {
  const DevFeatures({Key? key, this.backgroundColor}) : super(key: key);

  final Color? backgroundColor;
  @override
  State<DevFeatures> createState() => _DevFeaturesState();
}

class _DevFeaturesState extends State<DevFeatures> {
  @override
  Widget build(BuildContext context) {
    return ListItemWidget(
      backgroundColor: widget.backgroundColor,
      items: [
        ElevatedButton(
            onPressed: () =>
                context.startOldDashboardAndRemoveUntil(useRoot: true),
            child: const Text('Start Old Design')),
        ElevatedButton(
            onPressed: () =>
                context.startDashboardAndRemoveUntil(useRoot: true),
            child: const Text('Start New Design')),
      ],
    );
  }
}

class SharedPreferencesClearWidget extends StatefulWidget {
  const SharedPreferencesClearWidget({Key? key}) : super(key: key);

  @override
  State<SharedPreferencesClearWidget> createState() =>
      _SharedPreferencesClearWidgetState();
}

class _SharedPreferencesClearWidgetState
    extends State<SharedPreferencesClearWidget> {
  Map<String, String> getAllPrefs(SharedPreferences prefs) => <String, String>{
        for (final String key in prefs.getKeys()) ...{
          key: prefs.get(key).toString()
        }
      };

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (_, value) {
        if (value.data == null) {
          return const SizedBox();
        }
        final text = getAllPrefs(value.data!).toString();
        return Column(
          children: [
            GestureDetector(
              onTap: () {
                Clipboard.setData(ClipboardData(text: text)).then((value) {
                  ///
                });
              },
              child: Text(text),
            ),
            ElevatedButton(
              onPressed: () {
                value.data?.clear().then((value) => setState);
              },
              child: const Text('Clear SharedPreferences'),
            ),
          ],
        );
      },
    );
  }
}
