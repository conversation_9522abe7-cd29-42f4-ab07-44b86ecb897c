// ignore_for_file: lines_longer_than_80_chars

part of '../app_developer_screen.dart';

class DevScreens extends StatefulWidget {
  const DevScreens({Key? key, this.backgroundColor}) : super(key: key);
  final Color? backgroundColor;

  @override
  State<DevScreens> createState() => _DevScreensState();
}

class _DevScreensState extends State<DevScreens> {
  @override
  Widget build(BuildContext context) {
    return ListItemWidget(
      backgroundColor: widget.backgroundColor,
      items: [
        // ButtonGradiantWidget.primary(
        //   onPressed: () => context.startChooseCardScreen(
        //     onPressed: (card, image) => {
        //       context.startSeeCardContent(card: card, image: image),
        //     },
        //     listCard: [
        //       DailyInspirationData(
        //         content: 'Trăm triệu hạt mưa rơi không hạt nào rơi nhầm chỗ, '
        //             'tất cả những người ta từng gặp không người nào là ngẫu '
        //             'nhiên... Có lẽ đến cùng ai trong chúng ta đều muốn ở bên '
        //             'cạnh một người bình thường',
        //         author: 'Trích Thiền sư Thích Nhất Hạnh',
        //       ),
        //       DailyInspirationData(
        //         content: 'Trăm triệu hạt mưa rơi không hạt nào rơi nhầm chỗ, '
        //             'tất cả những người ta từng gặp không người nào là ngẫu '
        //             'nhiên... Có lẽ đến cùng ai trong chúng ta đều muốn ở bên '
        //             'cạnh một người bình thường',
        //         author: 'Trích Thiền sư Thích Nhất Hạnh',
        //       ),
        //       DailyInspirationData(
        //         content: 'Trăm triệu hạt mưa rơi không hạt nào rơi nhầm chỗ, '
        //             'tất cả những người ta từng gặp không người nào là ngẫu '
        //             'nhiên... Có lẽ đến cùng ai trong chúng ta đều muốn ở bên '
        //             'cạnh một người bình thường',
        //         author: 'Trích Thiền sư Thích Nhất Hạnh',
        //       ),
        //       DailyInspirationData(
        //         content: 'Trăm triệu hạt mưa rơi không hạt nào rơi nhầm chỗ, '
        //             'tất cả những người ta từng gặp không người nào là ngẫu '
        //             'nhiên... Có lẽ đến cùng ai trong chúng ta đều muốn ở bên '
        //             'cạnh một người bình thường',
        //         author: 'Trích Thiền sư Thích Nhất Hạnh',
        //       ),
        //     ],
        //   ),
        //   title: 'Choose Card Screen',
        // ),
        ButtonGradiantWidget.primary(
          onPressed: context.startGetMessages,
          title: 'Screen Get Message',
        ),
        ButtonGradiantWidget.primary(
          onPressed: () => context.startCardDetail(heroTag: '1'),
          title: 'Screen Card Detail',
        ),
      ],
    );
  }
}
