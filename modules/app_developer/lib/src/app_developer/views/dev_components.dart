part of '../app_developer_screen.dart';

class DevComponents extends StatefulWidget {
  const DevComponents({Key? key, this.backgroundColor}) : super(key: key);

  final Color? backgroundColor;
  @override
  State<DevComponents> createState() => _DevComponentsState();
}

class _DevComponentsState extends State<DevComponents> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final categorys =
        kMockDataCategoryMediation.map(CategoryMeditation.fromJson).toList();

    return ListItemWidget(
      backgroundColor: widget.backgroundColor,
      items: [
        const GroupWidget(
          name: 'Skeleton',
          children: [
            Skeleton(
              height: 100,
              width: 100,
              forceTheme: Brightness.dark,
            ),
            Skeleton(
              height: 100,
              width: 100,
              forceTheme: Brightness.light,
            ),
          ],
        ),
        GroupWidget(
          name: 'CardDrawCardWidget (has data)',
          children: [
            CardDrawCardWidget(
              paddingWhenHasData: const EdgeInsets.symmetric(
                horizontal: 25,
                vertical: 25,
              ),
              onPressed: () {},
            )
          ],
        ),
        GroupWidget(
          name: 'CardDrawCardWidget (no data)',
          children: [
            CardDrawCardWidget(
              onPressed: () {},
            )
          ],
        ),
        GroupWidget(
          name: 'SliderIssuesYourCareWidget',
          children: [
            SliderIssuesYourCareWidget(
              autoPlay: true,
              categories: categorys,
              onPressed: (category) {},
            ),
          ],
        ),
        GroupWidget(
          name: 'SharingForYouWidget',
          children: [
            SharingForYouWidget(
              infosShared:
                  kMockDataForYou.map(SharingForYour.fromJson).toList(),
            )
          ],
        ),
        GroupWidget(
          name: 'CardDrawCardWidget',
          children: [
            CardDrawCardWidget(
              onPressed: () {},
            ),
          ],
        ),
        const GroupWidget(
          name: 'CardBreathingExcercisesWidget',
          children: [
            CardBreathingExcercisesWidget(
              timeMinute: 15,
              imageUrl:
                  'https://i.postimg.cc/3w9ysxSg/fe82f66a4d5f84314b0957bcfda353b4.png',
              // description: 'Bắt đầu với các bài tập thở 5s',
            ),
          ],
        ),
        const GroupWidget(
          name: 'CardSharingDayWidget',
          children: [
            HeaederDevWidget('CardSharingDayWidget - Premium:'),
            CardSharingDayWidget(
              isPremium: true,
              timeMinute: 15,
              imageUrl: 'https://i.ibb.co/1dH1Mfv/image.png',
              description: 'Khoá học thiền 7 ngày thức tỉnh mục đích sống',
            ),
            HeaederDevWidget('CardSharingDayWidget - Basic'),
            CardSharingDayWidget(
              isPremium: false,
              timeMinute: 15,
              imageUrl: 'https://i.ibb.co/1dH1Mfv/image.png',
              description: 'Những chia sẽ ngắn trong ngày',
            ),
          ],
        ),
        GroupWidget(
          name: 'CardMediataWidget',
          children: [
            CardMediataWidget(
              title: 'Meditation',
              description: 'Thiền để bớt \ncăng thẳng!',
              highlightText: const ['thiền', 'căng thẳng'],
              imageUrl:
                  'https://i.ibb.co/YkTNpQT/076726be2db6fedfe97386f4b86cbc0e.png',
              onTap: () {},
            ),
          ],
        ),
        GroupWidget(
          name: 'CardUserWidget',
          children: [
            const HeaederDevWidget('Old Design (CardUserWidget.styleOld):'),
            CardUserWidget.styleOld(color: Colors.black),
            const HeaederDevWidget('New Design (CardUserWidget):'),
            const CardUserWidget(color: Colors.black),
          ],
        ),
        GroupWidget(
          name: 'Slider.adaptive',
          children: [
            Slider.adaptive(
              min: 0,
              max: 10,
              value: 2,
              onChanged: (value) {},
            ),
          ],
        ),
        const GroupWidget(
          name: 'EmptyResultWidget',
          children: [
            EmptyResultWidget(),
          ],
        ),
      ],
    );
  }
}
