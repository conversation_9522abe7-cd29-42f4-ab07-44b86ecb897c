import 'package:flutter/material.dart';

import 'app_developer/app_developer_screen.dart';

class AppDeveloperRoutes {
  static Map<String, WidgetBuilder> getAll(RouteSettings settings) => {
        AppDeveloperScreen.routeName: (context) {
          return const AppDeveloperScreen();
        },
      };
}

extension AppDeveloperCoodinator on BuildContext {
  Future startDevMode() async {
    await Navigator.of(this).push(MaterialPageRoute(
      builder: (context) => const AppDeveloperScreen(),
    ));
  }
}
