plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    // END: FlutterFire Configuration
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.inapps.medita"
    compileSdk = flutter.compileSdkVersion
//    ndkVersion = flutter.ndkVersion


    ndkVersion = "27.0.12077973"

    signingConfigs {
        create("prod") {
            storeFile = file("src/key_prod.jks")
            storePassword = "eYukR3k6SHGY"
            keyAlias = "medita"
            keyPassword = "eYukR3k6SHGY"
        }
    }
    compileOptions {
        // Flag to enable support for the new language APIs
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.inapps.medita"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("prod")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Eliminate this line (or the entire dependencies block)
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.20"
//    implementation "androidx.multidex:multidex:2.0.1"
    implementation("com.google.android.material:material:1.12.0")
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.3")
}
