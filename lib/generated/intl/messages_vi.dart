// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  static String m0(showtime) => "Đ<PERSON> đặt lịch hàng ngày cho ${showtime}.";

  static String m1(captcha) => "Nhập ${captcha} để xác nhận:";

  static String m2(link) =>
      "<PERSON><PERSON><PERSON> thân mến,\n\nNhững bài thiền theo liệu trình đặc biệt này đã giúp tôi rất nhiều trong việc tìm thấy sự bình an từ trong tâm, cân bằng trong cuộc sống và làm việc hiệu quả hơn rất nhiều.\n\nTôi muốn lan toả những trải nghiệm này đến với bạn, người mà tôi thực sự quan tâm. Nếu bạn trải nghiệm và thấy sự thay đổi tích cực, hãy lan toả đến những người quen của bạn bằng sự chân thành nhé.\n\n${link}\n\nThân mến,\nVới rất nhiều biết ơn và tình yêu thương...";

  static String m3(productDuration) =>
      "Bạn đã đăng ký thành công gói Tài Trợ ${productDuration}. Thiền Thức Tỉnh cám ơn bạn đã gieo hạt giống tốt lành, biết ơn vũ trụ đã tác hợp duyên lành. Để kiểm tra chi tiết, hãy truy cập phần \"Tài khoản\" trong Tab \"Cài đặt\"";

  static String m4(showtime) => "Lịch hẹn đã tồn tại cho ${showtime}";

  static String m5(time) => "Lời nhắc cho ${time} đã bị xóa!";

  static String m6(second) => "Gửi lại sau ${second} giây";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "accessAllFeature": MessageLookupByLibrary.simpleMessage(
      "Sử dụng được tất cả những tính năng của App",
    ),
    "accessAllLesson": MessageLookupByLibrary.simpleMessage(
      "Truy cập tất cả các bài thiền cao cấp",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Tài khoản"),
    "accountDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Xóa tài khoản sẽ xóa thông tin cá nhân khỏi cơ sở dữ liệu của chúng tôi.",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Hoạt động"),
    "addNewAddress": MessageLookupByLibrary.simpleMessage("Thêm địa chỉ mới"),
    "addressBook": MessageLookupByLibrary.simpleMessage("Sổ địa chỉ"),
    "advisory": MessageLookupByLibrary.simpleMessage("Tư vấn"),
    "agreeTermsDes": MessageLookupByLibrary.simpleMessage(
      "Bằng cách nhấn vào \'Đăng ký ngay\' bạn đồng ý với điều khoản sử dụng của chúng tôi",
    ),
    "anErrorOccurredAndTryAgain": MessageLookupByLibrary.simpleMessage(
      "Đã xảy ra lỗi. Hãy thử lại.",
    ),
    "appleConnectionNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Kết nối Apple không khả dụng cho thiết bị của bạn",
    ),
    "awakeMap": MessageLookupByLibrary.simpleMessage("Bản đồ Thức tỉnh"),
    "bestSaleProduct": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm bán chạy",
    ),
    "breatheOutGently": MessageLookupByLibrary.simpleMessage("thở ra nhẹ nhõm"),
    "cancel": MessageLookupByLibrary.simpleMessage("Đóng"),
    "cart": MessageLookupByLibrary.simpleMessage("Giỏ hàng"),
    "categories": MessageLookupByLibrary.simpleMessage("Hành Thiền Mỗi Ngày"),
    "categoryViewMore": MessageLookupByLibrary.simpleMessage(
      "Xem thêm danh mục",
    ),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Thay đổi ngôn ngữ"),
    "city": MessageLookupByLibrary.simpleMessage("Tỉnh/ thành phố"),
    "close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "comingSoon": MessageLookupByLibrary.simpleMessage("Sắp ra mắt..."),
    "confirmAccountDeletion": MessageLookupByLibrary.simpleMessage(
      "Xác nhận xoá tài khoản",
    ),
    "connectionProblem": MessageLookupByLibrary.simpleMessage(
      "There are some problems with the connection. Please try again",
    ),
    "connectionTimedOut": MessageLookupByLibrary.simpleMessage(
      "The connection has timed out. Please try again",
    ),
    "continueNext": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "dailyReminder": MessageLookupByLibrary.simpleMessage(
      "Lời nhắc hành thiền, hay một việc bất kỳ",
    ),
    "dailyReminderFor": m0,
    "defaultTitle": MessageLookupByLibrary.simpleMessage("Mặc định"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Xoá tài khoản"),
    "deleteAccountMsg": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc rằng bạn muốn xóa tài khoản của mình? Vui lòng xem sự ảnh hưởng nếu xoá tài khoản.",
    ),
    "deleteAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Đã xóa tài khoản thành công. Phiên của bạn đã hết hạn.",
    ),
    "discountPrice": MessageLookupByLibrary.simpleMessage(
      "Giảm 38% khi đăng ký ngay hôm nay",
    ),
    "discountPromo": MessageLookupByLibrary.simpleMessage(
      "(Chưa đến 75k VNĐ/tháng, tương đương 1 bữa ăn/tháng, chưa đến 2.5k VNĐ/ ngày, cho số lượng lớn các bài thiền là \"thức ăn\" tốt lành cho tâm hồn, giúp chữa lành và tiến hóa tâm thức toàn diện)",
    ),
    "dob": MessageLookupByLibrary.simpleMessage("Ngày sinh"),
    "dobInvalid": MessageLookupByLibrary.simpleMessage(
      "Ngày sinh không hợp lệ!",
    ),
    "done": MessageLookupByLibrary.simpleMessage("Hoàn tất"),
    "drawCard": MessageLookupByLibrary.simpleMessage("Rút bài"),
    "edit": MessageLookupByLibrary.simpleMessage("Sửa"),
    "editProfileGuide": MessageLookupByLibrary.simpleMessage(
      "Bạn có thể chỉnh sửa thông tin hồ sơ \nđịa chỉ email, số điện thoại hoặc ngày sinh",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Xóa tài khoản của bạn sẽ xóa bạn khỏi danh sách gửi thư.",
    ),
    "emailInvalid": MessageLookupByLibrary.simpleMessage("Email không hợp lệ!"),
    "emailIsRequire": MessageLookupByLibrary.simpleMessage("Email là bắt buộc"),
    "emailSubscription": MessageLookupByLibrary.simpleMessage("Đăng ký Email"),
    "english": MessageLookupByLibrary.simpleMessage("Tiếng Anh"),
    "enterCaptcha": m1,
    "exit": MessageLookupByLibrary.simpleMessage("Thoát"),
    "exitConfirm": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn thoát ứng dụng không?",
    ),
    "expiresOn": MessageLookupByLibrary.simpleMessage("Hết hạn vào"),
    "failed": MessageLookupByLibrary.simpleMessage("Lỗi"),
    "featuredStories": MessageLookupByLibrary.simpleMessage(
      "Câu Chuyện Thiền Thức Tỉnh",
    ),
    "feedback": MessageLookupByLibrary.simpleMessage("Góp ý"),
    "female": MessageLookupByLibrary.simpleMessage("Nữ"),
    "forgotPass": MessageLookupByLibrary.simpleMessage("Quên mật khẩu"),
    "forgotPassQuestion": MessageLookupByLibrary.simpleMessage(
      "Bạn quên mật khẩu?",
    ),
    "forgotPassSendEmailGuide": MessageLookupByLibrary.simpleMessage(
      "và chúng tôi sẽ gửi cho bạn một email cho phép bạn đặt lại mật khẩu.",
    ),
    "freeTrial7Days": MessageLookupByLibrary.simpleMessage(
      "Được tặng khóa học Thiền Thức Tỉnh trong 7 ngày",
    ),
    "fullName": MessageLookupByLibrary.simpleMessage("Tên đầy đủ"),
    "gallery": MessageLookupByLibrary.simpleMessage("Thư viện"),
    "gender": MessageLookupByLibrary.simpleMessage("Giới tính"),
    "general": MessageLookupByLibrary.simpleMessage("Thông tin chung"),
    "getPass": MessageLookupByLibrary.simpleMessage("Lấy lại mật khẩu"),
    "getStarted": MessageLookupByLibrary.simpleMessage("Bắt đầu"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có tài khoản?",
    ),
    "hello": MessageLookupByLibrary.simpleMessage("Xin chào!"),
    "home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
    "hotlineTitle": MessageLookupByLibrary.simpleMessage("Hotline hỗ trợ: "),
    "idTransaction": MessageLookupByLibrary.simpleMessage("ID Giao dịch"),
    "immutableIfSetOnce": MessageLookupByLibrary.simpleMessage(
      "Bất biến nếu được đặt một lần.",
    ),
    "inputEmail": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ email bên dưới",
    ),
    "inputFullName": MessageLookupByLibrary.simpleMessage(
      "Nhập đầy đủ họ & tên",
    ),
    "inputOTPCodeGuide": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập mã xác nhận được gửi đến số",
    ),
    "inputPassword": MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
    "inputPhoneToGetPass": MessageLookupByLibrary.simpleMessage(
      "Quý khách vui lòng nhập số điện thoại đã đăng ký.",
    ),
    "inputPhoneToLogin": MessageLookupByLibrary.simpleMessage(
      "Nhập số điện thoại đã đăng ký để tiếp tục.",
    ),
    "inputPhoneToRegister": MessageLookupByLibrary.simpleMessage(
      "Quý khách vui lòng nhập số điện thoại để đăng ký tài khoản.",
    ),
    "inputSpecialAddress": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ cụ thể",
    ),
    "internetError": MessageLookupByLibrary.simpleMessage(
      "Xin lỗi, Vui lòng kiểm tra kết nối internet của bạn và thử lại",
    ),
    "invalidCredentials": MessageLookupByLibrary.simpleMessage(
      "Invalid credentials",
    ),
    "inviteGuide": MessageLookupByLibrary.simpleMessage("Giới thiệu bạn bè"),
    "inviteMessage": m2,
    "inviteYourFriend": MessageLookupByLibrary.simpleMessage(
      "Mời Bạn bè của bạn",
    ),
    "inviteYourFriendInThisApp": MessageLookupByLibrary.simpleMessage(
      "Mời bạn bè cùng tham gia trải nghiệm\nThiền thức tinh và nhận được phần thưởng",
    ),
    "joinedDate": MessageLookupByLibrary.simpleMessage("Ngày tham gia"),
    "language": MessageLookupByLibrary.simpleMessage("Ngôn ngữ"),
    "learnMeditationNow": MessageLookupByLibrary.simpleMessage(
      "Học thiền ngay",
    ),
    "loadFail": MessageLookupByLibrary.simpleMessage(
      "Tải thất bại. Xin thử lại!",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
    "logout": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
    "logoutConfirm": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc muốn đăng xuất?",
    ),
    "male": MessageLookupByLibrary.simpleMessage("Nam"),
    "maybeLater": MessageLookupByLibrary.simpleMessage("Để sau"),
    "meditationNow": MessageLookupByLibrary.simpleMessage("Thiền ngay"),
    "meditationReminder": MessageLookupByLibrary.simpleMessage("Hẹn giờ thiền"),
    "message": MessageLookupByLibrary.simpleMessage("Thông điệp gửi tới bạn"),
    "messageEveryday": MessageLookupByLibrary.simpleMessage(
      "Thông điệp mỗi ngày dành cho bạn",
    ),
    "month": MessageLookupByLibrary.simpleMessage("Tháng"),
    "months": MessageLookupByLibrary.simpleMessage("Tháng"),
    "networkSettings": MessageLookupByLibrary.simpleMessage("Cài đặt mạng"),
    "news": MessageLookupByLibrary.simpleMessage("Tin tức & cẩm nang"),
    "no": MessageLookupByLibrary.simpleMessage("Không"),
    "noData": MessageLookupByLibrary.simpleMessage("Không có dữ liệu"),
    "noProductFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy sản phẩm nào!",
    ),
    "noPurchaseFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy giao dịch mua nào",
    ),
    "noThanks": MessageLookupByLibrary.simpleMessage("Không, cảm ơn"),
    "notFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy kết quả phù hợp.\nQuý khách vui lòng thử lại.",
    ),
    "notReceiveOTP": MessageLookupByLibrary.simpleMessage(
      "Không nhận được mã?",
    ),
    "notification": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "ok": MessageLookupByLibrary.simpleMessage("Ok"),
    "onboardDes1": MessageLookupByLibrary.simpleMessage(
      "Thiền giúp bạn cải thiện đáng kể khả năng tập trung, qua đó giúp bạn nâng cao hiệu suất làm việc. Thiền không phải là trở nên lười nhác, mà trái lại, bạn có thể sáng tạo được nhiều giá trị hữu ích hơn.",
    ),
    "onboardDes2": MessageLookupByLibrary.simpleMessage(
      "Nhiều nghiên cứu và thực nghiệm đã chứng minh, thiền có tác dụng hỗ trợ gia tăng sức đề kháng, chăm sóc sức khỏe cả thể chất lẫn tinh thần, hỗ trợ điều trị một số bệnh phổ biến.",
    ),
    "onboardDes3": MessageLookupByLibrary.simpleMessage(
      "Thiền giúp tâm trí trở nên tĩnh lặng, buông bỏ những áp lực không cần thiết, giúp bạn có sự bình an thực sự và hạnh phúc thực sự từ bên trong. Qua đó cũng cải thiện các mối quan hệ theo chiều hướng tích cực hơn.",
    ),
    "onboardHeader1": MessageLookupByLibrary.simpleMessage("Tập trung"),
    "onboardHeader2": MessageLookupByLibrary.simpleMessage("Sức khỏe"),
    "onboardHeader3": MessageLookupByLibrary.simpleMessage("Hạnh phúc"),
    "onboardTitle1": MessageLookupByLibrary.simpleMessage(
      "Nâng cao sự tập trung",
    ),
    "onboardTitle2": MessageLookupByLibrary.simpleMessage("Hỗ trợ sức khỏe"),
    "onboardTitle3": MessageLookupByLibrary.simpleMessage(
      "Hạnh phúc và bình an",
    ),
    "oopsInternetLost": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc! Internet bị lỗi",
    ),
    "orConnectWith": MessageLookupByLibrary.simpleMessage(
      "hoặc đăng nhập bằng",
    ),
    "password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
    "passwordGuide": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu đăng nhập gồm tối thiểu 8 kí tự bao gồm chữ thường, chữ hoa và số.",
    ),
    "passwordInvalid": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu không hợp lệ!",
    ),
    "passwordRequire": MessageLookupByLibrary.simpleMessage(
      "Mật khẩu bắt buộc",
    ),
    "passwordSetup": MessageLookupByLibrary.simpleMessage("Cài đặt mật khẩu"),
    "pastPurchase": MessageLookupByLibrary.simpleMessage(
      "Các giao dịch mua trước đây",
    ),
    "paymentFailed": MessageLookupByLibrary.simpleMessage(
      "Rất tiếc !! đã xảy ra lỗi. Vui lòng thử lại",
    ),
    "paymentSuccessful": m3,
    "phoneExists": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại quý khách vừa nhập đã được đăng ký trên hệ thống.",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Số điện thoại"),
    "phoneNumberFormatInvalid": MessageLookupByLibrary.simpleMessage(
      "Định dạng số điện thoại không đúng",
    ),
    "phoneNumberIsRequire": MessageLookupByLibrary.simpleMessage(
      "Số điện thoại là bắt buộc",
    ),
    "plan": MessageLookupByLibrary.simpleMessage("Gói"),
    "premiumPlanUntil": MessageLookupByLibrary.simpleMessage(
      "Thời hạn gói Premium:",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Chính sách Bảo mật"),
    "product": MessageLookupByLibrary.simpleMessage("Sản phẩm"),
    "productDetail": MessageLookupByLibrary.simpleMessage("Chi tiết sản phẩm"),
    "profile": MessageLookupByLibrary.simpleMessage("Hồ sơ"),
    "profileSetup": MessageLookupByLibrary.simpleMessage("Tạo hồ sơ cá nhân"),
    "profileSetupGuide": MessageLookupByLibrary.simpleMessage(
      "Quý khách vui lòng điền đầy đủ thông tin bên dưới.",
    ),
    "profileUpdated": MessageLookupByLibrary.simpleMessage(
      "Đã cập nhật hồ sơ.",
    ),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage("Kéo để tải thêm"),
    "rate": MessageLookupByLibrary.simpleMessage("Đánh giá"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage("Đánh giá ứng dụng"),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "Bạn thích ứng dụng này chứ? Hãy dành một chút thời gian để đánh giá nhé! ⭐\nĐiều đó giúp chúng tôi rất nhiều và chỉ mất chưa đầy một phút. Cảm ơn bạn!",
    ),
    "reEnterPass": MessageLookupByLibrary.simpleMessage("Nhập lại mật khẩu"),
    "reachYourGoal": MessageLookupByLibrary.simpleMessage(
      "Theo nghiên cứu, những người đặt lời nhắc nhở đạt được mục tiêu nhanh gấp đôi",
    ),
    "region": MessageLookupByLibrary.simpleMessage("Phường/ xã"),
    "register": MessageLookupByLibrary.simpleMessage("Đăng ký tài khoản"),
    "registerGuide": MessageLookupByLibrary.simpleMessage(
      "Hiện chưa có tài khoản nào đăng ký với số điện thoại quý khách vừa nhập.",
    ),
    "registerNow": MessageLookupByLibrary.simpleMessage("Đăng ký ngay"),
    "registerWelcome": MessageLookupByLibrary.simpleMessage(
      "Chào người bạn mới! ",
    ),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Thả để tải thêm",
    ),
    "reminderTimeExist": m4,
    "reminderTimeRemoved": m5,
    "removeAds": MessageLookupByLibrary.simpleMessage("Gỡ quảng cáo"),
    "reportError": MessageLookupByLibrary.simpleMessage("Báo cáo lỗi"),
    "reportErrorMessage": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn báo cáo lỗi này cho quản trị viên?",
    ),
    "resendOTP": MessageLookupByLibrary.simpleMessage("Gửi lại OTP"),
    "resendOTPAfter": m6,
    "restorePurchase": MessageLookupByLibrary.simpleMessage(
      "KHÔI PHỤC MUA HÀNG",
    ),
    "reviewApp": MessageLookupByLibrary.simpleMessage("Đánh giá ứng dụng"),
    "selectOne": MessageLookupByLibrary.simpleMessage("Chọn một"),
    "sendEmailTitle": MessageLookupByLibrary.simpleMessage("Gửi email"),
    "setAddressDefault": MessageLookupByLibrary.simpleMessage(
      "Đặt làm địa chỉ mặc định",
    ),
    "setReminder": MessageLookupByLibrary.simpleMessage("Đặt lời nhắc"),
    "setReminderGuide": MessageLookupByLibrary.simpleMessage(
      "Tiếng chuông thức tỉnh giúp bạn tinh tấn hành thiền.",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Cài đặt"),
    "shopping": MessageLookupByLibrary.simpleMessage("Mua sắm"),
    "skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
    "specialAddress": MessageLookupByLibrary.simpleMessage("Địa chỉ cụ thể"),
    "state": MessageLookupByLibrary.simpleMessage("Quận/ huyện"),
    "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "store": MessageLookupByLibrary.simpleMessage("Cửa hàng"),
    "subcribeFor": MessageLookupByLibrary.simpleMessage("Đăng ký gói Tài Trợ"),
    "subcribeGuide": MessageLookupByLibrary.simpleMessage(
      "Tài trợ để có quyền truy cập nội dung thiền định không giới hạn, nhận được lợi lạc, và giúp dự án phát triển bền vững.",
    ),
    "subcribeProPackage": MessageLookupByLibrary.simpleMessage(
      "Đăng ký Tài Trợ",
    ),
    "subcribedOn": MessageLookupByLibrary.simpleMessage("Đã đăng ký vào"),
    "submit": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "subscribeNow": MessageLookupByLibrary.simpleMessage("ĐĂNG KÝ TÀI TRỢ"),
    "subscription": MessageLookupByLibrary.simpleMessage("Tài trợ"),
    "subscriptionDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Các tài trợ trước đó sẽ bị xoá, vui lòng liên hệ với chúng tôi nếu cần hỗ trợ.",
    ),
    "success": MessageLookupByLibrary.simpleMessage("Thành công!"),
    "successfullyPurchased": MessageLookupByLibrary.simpleMessage(
      "Bạn đã mua thành công",
    ),
    "supportFree": MessageLookupByLibrary.simpleMessage(
      "Được sinh hoạt, cùng tinh tấn tu tập trong cộng đồng Thức Tỉnh Tâm Linh",
    ),
    "takeADeepBreath": MessageLookupByLibrary.simpleMessage("hít vào thật sâu"),
    "termsCondition": MessageLookupByLibrary.simpleMessage(
      "Điều khoản sử dụng",
    ),
    "timeToMeditate": MessageLookupByLibrary.simpleMessage(
      "Đã tới thời gian thiền",
    ),
    "today": MessageLookupByLibrary.simpleMessage("ngày hôm nay"),
    "transferMoney": MessageLookupByLibrary.simpleMessage(
      "Trường hợp chưa thể thanh toán được online, Quý khách có thể chuyển khoản theo thông tin bên dưới:",
    ),
    "transferMoneyInfo": MessageLookupByLibrary.simpleMessage(
      "Ngân hàng TMCP Quân Đội (MB Bank)\nSố tài khoản: 5.66.77.********\nChủ tài khoản: Tạ Minh Tuấn\nNội dung: TTT so_dien_thoai tai tro goi 12 thang",
    ),
    "trialTime": MessageLookupByLibrary.simpleMessage("Hạn dùng thử"),
    "tryAgain": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
    "uploadedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Đã tải lên thành công",
    ),
    "uploading": MessageLookupByLibrary.simpleMessage("Đang tải lên ...."),
    "userInfo": MessageLookupByLibrary.simpleMessage("Thông tin người dùng"),
    "verifyOTP": MessageLookupByLibrary.simpleMessage("Xác minh mã OTP"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("Tiếng Việt"),
    "viewMore": MessageLookupByLibrary.simpleMessage("Xem thêm"),
    "viewedProduct": MessageLookupByLibrary.simpleMessage("Sản phẩm đã xem"),
    "week": MessageLookupByLibrary.simpleMessage("Tuần"),
    "weeks": MessageLookupByLibrary.simpleMessage("Tuần"),
    "welcomeCustomer": MessageLookupByLibrary.simpleMessage("Chào quý khách!"),
    "whatYourPriority": MessageLookupByLibrary.simpleMessage(
      "Các bài thiền được phân chia theo nhóm",
    ),
    "year": MessageLookupByLibrary.simpleMessage("Năm"),
    "yes": MessageLookupByLibrary.simpleMessage("Có"),
    "youDontHaveSubscribeYet": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có bất kỳ tài trợ nào.",
    ),
  };
}
