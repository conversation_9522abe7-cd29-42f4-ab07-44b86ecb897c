// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(showtime) => "Daily reminder set for ${showtime}.";

  static String m1(captcha) => "Enter ${captcha} to confirm:";

  static String m2(link) =>
      "Dear friend,\n\nThese special therapy meditations have helped me a lot to find peace of mind, balance in life and work a lot more efficiently.\n\nI want to spread these experiences to you. Someone I really care about. If you experience and see a positive change, or spread it to your friends with sincerity.\n\n${link}\n\nBest regards,\nWith so much gratitude and love...";

  static String m3(productDuration) =>
      "You\'ve successfully subscribed to our ${productDuration} package. Thiền Thức Tỉnh thank you for sowing the seeds of goodness, thankful for the cosmic cooperation with good conditions. To check your subscription details goto “Account” page under “Settings” tab.";

  static String m4(showtime) => "Reminder Already exist for ${showtime}";

  static String m5(time) => "Reminder for ${time} is removed !";

  static String m6(second) => "Resend after ${second} s";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "accessAllFeature": MessageLookupByLibrary.simpleMessage(
      "Use all the features of the App",
    ),
    "accessAllLesson": MessageLookupByLibrary.simpleMessage(
      "Access all the Advanced Meditations",
    ),
    "account": MessageLookupByLibrary.simpleMessage("Account"),
    "accountDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Deleting your account removes personal information from our database.",
    ),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "addNewAddress": MessageLookupByLibrary.simpleMessage("Thêm địa chỉ mới"),
    "addressBook": MessageLookupByLibrary.simpleMessage("Sổ địa chỉ"),
    "advisory": MessageLookupByLibrary.simpleMessage("Tư vấn"),
    "agreeTermsDes": MessageLookupByLibrary.simpleMessage(
      "By pressing \'Register now\' you agree to our terms & conditions",
    ),
    "anErrorOccurredAndTryAgain": MessageLookupByLibrary.simpleMessage(
      "An error has occurred. Try Again.",
    ),
    "appleConnectionNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Apple connection is not available for your device",
    ),
    "awakeMap": MessageLookupByLibrary.simpleMessage("Awakening map"),
    "bestSaleProduct": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm bán chạy",
    ),
    "breatheOutGently": MessageLookupByLibrary.simpleMessage(
      "breathe out gently",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Close"),
    "cart": MessageLookupByLibrary.simpleMessage("Giỏ hàng"),
    "categories": MessageLookupByLibrary.simpleMessage("Categories"),
    "categoryViewMore": MessageLookupByLibrary.simpleMessage(
      "Xem thêm danh mục",
    ),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Change language"),
    "city": MessageLookupByLibrary.simpleMessage("Tỉnh/ thành phố"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "comingSoon": MessageLookupByLibrary.simpleMessage("Coming soon..."),
    "confirmAccountDeletion": MessageLookupByLibrary.simpleMessage(
      "Confirm deletion of your account",
    ),
    "connectionProblem": MessageLookupByLibrary.simpleMessage(
      "There are some problems with the connection. Please try again",
    ),
    "connectionTimedOut": MessageLookupByLibrary.simpleMessage(
      "The connection has timed out. Please try again",
    ),
    "continueNext": MessageLookupByLibrary.simpleMessage("Continue"),
    "dailyReminder": MessageLookupByLibrary.simpleMessage(
      "Reminders of meditation, or any other thing",
    ),
    "dailyReminderFor": m0,
    "defaultTitle": MessageLookupByLibrary.simpleMessage("Default"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete account"),
    "deleteAccountMsg": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete your account? Please read how account deletion will affect.",
    ),
    "deleteAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Account deleted successfully. Your session has been expired.",
    ),
    "discountPrice": MessageLookupByLibrary.simpleMessage(
      "38% off when you subscribe today",
    ),
    "discountPromo": MessageLookupByLibrary.simpleMessage(
      "(Less than 75k VND/month, equivalent to 1 meal/month, less than 2.5k VND/day, for a large number of meditations that are good \"food\" for the soul, helping to heal and comprehensive evolution of consciousness.)",
    ),
    "dob": MessageLookupByLibrary.simpleMessage("Date of birth"),
    "dobInvalid": MessageLookupByLibrary.simpleMessage(
      "Invalid date of birth!",
    ),
    "done": MessageLookupByLibrary.simpleMessage("Done"),
    "drawCard": MessageLookupByLibrary.simpleMessage("Draw card"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editProfileGuide": MessageLookupByLibrary.simpleMessage(
      "You can edit the information about your\nemail address, phone number, or the birth of date",
    ),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "Deleting your account will unsubscribe you from all mailing lists.",
    ),
    "emailInvalid": MessageLookupByLibrary.simpleMessage(
      "The email is not valid!",
    ),
    "emailIsRequire": MessageLookupByLibrary.simpleMessage("Email is required"),
    "emailSubscription": MessageLookupByLibrary.simpleMessage(
      "Email subscription",
    ),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "enterCaptcha": m1,
    "exit": MessageLookupByLibrary.simpleMessage("Exit"),
    "exitConfirm": MessageLookupByLibrary.simpleMessage(
      "Do you want to exit the app?",
    ),
    "expiresOn": MessageLookupByLibrary.simpleMessage("Expires on"),
    "failed": MessageLookupByLibrary.simpleMessage("Failed"),
    "featuredStories": MessageLookupByLibrary.simpleMessage("Featured stories"),
    "feedback": MessageLookupByLibrary.simpleMessage("Feedback"),
    "female": MessageLookupByLibrary.simpleMessage("Female"),
    "forgotPass": MessageLookupByLibrary.simpleMessage("Forget Password"),
    "forgotPassQuestion": MessageLookupByLibrary.simpleMessage(
      "Forgot your password?",
    ),
    "forgotPassSendEmailGuide": MessageLookupByLibrary.simpleMessage(
      "and we\'ll send you an email allowing you to reset the password.",
    ),
    "freeTrial7Days": MessageLookupByLibrary.simpleMessage(
      "Start with a 1 week free trial.",
    ),
    "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
    "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
    "gender": MessageLookupByLibrary.simpleMessage("Gender"),
    "general": MessageLookupByLibrary.simpleMessage("General"),
    "getPass": MessageLookupByLibrary.simpleMessage("Retrieve the password"),
    "getStarted": MessageLookupByLibrary.simpleMessage("Get Started"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage(
      "You do not have an account?",
    ),
    "hello": MessageLookupByLibrary.simpleMessage("Hello!"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "hotlineTitle": MessageLookupByLibrary.simpleMessage("Hotline: "),
    "idTransaction": MessageLookupByLibrary.simpleMessage("Id Transaction"),
    "immutableIfSetOnce": MessageLookupByLibrary.simpleMessage(
      "Immutable if set once.",
    ),
    "inputEmail": MessageLookupByLibrary.simpleMessage(
      "Enter your email address below.",
    ),
    "inputFullName": MessageLookupByLibrary.simpleMessage(
      "Enter your full name",
    ),
    "inputOTPCodeGuide": MessageLookupByLibrary.simpleMessage(
      "Please enter the confirmation code sent to your phone ",
    ),
    "inputPassword": MessageLookupByLibrary.simpleMessage("Enter password"),
    "inputPhoneToGetPass": MessageLookupByLibrary.simpleMessage(
      "Please enter registered phone number.",
    ),
    "inputPhoneToLogin": MessageLookupByLibrary.simpleMessage(
      "Enter the phone number registered to continue.",
    ),
    "inputPhoneToRegister": MessageLookupByLibrary.simpleMessage(
      "Please enter your phone number to register your account.",
    ),
    "inputSpecialAddress": MessageLookupByLibrary.simpleMessage(
      "Nhập địa chỉ cụ thể",
    ),
    "internetError": MessageLookupByLibrary.simpleMessage(
      "Sorry, Please check your internet connection and then try again",
    ),
    "invalidCredentials": MessageLookupByLibrary.simpleMessage(
      "Invalid credentials",
    ),
    "inviteGuide": MessageLookupByLibrary.simpleMessage("Say to Friends"),
    "inviteMessage": m2,
    "inviteYourFriend": MessageLookupByLibrary.simpleMessage(
      "Invite Your Friends",
    ),
    "inviteYourFriendInThisApp": MessageLookupByLibrary.simpleMessage(
      "Invite your friends to join the Thiền thức tỉnh experience and get rewarded",
    ),
    "joinedDate": MessageLookupByLibrary.simpleMessage("Joined Date"),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "learnMeditationNow": MessageLookupByLibrary.simpleMessage(
      "Learn meditation now",
    ),
    "loadFail": MessageLookupByLibrary.simpleMessage("Load Failed!"),
    "login": MessageLookupByLibrary.simpleMessage("Sign In"),
    "logout": MessageLookupByLibrary.simpleMessage("Log out"),
    "logoutConfirm": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to sign out?",
    ),
    "male": MessageLookupByLibrary.simpleMessage("Male"),
    "maybeLater": MessageLookupByLibrary.simpleMessage("Maybe Later"),
    "meditationNow": MessageLookupByLibrary.simpleMessage("Meditate now"),
    "meditationReminder": MessageLookupByLibrary.simpleMessage(
      "Meditation reminder",
    ),
    "message": MessageLookupByLibrary.simpleMessage("Message sent to you"),
    "messageEveryday": MessageLookupByLibrary.simpleMessage(
      "Daily message for you",
    ),
    "month": MessageLookupByLibrary.simpleMessage("Month"),
    "months": MessageLookupByLibrary.simpleMessage("Months"),
    "networkSettings": MessageLookupByLibrary.simpleMessage("Network settings"),
    "news": MessageLookupByLibrary.simpleMessage("Tin tức & cẩm nang"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noData": MessageLookupByLibrary.simpleMessage("Data empty"),
    "noProductFound": MessageLookupByLibrary.simpleMessage("No product found!"),
    "noPurchaseFound": MessageLookupByLibrary.simpleMessage(
      "No purchase found",
    ),
    "noThanks": MessageLookupByLibrary.simpleMessage("No thanks"),
    "notFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy kết quả phù hợp.\nQuý khách vui lòng thử lại.",
    ),
    "notReceiveOTP": MessageLookupByLibrary.simpleMessage(
      "Didn\'t receive the code?",
    ),
    "notification": MessageLookupByLibrary.simpleMessage("Notification"),
    "ok": MessageLookupByLibrary.simpleMessage("Okay"),
    "onboardDes1": MessageLookupByLibrary.simpleMessage(
      "Meditation significantly improves your ability to focus, thereby helping you to increase your productivity at work. Meditation is not about becoming lazy, but rather you can create more useful values.",
    ),
    "onboardDes2": MessageLookupByLibrary.simpleMessage(
      "Many studies and experiments have proven, meditation can help increase resistance, take care of both physical and mental health, support the treatment of some common diseases.",
    ),
    "onboardDes3": MessageLookupByLibrary.simpleMessage(
      "Meditation helps calm your mind, let go of unnecessary pressures, and gives you real peace and real happiness from within. Thereby also improving relationships in a more positive direction.",
    ),
    "onboardHeader1": MessageLookupByLibrary.simpleMessage("Concentrate"),
    "onboardHeader2": MessageLookupByLibrary.simpleMessage("Health"),
    "onboardHeader3": MessageLookupByLibrary.simpleMessage("Happy"),
    "onboardTitle1": MessageLookupByLibrary.simpleMessage(
      "Increase concentration",
    ),
    "onboardTitle2": MessageLookupByLibrary.simpleMessage("Health support"),
    "onboardTitle3": MessageLookupByLibrary.simpleMessage(
      "Happiness and peace",
    ),
    "oopsInternetLost": MessageLookupByLibrary.simpleMessage(
      "Oops! Internet lost",
    ),
    "orConnectWith": MessageLookupByLibrary.simpleMessage("or login with"),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "passwordGuide": MessageLookupByLibrary.simpleMessage(
      "The login password consists of at least 8 characters including lower case letters, upper case letters and numbers.",
    ),
    "passwordInvalid": MessageLookupByLibrary.simpleMessage(
      "Invalid password!",
    ),
    "passwordRequire": MessageLookupByLibrary.simpleMessage(
      "Password required",
    ),
    "passwordSetup": MessageLookupByLibrary.simpleMessage("Set password"),
    "pastPurchase": MessageLookupByLibrary.simpleMessage("Past Purchases"),
    "paymentFailed": MessageLookupByLibrary.simpleMessage(
      "Oops!! something went wrong. Please try again",
    ),
    "paymentSuccessful": m3,
    "phoneExists": MessageLookupByLibrary.simpleMessage(
      "The phone number you entered is already registered in the system.",
    ),
    "phoneNumber": MessageLookupByLibrary.simpleMessage("Phone number"),
    "phoneNumberFormatInvalid": MessageLookupByLibrary.simpleMessage(
      "Incorrect phone number format",
    ),
    "phoneNumberIsRequire": MessageLookupByLibrary.simpleMessage(
      "Phone number is required",
    ),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "premiumPlanUntil": MessageLookupByLibrary.simpleMessage(
      "Premium Plan until:",
    ),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "product": MessageLookupByLibrary.simpleMessage("Product"),
    "productDetail": MessageLookupByLibrary.simpleMessage("Chi tiết sản phẩm"),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "profileSetup": MessageLookupByLibrary.simpleMessage("Create profile"),
    "profileSetupGuide": MessageLookupByLibrary.simpleMessage(
      "Please fill in the information below.",
    ),
    "profileUpdated": MessageLookupByLibrary.simpleMessage("Profile updated."),
    "pullToLoadMore": MessageLookupByLibrary.simpleMessage("Pull to Load more"),
    "rate": MessageLookupByLibrary.simpleMessage("Rate"),
    "rateTheApp": MessageLookupByLibrary.simpleMessage("Rate the App"),
    "rateThisApp": MessageLookupByLibrary.simpleMessage("Rate this app"),
    "rateThisAppDescription": MessageLookupByLibrary.simpleMessage(
      "If you like this app, please take a moment to review it!\nIt really helps us and shouldn\'t take more than a minute.",
    ),
    "reEnterPass": MessageLookupByLibrary.simpleMessage("Re-enter password"),
    "reachYourGoal": MessageLookupByLibrary.simpleMessage(
      "According to research, people who set reminders hit goals twice as fast",
    ),
    "region": MessageLookupByLibrary.simpleMessage("Phường/ xã"),
    "register": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "registerGuide": MessageLookupByLibrary.simpleMessage(
      "Currently there is no account registered with the phone number you just entered.",
    ),
    "registerNow": MessageLookupByLibrary.simpleMessage("Register now"),
    "registerWelcome": MessageLookupByLibrary.simpleMessage(
      "Hello new friend!",
    ),
    "releaseToLoadMore": MessageLookupByLibrary.simpleMessage(
      "Release to load more",
    ),
    "reminderTimeExist": m4,
    "reminderTimeRemoved": m5,
    "removeAds": MessageLookupByLibrary.simpleMessage("No ads"),
    "reportError": MessageLookupByLibrary.simpleMessage("Error report!"),
    "reportErrorMessage": MessageLookupByLibrary.simpleMessage(
      "Do you want to report this error to the administrator?",
    ),
    "resendOTP": MessageLookupByLibrary.simpleMessage("Resend OTP"),
    "resendOTPAfter": m6,
    "restorePurchase": MessageLookupByLibrary.simpleMessage("RESTORE PURCHASE"),
    "reviewApp": MessageLookupByLibrary.simpleMessage("Review app"),
    "selectOne": MessageLookupByLibrary.simpleMessage("Select one"),
    "sendEmailTitle": MessageLookupByLibrary.simpleMessage("Send email"),
    "setAddressDefault": MessageLookupByLibrary.simpleMessage(
      "Đặt làm địa chỉ mặc định",
    ),
    "setReminder": MessageLookupByLibrary.simpleMessage("Set Reminder"),
    "setReminderGuide": MessageLookupByLibrary.simpleMessage(
      "The awakening bell helps you to diligently meditate.",
    ),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "shopping": MessageLookupByLibrary.simpleMessage("Shopping"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "specialAddress": MessageLookupByLibrary.simpleMessage("Địa chỉ cụ thể"),
    "state": MessageLookupByLibrary.simpleMessage("Quận/ huyện"),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "store": MessageLookupByLibrary.simpleMessage("Store"),
    "subcribeFor": MessageLookupByLibrary.simpleMessage("Subcribe for:"),
    "subcribeGuide": MessageLookupByLibrary.simpleMessage(
      "Subscribe now to get access to unlimited content.",
    ),
    "subcribeProPackage": MessageLookupByLibrary.simpleMessage(
      "Subscribe PREMIUM",
    ),
    "subcribedOn": MessageLookupByLibrary.simpleMessage("Subscribed on"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "subscribeNow": MessageLookupByLibrary.simpleMessage("SUBSCRIBE NOW"),
    "subscription": MessageLookupByLibrary.simpleMessage("Subscriptions"),
    "subscriptionDeleteDescription": MessageLookupByLibrary.simpleMessage(
      "All previous subscriptions will be deleted, please contact us if you need assistance.",
    ),
    "success": MessageLookupByLibrary.simpleMessage("Success"),
    "successfullyPurchased": MessageLookupByLibrary.simpleMessage(
      "You have successfully purchased",
    ),
    "supportFree": MessageLookupByLibrary.simpleMessage(
      "Living, practice in the Thức Tỉnh spiritual community",
    ),
    "takeADeepBreath": MessageLookupByLibrary.simpleMessage(
      "take a deep breath",
    ),
    "termsCondition": MessageLookupByLibrary.simpleMessage(
      "Terms & conditions",
    ),
    "timeToMeditate": MessageLookupByLibrary.simpleMessage("Time to meditate"),
    "today": MessageLookupByLibrary.simpleMessage("today"),
    "transferMoney": MessageLookupByLibrary.simpleMessage(
      "In case you cannot pay online, you can transfer money according to the information below:",
    ),
    "transferMoneyInfo": MessageLookupByLibrary.simpleMessage(
      "Military Commercial Joint Stock Bank (MB Bank)\nAccount number: 5.66.77.********\nAccount holder: Tạ Minh Tuấn\nContent: TTT phone_number subscribe premium 12 months",
    ),
    "trialTime": MessageLookupByLibrary.simpleMessage("Trial period"),
    "tryAgain": MessageLookupByLibrary.simpleMessage("Try again"),
    "update": MessageLookupByLibrary.simpleMessage("Update"),
    "uploadedSuccessfully": MessageLookupByLibrary.simpleMessage(
      " Uploaded successfully",
    ),
    "uploading": MessageLookupByLibrary.simpleMessage(" Uploading...."),
    "userInfo": MessageLookupByLibrary.simpleMessage("User information"),
    "verifyOTP": MessageLookupByLibrary.simpleMessage("Verify OTP"),
    "vietnamese": MessageLookupByLibrary.simpleMessage("Vietnamese"),
    "viewMore": MessageLookupByLibrary.simpleMessage("View more"),
    "viewedProduct": MessageLookupByLibrary.simpleMessage("Sản phẩm đã xem"),
    "week": MessageLookupByLibrary.simpleMessage("Week"),
    "weeks": MessageLookupByLibrary.simpleMessage("Weeks"),
    "welcomeCustomer": MessageLookupByLibrary.simpleMessage("Welcome!"),
    "whatYourPriority": MessageLookupByLibrary.simpleMessage(
      "What is your priority right now?",
    ),
    "year": MessageLookupByLibrary.simpleMessage("Year"),
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "youDontHaveSubscribeYet": MessageLookupByLibrary.simpleMessage(
      "You don\'t have any subscriptions yet.",
    ),
  };
}
