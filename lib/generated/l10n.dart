// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `The connection has timed out. Please try again`
  String get connectionTimedOut {
    return Intl.message(
      'The connection has timed out. Please try again',
      name: 'connectionTimedOut',
      desc: '',
      args: [],
    );
  }

  /// `There are some problems with the connection. Please try again`
  String get connectionProblem {
    return Intl.message(
      'There are some problems with the connection. Please try again',
      name: 'connectionProblem',
      desc: '',
      args: [],
    );
  }

  /// `Invalid credentials`
  String get invalidCredentials {
    return Intl.message(
      'Invalid credentials',
      name: 'invalidCredentials',
      desc: '',
      args: [],
    );
  }

  /// `Pull to Load more`
  String get pullToLoadMore {
    return Intl.message(
      'Pull to Load more',
      name: 'pullToLoadMore',
      desc: '',
      args: [],
    );
  }

  /// `Load Failed!`
  String get loadFail {
    return Intl.message('Load Failed!', name: 'loadFail', desc: '', args: []);
  }

  /// `Release to load more`
  String get releaseToLoadMore {
    return Intl.message(
      'Release to load more',
      name: 'releaseToLoadMore',
      desc: '',
      args: [],
    );
  }

  /// `Data empty`
  String get noData {
    return Intl.message('Data empty', name: 'noData', desc: '', args: []);
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `Vietnamese`
  String get vietnamese {
    return Intl.message('Vietnamese', name: 'vietnamese', desc: '', args: []);
  }

  /// `Okay`
  String get ok {
    return Intl.message('Okay', name: 'ok', desc: '', args: []);
  }

  /// `Close`
  String get cancel {
    return Intl.message('Close', name: 'cancel', desc: '', args: []);
  }

  /// `Notification`
  String get notification {
    return Intl.message(
      'Notification',
      name: 'notification',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Store`
  String get store {
    return Intl.message('Store', name: 'store', desc: '', args: []);
  }

  /// `Account`
  String get account {
    return Intl.message('Account', name: 'account', desc: '', args: []);
  }

  /// `Product`
  String get product {
    return Intl.message('Product', name: 'product', desc: '', args: []);
  }

  /// `Sign In`
  String get login {
    return Intl.message('Sign In', name: 'login', desc: '', args: []);
  }

  /// `Sign Up`
  String get register {
    return Intl.message('Sign Up', name: 'register', desc: '', args: []);
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `Change language`
  String get changeLanguage {
    return Intl.message(
      'Change language',
      name: 'changeLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Enter the phone number registered to continue.`
  String get inputPhoneToLogin {
    return Intl.message(
      'Enter the phone number registered to continue.',
      name: 'inputPhoneToLogin',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get phoneNumber {
    return Intl.message(
      'Phone number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect phone number format`
  String get phoneNumberFormatInvalid {
    return Intl.message(
      'Incorrect phone number format',
      name: 'phoneNumberFormatInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Hello new friend!`
  String get registerWelcome {
    return Intl.message(
      'Hello new friend!',
      name: 'registerWelcome',
      desc: '',
      args: [],
    );
  }

  /// `Currently there is no account registered with the phone number you just entered.`
  String get registerGuide {
    return Intl.message(
      'Currently there is no account registered with the phone number you just entered.',
      name: 'registerGuide',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueNext {
    return Intl.message('Continue', name: 'continueNext', desc: '', args: []);
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `The login password consists of at least 8 characters including lower case letters, upper case letters and numbers.`
  String get passwordGuide {
    return Intl.message(
      'The login password consists of at least 8 characters including lower case letters, upper case letters and numbers.',
      name: 'passwordGuide',
      desc: '',
      args: [],
    );
  }

  /// `Forget Password`
  String get forgotPass {
    return Intl.message(
      'Forget Password',
      name: 'forgotPass',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your phone number to register your account.`
  String get inputPhoneToRegister {
    return Intl.message(
      'Please enter your phone number to register your account.',
      name: 'inputPhoneToRegister',
      desc: '',
      args: [],
    );
  }

  /// `Retrieve the password`
  String get getPass {
    return Intl.message(
      'Retrieve the password',
      name: 'getPass',
      desc: '',
      args: [],
    );
  }

  /// `Please enter registered phone number.`
  String get inputPhoneToGetPass {
    return Intl.message(
      'Please enter registered phone number.',
      name: 'inputPhoneToGetPass',
      desc: '',
      args: [],
    );
  }

  /// `Welcome!`
  String get welcomeCustomer {
    return Intl.message(
      'Welcome!',
      name: 'welcomeCustomer',
      desc: '',
      args: [],
    );
  }

  /// `The phone number you entered is already registered in the system.`
  String get phoneExists {
    return Intl.message(
      'The phone number you entered is already registered in the system.',
      name: 'phoneExists',
      desc: '',
      args: [],
    );
  }

  /// `Verify OTP`
  String get verifyOTP {
    return Intl.message('Verify OTP', name: 'verifyOTP', desc: '', args: []);
  }

  /// `Please enter the confirmation code sent to your phone `
  String get inputOTPCodeGuide {
    return Intl.message(
      'Please enter the confirmation code sent to your phone ',
      name: 'inputOTPCodeGuide',
      desc: '',
      args: [],
    );
  }

  /// `Resend OTP`
  String get resendOTP {
    return Intl.message('Resend OTP', name: 'resendOTP', desc: '', args: []);
  }

  /// `Didn't receive the code?`
  String get notReceiveOTP {
    return Intl.message(
      'Didn\'t receive the code?',
      name: 'notReceiveOTP',
      desc: '',
      args: [],
    );
  }

  /// `Resend after {second} s`
  String resendOTPAfter(Object second) {
    return Intl.message(
      'Resend after $second s',
      name: 'resendOTPAfter',
      desc: '',
      args: [second],
    );
  }

  /// `Set password`
  String get passwordSetup {
    return Intl.message(
      'Set password',
      name: 'passwordSetup',
      desc: '',
      args: [],
    );
  }

  /// `Enter password`
  String get inputPassword {
    return Intl.message(
      'Enter password',
      name: 'inputPassword',
      desc: '',
      args: [],
    );
  }

  /// `Re-enter password`
  String get reEnterPass {
    return Intl.message(
      'Re-enter password',
      name: 'reEnterPass',
      desc: '',
      args: [],
    );
  }

  /// `Invalid password!`
  String get passwordInvalid {
    return Intl.message(
      'Invalid password!',
      name: 'passwordInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Create profile`
  String get profileSetup {
    return Intl.message(
      'Create profile',
      name: 'profileSetup',
      desc: '',
      args: [],
    );
  }

  /// `Please fill in the information below.`
  String get profileSetupGuide {
    return Intl.message(
      'Please fill in the information below.',
      name: 'profileSetupGuide',
      desc: '',
      args: [],
    );
  }

  /// `Full Name`
  String get fullName {
    return Intl.message('Full Name', name: 'fullName', desc: '', args: []);
  }

  /// `Enter your full name`
  String get inputFullName {
    return Intl.message(
      'Enter your full name',
      name: 'inputFullName',
      desc: '',
      args: [],
    );
  }

  /// `Date of birth`
  String get dob {
    return Intl.message('Date of birth', name: 'dob', desc: '', args: []);
  }

  /// `Gender`
  String get gender {
    return Intl.message('Gender', name: 'gender', desc: '', args: []);
  }

  /// `Male`
  String get male {
    return Intl.message('Male', name: 'male', desc: '', args: []);
  }

  /// `Female`
  String get female {
    return Intl.message('Female', name: 'female', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Enter your email address below.`
  String get inputEmail {
    return Intl.message(
      'Enter your email address below.',
      name: 'inputEmail',
      desc: '',
      args: [],
    );
  }

  /// `The email is not valid!`
  String get emailInvalid {
    return Intl.message(
      'The email is not valid!',
      name: 'emailInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Invalid date of birth!`
  String get dobInvalid {
    return Intl.message(
      'Invalid date of birth!',
      name: 'dobInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Shopping`
  String get shopping {
    return Intl.message('Shopping', name: 'shopping', desc: '', args: []);
  }

  /// `Sản phẩm bán chạy`
  String get bestSaleProduct {
    return Intl.message(
      'Sản phẩm bán chạy',
      name: 'bestSaleProduct',
      desc: '',
      args: [],
    );
  }

  /// `View more`
  String get viewMore {
    return Intl.message('View more', name: 'viewMore', desc: '', args: []);
  }

  /// `Sản phẩm đã xem`
  String get viewedProduct {
    return Intl.message(
      'Sản phẩm đã xem',
      name: 'viewedProduct',
      desc: '',
      args: [],
    );
  }

  /// `Tin tức & cẩm nang`
  String get news {
    return Intl.message('Tin tức & cẩm nang', name: 'news', desc: '', args: []);
  }

  /// `Tư vấn`
  String get advisory {
    return Intl.message('Tư vấn', name: 'advisory', desc: '', args: []);
  }

  /// `Không tìm thấy kết quả phù hợp.\nQuý khách vui lòng thử lại.`
  String get notFound {
    return Intl.message(
      'Không tìm thấy kết quả phù hợp.\nQuý khách vui lòng thử lại.',
      name: 'notFound',
      desc: '',
      args: [],
    );
  }

  /// `Xem thêm danh mục`
  String get categoryViewMore {
    return Intl.message(
      'Xem thêm danh mục',
      name: 'categoryViewMore',
      desc: '',
      args: [],
    );
  }

  /// `Chi tiết sản phẩm`
  String get productDetail {
    return Intl.message(
      'Chi tiết sản phẩm',
      name: 'productDetail',
      desc: '',
      args: [],
    );
  }

  /// `Log out`
  String get logout {
    return Intl.message('Log out', name: 'logout', desc: '', args: []);
  }

  /// `Are you sure you want to sign out?`
  String get logoutConfirm {
    return Intl.message(
      'Are you sure you want to sign out?',
      name: 'logoutConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Sổ địa chỉ`
  String get addressBook {
    return Intl.message('Sổ địa chỉ', name: 'addressBook', desc: '', args: []);
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `Thêm địa chỉ mới`
  String get addNewAddress {
    return Intl.message(
      'Thêm địa chỉ mới',
      name: 'addNewAddress',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get update {
    return Intl.message('Update', name: 'update', desc: '', args: []);
  }

  /// `Done`
  String get done {
    return Intl.message('Done', name: 'done', desc: '', args: []);
  }

  /// `Tỉnh/ thành phố`
  String get city {
    return Intl.message('Tỉnh/ thành phố', name: 'city', desc: '', args: []);
  }

  /// `Quận/ huyện`
  String get state {
    return Intl.message('Quận/ huyện', name: 'state', desc: '', args: []);
  }

  /// `Phường/ xã`
  String get region {
    return Intl.message('Phường/ xã', name: 'region', desc: '', args: []);
  }

  /// `Nhập địa chỉ cụ thể`
  String get inputSpecialAddress {
    return Intl.message(
      'Nhập địa chỉ cụ thể',
      name: 'inputSpecialAddress',
      desc: '',
      args: [],
    );
  }

  /// `Địa chỉ cụ thể`
  String get specialAddress {
    return Intl.message(
      'Địa chỉ cụ thể',
      name: 'specialAddress',
      desc: '',
      args: [],
    );
  }

  /// `Đặt làm địa chỉ mặc định`
  String get setAddressDefault {
    return Intl.message(
      'Đặt làm địa chỉ mặc định',
      name: 'setAddressDefault',
      desc: '',
      args: [],
    );
  }

  /// `Default`
  String get defaultTitle {
    return Intl.message('Default', name: 'defaultTitle', desc: '', args: []);
  }

  /// `Giỏ hàng`
  String get cart {
    return Intl.message('Giỏ hàng', name: 'cart', desc: '', args: []);
  }

  /// `Network settings`
  String get networkSettings {
    return Intl.message(
      'Network settings',
      name: 'networkSettings',
      desc: '',
      args: [],
    );
  }

  /// `You've successfully subscribed to our {productDuration} package. Thiền Thức Tỉnh thank you for sowing the seeds of goodness, thankful for the cosmic cooperation with good conditions. To check your subscription details goto “Account” page under “Settings” tab.`
  String paymentSuccessful(Object productDuration) {
    return Intl.message(
      'You\'ve successfully subscribed to our $productDuration package. Thiền Thức Tỉnh thank you for sowing the seeds of goodness, thankful for the cosmic cooperation with good conditions. To check your subscription details goto “Account” page under “Settings” tab.',
      name: 'paymentSuccessful',
      desc: '',
      args: [productDuration],
    );
  }

  /// `Oops!! something went wrong. Please try again`
  String get paymentFailed {
    return Intl.message(
      'Oops!! something went wrong. Please try again',
      name: 'paymentFailed',
      desc: '',
      args: [],
    );
  }

  /// `and we'll send you an email allowing you to reset the password.`
  String get forgotPassSendEmailGuide {
    return Intl.message(
      'and we\'ll send you an email allowing you to reset the password.',
      name: 'forgotPassSendEmailGuide',
      desc: '',
      args: [],
    );
  }

  /// `Email is required`
  String get emailIsRequire {
    return Intl.message(
      'Email is required',
      name: 'emailIsRequire',
      desc: '',
      args: [],
    );
  }

  /// `Send email`
  String get sendEmailTitle {
    return Intl.message(
      'Send email',
      name: 'sendEmailTitle',
      desc: '',
      args: [],
    );
  }

  /// `Password required`
  String get passwordRequire {
    return Intl.message(
      'Password required',
      name: 'passwordRequire',
      desc: '',
      args: [],
    );
  }

  /// `Forgot your password?`
  String get forgotPassQuestion {
    return Intl.message(
      'Forgot your password?',
      name: 'forgotPassQuestion',
      desc: '',
      args: [],
    );
  }

  /// `You do not have an account?`
  String get haveAnAccount {
    return Intl.message(
      'You do not have an account?',
      name: 'haveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Register now`
  String get registerNow {
    return Intl.message(
      'Register now',
      name: 'registerNow',
      desc: '',
      args: [],
    );
  }

  /// `or login with`
  String get orConnectWith {
    return Intl.message(
      'or login with',
      name: 'orConnectWith',
      desc: '',
      args: [],
    );
  }

  /// `An error has occurred. Try Again.`
  String get anErrorOccurredAndTryAgain {
    return Intl.message(
      'An error has occurred. Try Again.',
      name: 'anErrorOccurredAndTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Apple connection is not available for your device`
  String get appleConnectionNotAvailable {
    return Intl.message(
      'Apple connection is not available for your device',
      name: 'appleConnectionNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `By pressing 'Register now' you agree to our terms & conditions`
  String get agreeTermsDes {
    return Intl.message(
      'By pressing \'Register now\' you agree to our terms & conditions',
      name: 'agreeTermsDes',
      desc: '',
      args: [],
    );
  }

  /// `Terms & conditions`
  String get termsCondition {
    return Intl.message(
      'Terms & conditions',
      name: 'termsCondition',
      desc: '',
      args: [],
    );
  }

  /// `Categories`
  String get categories {
    return Intl.message('Categories', name: 'categories', desc: '', args: []);
  }

  /// `What is your priority right now?`
  String get whatYourPriority {
    return Intl.message(
      'What is your priority right now?',
      name: 'whatYourPriority',
      desc: '',
      args: [],
    );
  }

  /// `Featured stories`
  String get featuredStories {
    return Intl.message(
      'Featured stories',
      name: 'featuredStories',
      desc: '',
      args: [],
    );
  }

  /// `Daily reminder set for {showtime}.`
  String dailyReminderFor(Object showtime) {
    return Intl.message(
      'Daily reminder set for $showtime.',
      name: 'dailyReminderFor',
      desc: '',
      args: [showtime],
    );
  }

  /// `Reminder Already exist for {showtime}`
  String reminderTimeExist(Object showtime) {
    return Intl.message(
      'Reminder Already exist for $showtime',
      name: 'reminderTimeExist',
      desc: '',
      args: [showtime],
    );
  }

  /// `Set Reminder`
  String get setReminder {
    return Intl.message(
      'Set Reminder',
      name: 'setReminder',
      desc: '',
      args: [],
    );
  }

  /// `The awakening bell helps you to diligently meditate.`
  String get setReminderGuide {
    return Intl.message(
      'The awakening bell helps you to diligently meditate.',
      name: 'setReminderGuide',
      desc: '',
      args: [],
    );
  }

  /// `Reminder for {time} is removed !`
  String reminderTimeRemoved(Object time) {
    return Intl.message(
      'Reminder for $time is removed !',
      name: 'reminderTimeRemoved',
      desc: '',
      args: [time],
    );
  }

  /// `According to research, people who set reminders hit goals twice as fast`
  String get reachYourGoal {
    return Intl.message(
      'According to research, people who set reminders hit goals twice as fast',
      name: 'reachYourGoal',
      desc: '',
      args: [],
    );
  }

  /// `Reminders of meditation, or any other thing`
  String get dailyReminder {
    return Intl.message(
      'Reminders of meditation, or any other thing',
      name: 'dailyReminder',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `Review app`
  String get reviewApp {
    return Intl.message('Review app', name: 'reviewApp', desc: '', args: []);
  }

  /// `Feedback`
  String get feedback {
    return Intl.message('Feedback', name: 'feedback', desc: '', args: []);
  }

  /// `Say to Friends`
  String get inviteGuide {
    return Intl.message(
      'Say to Friends',
      name: 'inviteGuide',
      desc: '',
      args: [],
    );
  }

  /// `Success`
  String get success {
    return Intl.message('Success', name: 'success', desc: '', args: []);
  }

  /// `Failed`
  String get failed {
    return Intl.message('Failed', name: 'failed', desc: '', args: []);
  }

  /// `Try again`
  String get tryAgain {
    return Intl.message('Try again', name: 'tryAgain', desc: '', args: []);
  }

  /// `Exit`
  String get exit {
    return Intl.message('Exit', name: 'exit', desc: '', args: []);
  }

  /// `Do you want to exit the app?`
  String get exitConfirm {
    return Intl.message(
      'Do you want to exit the app?',
      name: 'exitConfirm',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get no {
    return Intl.message('No', name: 'no', desc: '', args: []);
  }

  /// `Yes`
  String get yes {
    return Intl.message('Yes', name: 'yes', desc: '', args: []);
  }

  /// `Invite Your Friends`
  String get inviteYourFriend {
    return Intl.message(
      'Invite Your Friends',
      name: 'inviteYourFriend',
      desc: '',
      args: [],
    );
  }

  /// `Invite your friends to join the Thiền thức tỉnh experience and get rewarded`
  String get inviteYourFriendInThisApp {
    return Intl.message(
      'Invite your friends to join the Thiền thức tỉnh experience and get rewarded',
      name: 'inviteYourFriendInThisApp',
      desc: '',
      args: [],
    );
  }

  /// `Dear friend,\n\nThese special therapy meditations have helped me a lot to find peace of mind, balance in life and work a lot more efficiently.\n\nI want to spread these experiences to you. Someone I really care about. If you experience and see a positive change, or spread it to your friends with sincerity.\n\n{link}\n\nBest regards,\nWith so much gratitude and love...`
  String inviteMessage(Object link) {
    return Intl.message(
      'Dear friend,\n\nThese special therapy meditations have helped me a lot to find peace of mind, balance in life and work a lot more efficiently.\n\nI want to spread these experiences to you. Someone I really care about. If you experience and see a positive change, or spread it to your friends with sincerity.\n\n$link\n\nBest regards,\nWith so much gratitude and love...',
      name: 'inviteMessage',
      desc: '',
      args: [link],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe PREMIUM`
  String get subcribeProPackage {
    return Intl.message(
      'Subscribe PREMIUM',
      name: 'subcribeProPackage',
      desc: '',
      args: [],
    );
  }

  /// `Access all the Advanced Meditations`
  String get accessAllLesson {
    return Intl.message(
      'Access all the Advanced Meditations',
      name: 'accessAllLesson',
      desc: '',
      args: [],
    );
  }

  /// `Living, practice in the Thức Tỉnh spiritual community`
  String get supportFree {
    return Intl.message(
      'Living, practice in the Thức Tỉnh spiritual community',
      name: 'supportFree',
      desc: '',
      args: [],
    );
  }

  /// `No ads`
  String get removeAds {
    return Intl.message('No ads', name: 'removeAds', desc: '', args: []);
  }

  /// `Start with a 1 week free trial.`
  String get freeTrial7Days {
    return Intl.message(
      'Start with a 1 week free trial.',
      name: 'freeTrial7Days',
      desc: '',
      args: [],
    );
  }

  /// `Subcribe for:`
  String get subcribeFor {
    return Intl.message(
      'Subcribe for:',
      name: 'subcribeFor',
      desc: '',
      args: [],
    );
  }

  /// `No product found!`
  String get noProductFound {
    return Intl.message(
      'No product found!',
      name: 'noProductFound',
      desc: '',
      args: [],
    );
  }

  /// `RESTORE PURCHASE`
  String get restorePurchase {
    return Intl.message(
      'RESTORE PURCHASE',
      name: 'restorePurchase',
      desc: '',
      args: [],
    );
  }

  /// `No purchase found`
  String get noPurchaseFound {
    return Intl.message(
      'No purchase found',
      name: 'noPurchaseFound',
      desc: '',
      args: [],
    );
  }

  /// `Past Purchases`
  String get pastPurchase {
    return Intl.message(
      'Past Purchases',
      name: 'pastPurchase',
      desc: '',
      args: [],
    );
  }

  /// ` Uploading....`
  String get uploading {
    return Intl.message(
      ' Uploading....',
      name: 'uploading',
      desc: '',
      args: [],
    );
  }

  /// ` Uploaded successfully`
  String get uploadedSuccessfully {
    return Intl.message(
      ' Uploaded successfully',
      name: 'uploadedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Profile updated.`
  String get profileUpdated {
    return Intl.message(
      'Profile updated.',
      name: 'profileUpdated',
      desc: '',
      args: [],
    );
  }

  /// `Select one`
  String get selectOne {
    return Intl.message('Select one', name: 'selectOne', desc: '', args: []);
  }

  /// `Gallery`
  String get gallery {
    return Intl.message('Gallery', name: 'gallery', desc: '', args: []);
  }

  /// `User information`
  String get userInfo {
    return Intl.message(
      'User information',
      name: 'userInfo',
      desc: '',
      args: [],
    );
  }

  /// `Immutable if set once.`
  String get immutableIfSetOnce {
    return Intl.message(
      'Immutable if set once.',
      name: 'immutableIfSetOnce',
      desc: '',
      args: [],
    );
  }

  /// `Joined Date`
  String get joinedDate {
    return Intl.message('Joined Date', name: 'joinedDate', desc: '', args: []);
  }

  /// `Trial period`
  String get trialTime {
    return Intl.message('Trial period', name: 'trialTime', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Subscriptions`
  String get subscription {
    return Intl.message(
      'Subscriptions',
      name: 'subscription',
      desc: '',
      args: [],
    );
  }

  /// `Id Transaction`
  String get idTransaction {
    return Intl.message(
      'Id Transaction',
      name: 'idTransaction',
      desc: '',
      args: [],
    );
  }

  /// `Plan`
  String get plan {
    return Intl.message('Plan', name: 'plan', desc: '', args: []);
  }

  /// `Subscribed on`
  String get subcribedOn {
    return Intl.message(
      'Subscribed on',
      name: 'subcribedOn',
      desc: '',
      args: [],
    );
  }

  /// `Expires on`
  String get expiresOn {
    return Intl.message('Expires on', name: 'expiresOn', desc: '', args: []);
  }

  /// `Status`
  String get status {
    return Intl.message('Status', name: 'status', desc: '', args: []);
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `You don't have any subscriptions yet.`
  String get youDontHaveSubscribeYet {
    return Intl.message(
      'You don\'t have any subscriptions yet.',
      name: 'youDontHaveSubscribeYet',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe now to get access to unlimited content.`
  String get subcribeGuide {
    return Intl.message(
      'Subscribe now to get access to unlimited content.',
      name: 'subcribeGuide',
      desc: '',
      args: [],
    );
  }

  /// `SUBSCRIBE NOW`
  String get subscribeNow {
    return Intl.message(
      'SUBSCRIBE NOW',
      name: 'subscribeNow',
      desc: '',
      args: [],
    );
  }

  /// `Oops! Internet lost`
  String get oopsInternetLost {
    return Intl.message(
      'Oops! Internet lost',
      name: 'oopsInternetLost',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, Please check your internet connection and then try again`
  String get internetError {
    return Intl.message(
      'Sorry, Please check your internet connection and then try again',
      name: 'internetError',
      desc: '',
      args: [],
    );
  }

  /// `Concentrate`
  String get onboardHeader1 {
    return Intl.message(
      'Concentrate',
      name: 'onboardHeader1',
      desc: '',
      args: [],
    );
  }

  /// `Increase concentration`
  String get onboardTitle1 {
    return Intl.message(
      'Increase concentration',
      name: 'onboardTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Meditation significantly improves your ability to focus, thereby helping you to increase your productivity at work. Meditation is not about becoming lazy, but rather you can create more useful values.`
  String get onboardDes1 {
    return Intl.message(
      'Meditation significantly improves your ability to focus, thereby helping you to increase your productivity at work. Meditation is not about becoming lazy, but rather you can create more useful values.',
      name: 'onboardDes1',
      desc: '',
      args: [],
    );
  }

  /// `Health`
  String get onboardHeader2 {
    return Intl.message('Health', name: 'onboardHeader2', desc: '', args: []);
  }

  /// `Health support`
  String get onboardTitle2 {
    return Intl.message(
      'Health support',
      name: 'onboardTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Many studies and experiments have proven, meditation can help increase resistance, take care of both physical and mental health, support the treatment of some common diseases.`
  String get onboardDes2 {
    return Intl.message(
      'Many studies and experiments have proven, meditation can help increase resistance, take care of both physical and mental health, support the treatment of some common diseases.',
      name: 'onboardDes2',
      desc: '',
      args: [],
    );
  }

  /// `Happy`
  String get onboardHeader3 {
    return Intl.message('Happy', name: 'onboardHeader3', desc: '', args: []);
  }

  /// `Happiness and peace`
  String get onboardTitle3 {
    return Intl.message(
      'Happiness and peace',
      name: 'onboardTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Meditation helps calm your mind, let go of unnecessary pressures, and gives you real peace and real happiness from within. Thereby also improving relationships in a more positive direction.`
  String get onboardDes3 {
    return Intl.message(
      'Meditation helps calm your mind, let go of unnecessary pressures, and gives you real peace and real happiness from within. Thereby also improving relationships in a more positive direction.',
      name: 'onboardDes3',
      desc: '',
      args: [],
    );
  }

  /// `Get Started`
  String get getStarted {
    return Intl.message('Get Started', name: 'getStarted', desc: '', args: []);
  }

  /// `Language`
  String get language {
    return Intl.message('Language', name: 'language', desc: '', args: []);
  }

  /// `Time to meditate`
  String get timeToMeditate {
    return Intl.message(
      'Time to meditate',
      name: 'timeToMeditate',
      desc: '',
      args: [],
    );
  }

  /// `Month`
  String get month {
    return Intl.message('Month', name: 'month', desc: '', args: []);
  }

  /// `Months`
  String get months {
    return Intl.message('Months', name: 'months', desc: '', args: []);
  }

  /// `Year`
  String get year {
    return Intl.message('Year', name: 'year', desc: '', args: []);
  }

  /// `Week`
  String get week {
    return Intl.message('Week', name: 'week', desc: '', args: []);
  }

  /// `Weeks`
  String get weeks {
    return Intl.message('Weeks', name: 'weeks', desc: '', args: []);
  }

  /// `Use all the features of the App`
  String get accessAllFeature {
    return Intl.message(
      'Use all the features of the App',
      name: 'accessAllFeature',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `You can edit the information about your\nemail address, phone number, or the birth of date`
  String get editProfileGuide {
    return Intl.message(
      'You can edit the information about your\nemail address, phone number, or the birth of date',
      name: 'editProfileGuide',
      desc: '',
      args: [],
    );
  }

  /// `General`
  String get general {
    return Intl.message('General', name: 'general', desc: '', args: []);
  }

  /// `You have successfully purchased`
  String get successfullyPurchased {
    return Intl.message(
      'You have successfully purchased',
      name: 'successfullyPurchased',
      desc: '',
      args: [],
    );
  }

  /// `Learn meditation now`
  String get learnMeditationNow {
    return Intl.message(
      'Learn meditation now',
      name: 'learnMeditationNow',
      desc: '',
      args: [],
    );
  }

  /// `Meditate now`
  String get meditationNow {
    return Intl.message(
      'Meditate now',
      name: 'meditationNow',
      desc: '',
      args: [],
    );
  }

  /// `Meditation reminder`
  String get meditationReminder {
    return Intl.message(
      'Meditation reminder',
      name: 'meditationReminder',
      desc: '',
      args: [],
    );
  }

  /// `Hello!`
  String get hello {
    return Intl.message('Hello!', name: 'hello', desc: '', args: []);
  }

  /// `Premium Plan until:`
  String get premiumPlanUntil {
    return Intl.message(
      'Premium Plan until:',
      name: 'premiumPlanUntil',
      desc: '',
      args: [],
    );
  }

  /// `Hotline: `
  String get hotlineTitle {
    return Intl.message('Hotline: ', name: 'hotlineTitle', desc: '', args: []);
  }

  /// `In case you cannot pay online, you can transfer money according to the information below:`
  String get transferMoney {
    return Intl.message(
      'In case you cannot pay online, you can transfer money according to the information below:',
      name: 'transferMoney',
      desc: '',
      args: [],
    );
  }

  /// `Military Commercial Joint Stock Bank (MB Bank)\nAccount number: 5.66.77.********\nAccount holder: Tạ Minh Tuấn\nContent: TTT phone_number subscribe premium 12 months`
  String get transferMoneyInfo {
    return Intl.message(
      'Military Commercial Joint Stock Bank (MB Bank)\nAccount number: 5.66.77.********\nAccount holder: Tạ Minh Tuấn\nContent: TTT phone_number subscribe premium 12 months',
      name: 'transferMoneyInfo',
      desc: '',
      args: [],
    );
  }

  /// `38% off when you subscribe today`
  String get discountPrice {
    return Intl.message(
      '38% off when you subscribe today',
      name: 'discountPrice',
      desc: '',
      args: [],
    );
  }

  /// `(Less than 75k VND/month, equivalent to 1 meal/month, less than 2.5k VND/day, for a large number of meditations that are good "food" for the soul, helping to heal and comprehensive evolution of consciousness.)`
  String get discountPromo {
    return Intl.message(
      '(Less than 75k VND/month, equivalent to 1 meal/month, less than 2.5k VND/day, for a large number of meditations that are good "food" for the soul, helping to heal and comprehensive evolution of consciousness.)',
      name: 'discountPromo',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon...`
  String get comingSoon {
    return Intl.message(
      'Coming soon...',
      name: 'comingSoon',
      desc: '',
      args: [],
    );
  }

  /// `take a deep breath`
  String get takeADeepBreath {
    return Intl.message(
      'take a deep breath',
      name: 'takeADeepBreath',
      desc: '',
      args: [],
    );
  }

  /// `breathe out gently`
  String get breatheOutGently {
    return Intl.message(
      'breathe out gently',
      name: 'breatheOutGently',
      desc: '',
      args: [],
    );
  }

  /// `Phone number is required`
  String get phoneNumberIsRequire {
    return Intl.message(
      'Phone number is required',
      name: 'phoneNumberIsRequire',
      desc: '',
      args: [],
    );
  }

  /// `Delete account`
  String get deleteAccount {
    return Intl.message(
      'Delete account',
      name: 'deleteAccount',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete your account? Please read how account deletion will affect.`
  String get deleteAccountMsg {
    return Intl.message(
      'Are you sure you want to delete your account? Please read how account deletion will affect.',
      name: 'deleteAccountMsg',
      desc: '',
      args: [],
    );
  }

  /// `Deleting your account removes personal information from our database.`
  String get accountDeleteDescription {
    return Intl.message(
      'Deleting your account removes personal information from our database.',
      name: 'accountDeleteDescription',
      desc: '',
      args: [],
    );
  }

  /// `Email subscription`
  String get emailSubscription {
    return Intl.message(
      'Email subscription',
      name: 'emailSubscription',
      desc: '',
      args: [],
    );
  }

  /// `Deleting your account will unsubscribe you from all mailing lists.`
  String get emailDeleteDescription {
    return Intl.message(
      'Deleting your account will unsubscribe you from all mailing lists.',
      name: 'emailDeleteDescription',
      desc: '',
      args: [],
    );
  }

  /// `All previous subscriptions will be deleted, please contact us if you need assistance.`
  String get subscriptionDeleteDescription {
    return Intl.message(
      'All previous subscriptions will be deleted, please contact us if you need assistance.',
      name: 'subscriptionDeleteDescription',
      desc: '',
      args: [],
    );
  }

  /// `Confirm deletion of your account`
  String get confirmAccountDeletion {
    return Intl.message(
      'Confirm deletion of your account',
      name: 'confirmAccountDeletion',
      desc: '',
      args: [],
    );
  }

  /// `Enter {captcha} to confirm:`
  String enterCaptcha(Object captcha) {
    return Intl.message(
      'Enter $captcha to confirm:',
      name: 'enterCaptcha',
      desc: '',
      args: [captcha],
    );
  }

  /// `Account deleted successfully. Your session has been expired.`
  String get deleteAccountSuccess {
    return Intl.message(
      'Account deleted successfully. Your session has been expired.',
      name: 'deleteAccountSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Awakening map`
  String get awakeMap {
    return Intl.message('Awakening map', name: 'awakeMap', desc: '', args: []);
  }

  /// `Daily message for you`
  String get messageEveryday {
    return Intl.message(
      'Daily message for you',
      name: 'messageEveryday',
      desc: '',
      args: [],
    );
  }

  /// `Draw card`
  String get drawCard {
    return Intl.message('Draw card', name: 'drawCard', desc: '', args: []);
  }

  /// `Error report!`
  String get reportError {
    return Intl.message(
      'Error report!',
      name: 'reportError',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to report this error to the administrator?`
  String get reportErrorMessage {
    return Intl.message(
      'Do you want to report this error to the administrator?',
      name: 'reportErrorMessage',
      desc: '',
      args: [],
    );
  }

  /// `Message sent to you`
  String get message {
    return Intl.message(
      'Message sent to you',
      name: 'message',
      desc: '',
      args: [],
    );
  }

  /// `today`
  String get today {
    return Intl.message('today', name: 'today', desc: '', args: []);
  }

  /// `Rate the App`
  String get rateTheApp {
    return Intl.message('Rate the App', name: 'rateTheApp', desc: '', args: []);
  }

  /// `Rate this app`
  String get rateThisApp {
    return Intl.message(
      'Rate this app',
      name: 'rateThisApp',
      desc: '',
      args: [],
    );
  }

  /// `If you like this app, please take a moment to review it!\nIt really helps us and shouldn't take more than a minute.`
  String get rateThisAppDescription {
    return Intl.message(
      'If you like this app, please take a moment to review it!\nIt really helps us and shouldn\'t take more than a minute.',
      name: 'rateThisAppDescription',
      desc: '',
      args: [],
    );
  }

  /// `Rate`
  String get rate {
    return Intl.message('Rate', name: 'rate', desc: '', args: []);
  }

  /// `No thanks`
  String get noThanks {
    return Intl.message('No thanks', name: 'noThanks', desc: '', args: []);
  }

  /// `Maybe Later`
  String get maybeLater {
    return Intl.message('Maybe Later', name: 'maybeLater', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
