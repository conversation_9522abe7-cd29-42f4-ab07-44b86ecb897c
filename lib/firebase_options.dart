// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBJ73OC7oeUB9CVWk-dd19iJefGxLzgOWg',
    appId: '1:905493402067:web:3df21f4fa4a6b130621f26',
    messagingSenderId: '905493402067',
    projectId: 'medita-fef9a',
    authDomain: 'medita-fef9a.firebaseapp.com',
    storageBucket: 'medita-fef9a.appspot.com',
    measurementId: 'G-SKVY5BJK74',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAd0ekAv8BTxbURFz9N1CaiEt3jAPZBwc8',
    appId: '1:905493402067:android:cdbd27d2ddc86f0a621f26',
    messagingSenderId: '905493402067',
    projectId: 'medita-fef9a',
    storageBucket: 'medita-fef9a.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBGG0lpbP_I66dXNZD2eeFgU1IUxtLxArg',
    appId: '1:905493402067:ios:afdfbad9cb5b3b1b621f26',
    messagingSenderId: '905493402067',
    projectId: 'medita-fef9a',
    storageBucket: 'medita-fef9a.appspot.com',
    androidClientId: '905493402067-dlm85jrurg4vv1d706fnovs6099j42jc.apps.googleusercontent.com',
    iosClientId: '905493402067-1fuclt5esf363dnntt3negu0hdhanetn.apps.googleusercontent.com',
    iosBundleId: 'com.inapps.medita',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBGG0lpbP_I66dXNZD2eeFgU1IUxtLxArg',
    appId: '1:905493402067:ios:a3cb7249ae9fa49c621f26',
    messagingSenderId: '905493402067',
    projectId: 'medita-fef9a',
    storageBucket: 'medita-fef9a.appspot.com',
    androidClientId: '905493402067-dlm85jrurg4vv1d706fnovs6099j42jc.apps.googleusercontent.com',
    iosClientId: '905493402067-9nuloc2shj6h3koqash3k821rqaab479.apps.googleusercontent.com',
    iosBundleId: 'com.inapps.meditaguru',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCgOVYUI7c8ZDAfu66oUr7ksAv-n6JKrMc',
    appId: '1:905493402067:web:494907eed0e7ee3a621f26',
    messagingSenderId: '905493402067',
    projectId: 'medita-fef9a',
    authDomain: 'medita-fef9a.firebaseapp.com',
    storageBucket: 'medita-fef9a.appspot.com',
  );
}
