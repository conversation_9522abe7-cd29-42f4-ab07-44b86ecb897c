import 'package:meditaguru/src/core/lib_common.dart';

import '../../core/utils/string_utils.dart';
import '../entities/category_data.dart';
import '../entities/lesson_data.dart';
import '../repository/category_repository.dart';

class CategoryUsecase {
  final CategoryRepository _categoryRepository;

  CategoryUsecase(this._categoryRepository);

  Future<List> getCategories() {
    return _categoryRepository.getCategories();
  }

  Future<List<CategoryData>> getCategoriesData() {
    return _categoryRepository.getCategoriesData();
  }

  Future<void> handleAudio() async {
    await _categoryRepository.handleAudio();
  }

  Future<LessonData?> getLessonFromCategory({
    String? categoryName,
    String? lessonName,
    required bool isUnLock,
  }) async {
    final rawData = await _categoryRepository.getLessonFromCategory(
      isUnLock: isUnLock,
      categoryName: categoryName,
      lessonName: lessonName,
    );

    final lessonData = rawData?.data;

    if (rawData != null && rawData.isLocked == false && lessonData != null) {
      final imageUrl = lessonData.imageId.mediaLinkFirebase;
      var urlFile = lessonData.fileName.mediaLinkFirebase;
      var isAudio = false;

      if (StringUtils.isEmpty(lessonData.fileName)) {
        urlFile = lessonData.tempObj['urlFile'] ?? '';
        isAudio = lessonData.tempObj['typeFile'] != 1; // 0 : audio - 1: video
      } else {
        isAudio = lessonData.fileName.isAudio;
      }

      return LessonData(
        description: lessonData.tempObj['audioDescription'],
        imageUrl: imageUrl,
        isAudio: isAudio,
        url: urlFile,
        name: lessonData.tempObj['audioName'],
        time: lessonData.tempObj['audioTime'] ?? 0,
      );
    }
    return null;
  }

  Future<CategoryData?> getCategoryByName(String categoryName) {
    return _categoryRepository.getCategoryByName(categoryName);
  }
}
