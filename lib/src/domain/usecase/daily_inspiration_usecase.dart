import 'dart:convert';

import 'package:meditaguru/src/domain/entities/choose_card_info.dart';
import 'package:meditaguru/src/domain/repository/daily_inspiration_repository.dart';

import '../../core/network/shared_preferences_manager.dart';
import '../entities/daily_inspiration_data.dart';

class DailyInspirationUsecase {
  final DailyInspirationRepository _inspirationRepository;

  final SharedPreferencesManager _sharedPreferencesManager;

  DailyInspirationUsecase(
    this._inspirationRepository,
    this._sharedPreferencesManager,
  );

  Future<List<DailyInspirationData>> getListDailyInspiration() {
    return _inspirationRepository.getListDailyInspiration();
  }

  Future<void> saveCardSelected(ChooseCardInfo card) async {
    final data = jsonEncode(card.toJson());
    await _sharedPreferencesManager.putString(keyChooseCard, data);
  }

  ChooseCardInfo? getCard() {
    final data =
        jsonDecode(_sharedPreferencesManager.getString(keyChooseCard) ?? '{}');
    if (data.isNotEmpty) {
      return ChooseCardInfo.fromJson(Map<String, dynamic>.from(data));
    }

    return null;
  }
}
