import 'package:meditaguru/src/core/config.dart';
import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';

import '../../core/utils.dart';
import '../../presentation/shared/navigation/navigation_model.dart';
import '../entities/design_settings.dart';
import '../repository/app_configs_repository.dart';

abstract class AppSettingUseCase {
  String getLanguage();
  Future<bool> setLanguage(String newLanguage);
  Future<ConfigModel?> getConfigDesign(String languageCode);
  Future<DesignSettings?> getDesignSettings();
}

class AppSettingUseCaseImpl implements AppSettingUseCase {
  final SharedPreferencesManager shared;
  final AppConfigsRepository appConfigsRepository;

  AppSettingUseCaseImpl({
    required this.shared,
    required this.appConfigsRepository,
  });

  @override
  String getLanguage() {
    return StringUtils.isEmpty(shared.getString(keyLanguage))
        ? kAdvanceConfigRaw['DefaultLanguage'].toString()
        : shared.getString(keyLanguage) ?? '';
  }

  @override
  Future<bool> setLanguage(String country) async {
    return await shared.putString(keyLanguage, country);
  }

  @override
  Future<ConfigModel?> getConfigDesign(String languageCode) {
    return appConfigsRepository.getConfigDesign(languageCode);
  }

  @override
  Future<DesignSettings?> getDesignSettings() {
    return appConfigsRepository.getDesignSettings();
  }
}
