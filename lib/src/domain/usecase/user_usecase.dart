import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:meditaguru/src/domain/repository/user_repository.dart';

class UserUsecase {
  final UserRepo _userRepo;

  UserUsecase(this._userRepo);

  Future getUserSystem() {
    return _userRepo.getUserSystem();
  }

  Future<QuerySnapshot<Map<String, dynamic>>> getUser(String uid) {
    return _userRepo.getUser(uid);
  }

  Future<DocumentSnapshot<Map<String, dynamic>>> getUserByDocId(String uid) {
    return _userRepo.getUserByDocId(uid);
  }

  Future updateUserInfo({
    required String uid,
    required String phone,
    required String email,
    String? dob,
    String? photoUrl,
  }) {
    return _userRepo.updateUserInfo(
      uid: uid,
      dob: dob,
      phone: phone,
      email: email,
      photoUrl: photoUrl,
    );
  }
}
