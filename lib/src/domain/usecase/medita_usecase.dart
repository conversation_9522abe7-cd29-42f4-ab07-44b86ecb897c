import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/network/api_exception.dart';
import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';
import 'package:meditaguru/src/core/services/device_info_service.dart';
import 'package:meditaguru/src/di/injection/injection.dart';

import '../../core/utils.dart';
import '../entities/global_config.dart';

abstract class MeditaUsecase {
  Future setDataUser(User? currentUser,
      {String? email, String? name, String? phone, DateTime? dob});

  Future setDataUserSignUpWithAccountPass(
      User currentUser, String displayName, String? phoneNumber, DateTime? dob);

  Future<GlobalConfig> getGlobalConfig();

  Future<bool> checkExistSocial(User? currentUser);

  Future<bool> checkExistPhoneNumber(String? phoneNumber,
      {String currentUserId = ''});

  Future<bool> pushLastLoginInfo(User? currentUser);
  Future<bool> updateItemPurchased(
      User? user, PurchaseDetails? purchaseDetails);
  Future<bool> updateItemPurchasedByUser(
      User? user, PurchaseDetails? purchaseDetails);
  Future<void> deleteAccount(User? currentUser);
}

class MeditaUsecaseImpl implements MeditaUsecase {
  @override
  Future<bool> checkExistSocial(User? currentUser) async {
    var result = false;
    if (currentUser?.uid != null) {
      var snapshot = await FirebaseFirestore.instance
          .collection('fl_content')
          .where('_fl_meta_.fl_id', isEqualTo: currentUser?.uid)
          .get();
      result = snapshot.docs.isNotEmpty;
    }
    return result;
  }

  @override
  Future setDataUser(User? currentUser,
      {String? email, String? name, String? phone, DateTime? dob}) async {
    var metaData = {
      'createdBy': "${currentUser?.uid ?? '0L1uQlYHdrdrG0D5CroAeybZsL33'}",
      'createdDate': DateTime.now(),
      'docId': currentUser?.uid,
      'env': 'production',
      'fl_id': currentUser?.uid,
      'locale': 'en-US',
      'schema': 'users',
      'schemaRef': 'fl_schemas/RIGJC2G8tsCBml0270IN',
      'schemaType': 'collection',
    };
    try {
      if (currentUser?.uid != null) {
        var deviceInfo = await DeviceInfoService.getDeviceInfo();
        // SharedPreferences prefs = await SharedPreferences.getInstance();
        var snapshot = await FirebaseFirestore.instance
            .collection('fl_content')
            .where('_fl_meta_.fl_id', isEqualTo: currentUser?.uid)
            .get();

        if (snapshot.docs.isEmpty) {
          var isDeviceExits = (await isDeviceIdExists(deviceInfo.deviceId));
          await FirebaseFirestore.instance
              .collection('fl_content')
              .doc(currentUser?.uid)
              .set({
            '_fl_meta_': metaData,
            'uid': "${currentUser?.uid ?? ''}",
            'email': email ?? currentUser?.email,
            'name': name ?? currentUser?.displayName,
            'photoUrl': currentUser?.photoURL,
            'joiningDate': DateTime.now().toString(),
            'deviceId': deviceInfo.deviceId,
            'deviceName': deviceInfo.deviceName,
            'os': isIos ? 'iOS' : 'Android',
            'phone': phone ?? currentUser?.phoneNumber,
            'dob': dob == null ? null : dob.toString()
            // "bonus" : {
            //   "expiryDate" : (await isDeviceIdExists(deviceInfo.deviceId)) ? '' : DateTime.now().add(new Duration(days: 7)).toString()
            // }
          }, SetOptions(merge: true));
          if (!isDeviceExits) {
            await bonusInvite(currentUser?.uid ?? '');
          }
        }
      }
    } catch (err) {
      throw CustomException(exception: err);
    }
  }

  @override
  Future setDataUserSignUpWithAccountPass(User currentUser, String displayName,
      String? phoneNumber, DateTime? dob) async {
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // var expiryDate;
    var metaData = {
      'createdBy': '${currentUser.uid}',
      'createdDate': DateTime.now(),
      'docId': currentUser.uid,
      'env': 'production',
      'fl_id': currentUser.uid,
      'locale': 'en-US',
      'schema': 'users',
      'schemaRef': 'fl_schemas/RIGJC2G8tsCBml0270IN',
      'schemaType': 'collection',
    };
    try {
      var deviceInfo = await DeviceInfoService.getDeviceInfo();
      var isDeviceExits = (await isDeviceIdExists(deviceInfo.deviceId));
      await FirebaseFirestore.instance
          .collection('fl_content')
          .doc(currentUser.uid)
          .set(
        {
          '_fl_meta_': metaData,
          'uid': '${currentUser.uid}',
          'email': currentUser.email,
          'name': displayName,
          'joiningDate': DateTime.now().toString(),
          'deviceId': deviceInfo.deviceId,
          'deviceName': deviceInfo.deviceName,
          'os': isIos ? 'iOS' : 'Android',
          'phone': phoneNumber,
          'dob': dob == null ? null : dob.toString()
          // "bonus" : {
          //   "expiryDate" : (await isDeviceIdExists(deviceInfo.deviceId)) ? '' : DateTime.now().add(new Duration(days: 7)).toString()
          // }
        },
        SetOptions(merge: true),
      );
      if (!isDeviceExits) {
        await bonusInvite(currentUser.uid);
      }
      // expiryDate = DateTime.now().add(new Duration(days: 7));
      // prefs.setString('expiryDate', expiryDate.toString());
      // prefs.setBool('isTrial', true);
    } catch (err) {
      print('Error: $err');
      rethrow;
    }
  }

  Future bonusInvite(String newUserId) async {
    var userIdInvite =
        injector<SharedPreferencesManager>().getString(keyInviteCode);
    await injector<SharedPreferencesManager>().putString(keyInviteCode, '');

    var snapshot = await FirebaseFirestore.instance
        .collection('fl_content')
        .where('_fl_meta_.fl_id', isEqualTo: userIdInvite)
        .get();

    if (snapshot.docs.isNotEmpty) {
      var deviceInfo = await DeviceInfoService.getDeviceInfo();
      String? strExpiryDate;
      if (snapshot.docs[0].data()['bonus'] != null &&
          StringUtils.isNotEmpty(
              snapshot.docs[0].data()['bonus']['expiryDate'])) {
        strExpiryDate = snapshot.docs[0].data()['bonus']['expiryDate'];

        for (var tran in snapshot.docs[0].data()['bonus']['transactions']) {
          if (deviceInfo.deviceId == tran['deviceId']) {
            return;
          }
        }
      }
      if (strExpiryDate == null) {
        strExpiryDate = DateTime.now().add(const Duration(days: 30)).toString();
      } else {
        var expiryDate = DateTime.parse(strExpiryDate);
        if (expiryDate.isAfter(DateTime.now())) {
          strExpiryDate = expiryDate.add(const Duration(days: 30)).toString();
        } else {
          strExpiryDate =
              DateTime.now().add(const Duration(days: 30)).toString();
        }
      }
      await FirebaseFirestore.instance
          .collection('fl_content')
          .doc(userIdInvite)
          .set(
        {
          // "_fl_meta_": metaData,
          'bonus': {
            'expiryDate': strExpiryDate,
            'transactions': FieldValue.arrayUnion([
              {
                'userId': newUserId,
                'signupDate': DateTime.now().toString(),
                'deviceId': deviceInfo.deviceId
              }
            ]),
          }
        },
        SetOptions(merge: true),
      );
    }
  }

  Future<bool> isDeviceIdExists(String? deviceId) async {
    var snapshot = await FirebaseFirestore.instance
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'users')
        .where('deviceId', isEqualTo: deviceId)
        .get();
    return snapshot.docs.isNotEmpty;
  }

  @override
  Future<GlobalConfig> getGlobalConfig() async {
    var snapshot = await FirebaseFirestore.instance
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'globalConfig')
        .get();
    return GlobalConfig(
      termsAndConditions: snapshot.docs[0].data()['termLink'],
      privacyPolicy: snapshot.docs[0].data()['privacyPolicyLink'],
      bannerLink: snapshot.docs[0].data()['bannerLink'],
      isDisplayBankMethodPayment:
          snapshot.docs[0].data()['isDisplayBankMethodPayment'],
      transferMoneyInfoEn: snapshot.docs[0].data()['transferMoneyInfoEn'],
      transferMoneyInfoVi: snapshot.docs[0].data()['transferMoneyInfoVi'],
      hideFeaturesIos: snapshot.docs[0].data()['hideFeaturesIos'],
      storiesDuration: snapshot.docs[0].data()['storiesDuration'],
      hideFacebookLogin: snapshot.docs[0].data()['hideFacebookLogin'],
    );
  }

  @override
  Future<bool> checkExistPhoneNumber(String? phoneNumber,
      {String currentUserId = ''}) async {
    var result = false;
    if (phoneNumber != null) {
      var originalPhoneNumber = phoneNumber;
      if (originalPhoneNumber.length >= 3 &&
          originalPhoneNumber.substring(0, 3) == '+84') {
        originalPhoneNumber = originalPhoneNumber.replaceFirst('+84', '');
      }
      if (originalPhoneNumber.isNotEmpty &&
          originalPhoneNumber.substring(0, 1) == '0') {
        originalPhoneNumber = originalPhoneNumber.replaceFirst('0', '');
      }
      var startWithZeroPhoneNumber = '0$originalPhoneNumber';
      var startWith84PhoneNumber = '+84$originalPhoneNumber';
      var snapshot = await FirebaseFirestore.instance
          .collection('fl_content')
          .where('_fl_meta_.schema', isEqualTo: 'users')
          // .where('_fl_meta_.schema', isNotEqualTo: currentUserId)
          .where('uid', isNotEqualTo: currentUserId)
          .where('phone', whereIn: [
        phoneNumber,
        originalPhoneNumber,
        startWithZeroPhoneNumber,
        startWith84PhoneNumber,
      ]).get();
      result = snapshot.docs.isNotEmpty;
    }
    return result;
  }

  @override
  Future<bool> pushLastLoginInfo(User? user) async {
    var deviceInfo = await DeviceInfoService.getDeviceInfo();
    await FirebaseFirestore.instance
        .collection('fl_content')
        .doc(user?.uid)
        .set({
      'deviceName': deviceInfo.deviceName,
      'os': isIos ? 'iOS' : 'Android',
      'lastLogin': DateTime.now().toString(),
    }, SetOptions(merge: true));
    return true;
  }

  @override
  Future<bool> updateItemPurchased(
      User? user, PurchaseDetails? purchaseDetails) async {
    await FirebaseFirestore.instance
        .collection('fl_content')
        .doc(user?.uid)
        .set({
      'listReceiptByDevice': {
        'lastUpdate': DateTime.now().toIso8601String(),
        'purchasedItemsByDevice': FieldValue.arrayUnion([
          {
            'originalJson': json.encode({
              'productID': purchaseDetails?.productID ?? '',
              'purchaseID': purchaseDetails?.purchaseID ?? '',
              'transactionDate': DateTime.fromMillisecondsSinceEpoch(
                      int.parse(purchaseDetails?.transactionDate ?? '0'))
                  .toIso8601String(),
              'status': purchaseDetails?.status.toString(),
              'pendingCompletePurchase':
                  purchaseDetails?.pendingCompletePurchase,
              'verificationDataLocal':
                  purchaseDetails?.verificationData.localVerificationData,
              'serverVerificationData':
                  purchaseDetails?.verificationData.serverVerificationData,
              'verificationDataSource':
                  purchaseDetails?.verificationData.source,
              // 'orderId' : purchaseDetails?.billingClientPurchase?.orderId??'',
              // 'packageName' : purchaseDetails?.billingClientPurchase?.packageName??'',
              // 'purchaseTime' : purchaseDetails?.billingClientPurchase?.purchaseTime??0,
              // 'purchaseToken' : purchaseDetails?.billingClientPurchase?.purchaseToken??'',
              // 'purchaseState' : purchaseDetails?.billingClientPurchase?.purchaseState??'',
              // 'localVerificationData' : purchaseDetails?.verificationData.localVerificationData??'',
            }),
          }
        ]),
      },
    }, SetOptions(merge: true));
    return true;
  }

  @override
  Future<bool> updateItemPurchasedByUser(
      User? user, PurchaseDetails? purchaseDetails) async {
    await FirebaseFirestore.instance
        .collection('fl_content')
        .doc(user?.uid)
        .set({
      'listReceiptByUser': {
        'lastUpdate': DateTime.now().toIso8601String(),
        'purchasedItemsByUser': FieldValue.arrayUnion([
          {
            'originalJson': json.encode({
              'productID': purchaseDetails?.productID ?? '',
              'purchaseID': purchaseDetails?.purchaseID ?? '',
              'transactionDate': DateTime.fromMillisecondsSinceEpoch(
                      int.parse(purchaseDetails?.transactionDate ?? '0'))
                  .toIso8601String(),
              'status': purchaseDetails?.status.toString(),
              'pendingCompletePurchase':
                  purchaseDetails?.pendingCompletePurchase,
              'verificationDataLocal':
                  purchaseDetails?.verificationData.localVerificationData,
              'serverVerificationData':
                  purchaseDetails?.verificationData.serverVerificationData,
              'verificationDataSource':
                  purchaseDetails?.verificationData.source,
              // 'orderId' : purchaseDetails?.billingClientPurchase?.orderId??'',
              // 'packageName' : purchaseDetails?.billingClientPurchase?.packageName??'',
              // 'purchaseTime' : purchaseDetails?.billingClientPurchase?.purchaseTime??0,
              // 'purchaseToken' : purchaseDetails?.billingClientPurchase?.purchaseToken??'',
              // 'purchaseState' : purchaseDetails?.billingClientPurchase?.purchaseState??'',
              // 'localVerificationData' : purchaseDetails?.verificationData.localVerificationData??'',
            }),
          }
        ]),
      },
    }, SetOptions(merge: true));
    return true;
  }

  @override
  Future<void> deleteAccount(User? currentUser) async {
    try {
      if (currentUser?.uid != null) {
        var snapshot = await FirebaseFirestore.instance
            .collection('fl_content')
            .where('_fl_meta_.fl_id', isEqualTo: currentUser?.uid)
            .get();

        if (snapshot.docs.isNotEmpty) {
          await FirebaseFirestore.instance
              .collection('fl_content')
              .doc(currentUser?.uid)
              .set(
            {
              'isDelete': true,
            },
            SetOptions(merge: true),
          );
        }
      }
    } catch (err) {
      throw CustomException(exception: err);
    }
  }
}
