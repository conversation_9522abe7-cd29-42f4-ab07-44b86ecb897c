import 'package:meditaguru/src/domain/entities/version_check_config.dart';

import 'in_app_update_for_android_config.dart';

class AdvanceConfig {
  final VersionCheckConfig versionCheck;
  final InAppUpdateForAndroidConfig inAppUpdateForAndroid;

  AdvanceConfig({
    required this.versionCheck,
    required this.inAppUpdateForAndroid,
  });

  factory AdvanceConfig.fromJson(Map<String, dynamic> map) {
    return AdvanceConfig(
      versionCheck:
          VersionCheckConfig.from<PERSON>son(map['versionCheck']),
      inAppUpdateForAndroid: InAppUpdateForAndroidConfig.fromJson(
          map['inAppUpdateForAndroid']),
    );
  }

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'versionCheck': versionCheck.toJson()})
      ..addAll({
        'inAppUpdateForAndroid': inAppUpdateForAndroid.toJson()
      });

    return result;
  }
}
