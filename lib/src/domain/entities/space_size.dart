import 'package:flutter/material.dart';

class SpaceSize {
  final double? top;
  final double? bottom;
  final double? left;
  final double? right;

  SpaceSize({
    this.top,
    this.bottom,
    this.left,
    this.right,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    if (top != null) {
      result.addAll({'top': top});
    }
    if (bottom != null) {
      result.addAll({'bottom': bottom});
    }
    if (left != null) {
      result.addAll({'left': left});
    }
    if (right != null) {
      result.addAll({'right': right});
    }

    return result;
  }

  factory SpaceSize.fromJson(Map<String, dynamic> map) {
    return SpaceSize(
      top: map['top']?.toDouble(),
      bottom: map['bottom']?.toDouble(),
      left: map['left']?.toDouble(),
      right: map['right']?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'SpaceSize(top: $top, bottom: $bottom, left: $left, right: $right)';
  }

  EdgeInsets toEdgeInsets() => EdgeInsets.only(
        top: top ?? 0.0,
        bottom: bottom ?? 0.0,
        left: left ?? 0.0,
        right: right ?? 0.0,
      );
}
