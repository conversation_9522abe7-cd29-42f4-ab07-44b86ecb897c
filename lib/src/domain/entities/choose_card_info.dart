import 'daily_inspiration_data.dart';

class ChooseCardInfo {
  final DailyInspirationData card;
  final DateTime time;

  ChooseCardInfo({required this.card, required this.time});

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'card': card.toJson()})
      ..addAll({'time': time.millisecondsSinceEpoch});

    return result;
  }

  factory ChooseCardInfo.fromJson(Map<String, dynamic> map) {
    return ChooseCardInfo(
      card: DailyInspirationData.fromJson(map['card']),
      time: DateTime.fromMillisecondsSinceEpoch(map['time']),
    );
  }
}
