import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

enum BreathingAction {
  pause,
  start,
  play,
  ;

  BreathingAction get next {
    switch (this) {
      case pause:
        return BreathingAction.play;
      case start:
        return BreathingAction.play;
      default:
        return BreathingAction.pause;
    }
  }

  String get title {
    switch (this) {
      case pause:
        return 'Tiếp tục';
      case start:
        return 'Thực hành';
      default:
        return 'Dừng lại';
    }
  }

  Widget get icon {
    switch (this) {
      case pause:
        return Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(56),
            color: Colors.white,
          ),
          child: const Center(child: Icon(Icons.play_arrow)),
        );
      case start:
        return Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(56),
            gradient: const LinearGradient(
              colors: [
                Color(0xffE4CF13),
                Color(0xffF6EA7E),
              ],
            ),
          ),
          child: const Center(
            child: Icon(
              CupertinoIcons.arrow_right,
              size: 40,
            ),
          ),
        );
      default:
        return Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(56),
            color: Colors.white,
          ),
          child: const Center(child: Icon(Icons.pause)),
        );
    }
  }
}
