class DailyInspirationData {
  final String content;
  final String author;
  final String image;

  DailyInspirationData({
    required this.content,
    required this.author,
    required this.image,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'content': content})
      ..addAll({'image': image})
      ..addAll({'author': author});

    return result;
  }

  factory DailyInspirationData.fromJson(Map<String, dynamic> map) {
    return DailyInspirationData(
      content: map['content'] ?? '',
      image: map['image'] ?? '',
      author: map['author'] ?? '',
    );
  }
}
