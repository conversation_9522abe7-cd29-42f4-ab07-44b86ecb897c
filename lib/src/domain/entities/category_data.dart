class CategoryData {
  final String name;
  final String? time;
  final String? description;
  final String id;
  final int? parentId;
  final String? coverImage;
  final List<AudioData>? audios;
  final bool isPaid;

  CategoryData({
    required this.name,
    this.description,
    required this.id,
    this.parentId,
    this.time,
    this.coverImage,
    this.audios,
    this.isPaid = false,
  });

  factory CategoryData.fromJson(Map<String, dynamic> map) {
    return CategoryData(
      name: map['name'] ?? '',
      time: map['time'],
      description: map['description'],
      id: map['id'] ?? '',
      isPaid: map['isPaid'] ?? false,
      parentId: map['parentId']?.toInt(),
      coverImage: map['coverImage'] ?? map['imageUrl'],
      audios: map['audios'] != null
          ? List<Map<String, dynamic>>.from(map['audios'])
              .map(AudioData.fromJson)
              .toList()
          : null,
    );
  }

  CategoryData copyWith({
    String? name,
    String? description,
    String? id,
    int? parentId,
    bool? isPaid,
    String? coverImage,
    String? time,
    List<AudioData>? audios,
  }) {
    return CategoryData(
      name: name ?? this.name,
      time: time ?? this.time,
      description: description ?? this.description,
      id: id ?? this.id,
      isPaid: isPaid ?? this.isPaid,
      parentId: parentId ?? this.parentId,
      coverImage: coverImage ?? this.coverImage,
      audios: audios ?? this.audios,
    );
  }
}

class AudioData {
  final String audioName;
  final bool isPaid;
  final String? description;
  final String id;
  final String? nameFile;
  final String? nameCoverImage;
  final dynamic files;
  final dynamic coverImage;
  final int? typeFile;
  final String? urlFile;
  final int duration;

  AudioData({
    required this.audioName,
    required this.isPaid,
    required this.id,
    this.description,
    this.nameFile,
    this.nameCoverImage,
    this.files,
    this.coverImage,
    this.typeFile,
    this.urlFile,
    required this.duration,
  });

  bool isPremium(int index) {
    return index != 0 || audioName.toLowerCase().contains('hướng dẫn') == false;
  }

  factory AudioData.fromJson(Map<String, dynamic> map) {
    return AudioData(
      audioName: map['audioName'] ?? '',
      isPaid: map['isPaid'] ?? false,
      description: map['description'],
      id: map['uniqueKey'] ?? '',
      files: map['audioFile'],
      coverImage: map['audioCoverImage'],
      nameCoverImage: map['nameCoverImage'],
      nameFile: map['nameFile'],
      typeFile: map['typeFile'],
      urlFile: map['urlFile'],
      duration: num.tryParse(map['audioTime']?.toString() ?? '0')?.toInt() ?? 0,
    );
  }

  AudioData copyWith({
    String? audioName,
    bool? isPaid,
    String? description,
    String? id,
    dynamic files,
    String? coverImage,
    String? nameCoverImage,
    String? nameFile,
    int? typeFile,
    String? urlFile,
    int? duration,
  }) {
    return AudioData(
      audioName: audioName ?? this.audioName,
      isPaid: isPaid ?? this.isPaid,
      description: description ?? this.description,
      id: id ?? this.id,
      files: files ?? this.files,
      nameCoverImage: nameCoverImage ?? this.nameCoverImage,
      nameFile: nameFile ?? this.nameFile,
      coverImage: coverImage ?? this.coverImage,
      typeFile: typeFile ?? this.typeFile,
      urlFile: urlFile ?? this.urlFile,
      duration: duration ?? this.duration,
    );
  }
}
