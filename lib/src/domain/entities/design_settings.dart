import '../../core/config.dart';

class DesignSettings {
  final String? remoteUrl;
  final bool enableFirestoreConfig;

  DesignSettings({
    required this.remoteUrl,
    required this.enableFirestoreConfig,
  });

  bool get enableRemoteConfig => remoteUrl?.isNotEmpty ?? false;

  DesignSettings.local()
      : remoteUrl = kAppConfig,
        enableFirestoreConfig = false;

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({
        'remoteUrl': (remoteUrl?.startsWith('http') ?? false) ? remoteUrl : null
      })
      ..addAll({'enableFirestoreConfig': enableFirestoreConfig});

    return result;
  }

  factory DesignSettings.fromJson(Map<String, dynamic> map) {
    return DesignSettings(
      remoteUrl: map['remoteUrl'],
      enableFirestoreConfig: map['enableFirestoreConfig'] ?? false,
    );
  }
}
