import 'package:meditaguru/src/domain/entities/space_size.dart';

class DynamicConfigBasic {
  final SpaceSize? margin;
  final SpaceSize? padding;
  final double? percentHeight;

  DynamicConfigBasic({
    this.percentHeight,
    this.margin,
    this.padding,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    if (margin != null) {
      result.addAll({'margin': margin!.toJson()});
    }
    if (padding != null) {
      result.addAll({'padding': padding!.toJson()});
    }
    if (percentHeight != null) {
      result.addAll({'percentHeight': percentHeight});
    }

    return result;
  }

  factory DynamicConfigBasic.fromJson(Map<String, dynamic> map) {
    return DynamicConfigBasic(
      margin: map['margin'] != null ? SpaceSize.fromJson(map['margin']) : null,
      padding:
          map['padding'] != null ? SpaceSize.fromJson(map['padding']) : null,
      percentHeight: num.tryParse(map['percentHeight'].toString())?.toDouble(),
    );
  }
}
