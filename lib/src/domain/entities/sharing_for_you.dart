import 'category_data.dart';
import 'category_meditation.dart';

class SharingForYour {
  final String title;
  final String icon;
  final List<CourseMeditation> courses;

  SharingForYour({
    required this.title,
    required this.icon,
    required this.courses,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'title': title})
      ..addAll({'icon': icon})
      ..addAll({'courses': courses.map((x) => x.toJson()).toList()});

    return result;
  }

  factory SharingForYour.fromJson(Map<String, dynamic> map) {
    return SharingForYour(
      title: map['title'] ?? '',
      icon: map['icon'] ?? '',
      courses: map['courses'] != null
          ? List<Map<String, dynamic>>.from(map['courses'])
              .map(CourseMeditation.fromJson)
              .toList()
          : <CourseMeditation>[],
    );
  }
}

extension SharingForYourExt on SharingForYour {
  List<CourseMeditation> getCourses(List<CategoryData> categories) {
    final listItem = <CourseMeditation>[];
    final ctgIds = categories.map((e) => e.id);
    for (var element in courses) {
      if (element.categoryId != null && ctgIds.contains(element.categoryId)) {
        listItem.add(element);
      }
    }

    return listItem;
  }
}
