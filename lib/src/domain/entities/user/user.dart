import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/domain/entities/user/user_address.dart';

class User {
  String? id;
  bool? loggedIn;
  String? name;
  String? firstName;
  String? lastName;
  String? username;
  String? email;
  String? nicename;
  String? userUrl;
  String? picture;
  String? cookie;
  Shipping? shipping;
  Billing? billing;
  bool? isSocial = false;
  DateTime? dob;
  String? gender;

  User({
    this.id,
    this.loggedIn,
    this.name,
    this.firstName,
    this.lastName,
    this.username,
    this.email,
    this.nicename,
    this.userUrl,
    this.picture,
    this.cookie,
    this.shipping,
    this.billing,
    this.isSocial,
    this.dob,
    this.gender,
  }); // from Magento Json
  User.fromMagentoJson(Map<String, dynamic> json, token) {
    try {
      loggedIn = true;
      id = json['id'];
      name = json['first_name'];
      username = json['phone_number'];
      cookie = token;
      firstName = json['first_name'];
      lastName = json['last_name'];
      email = json['email'];
      picture = '';
      dob = json['dob'];
      gender = json['gender'];
    } catch (e) {
      printLog(e.toString());
    }
  }

  @override
  String toString() => 'User { username: $id $name $email}';

  String getGenderTitle(BuildContext context) {
    switch (gender) {
      case '0':
        return S.of(context).male;
      case '1':
        return S.of(context).female;
      default:
        return S.of(context).gender;
    }
  }
}
