import 'package:flutter/widgets.dart';
import 'package:meditaguru/src/domain/entities/category_data.dart';

import 'space_size.dart';

class CategoryMeditation {
  final String urlBackground;
  final String title;
  final String? description;
  final List<String> highlightKeywords;
  final String shortDescription;
  final String type;
  final String urlImageCard;
  final SpaceSize? padding;
  final SpaceSize? margin;
  final List<CourseMeditation>? courses;

  bool get isEmpty => (courses?.isEmpty ?? true);

  CategoryMeditation({
    required this.urlBackground,
    required this.title,
    required this.description,
    required this.highlightKeywords,
    required this.shortDescription,
    required this.type,
    required this.urlImageCard,
    this.padding,
    this.margin,
    this.courses,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'urlBackground': urlBackground})
      ..addAll({'title': title})
      ..addAll({'highlightKeywords': highlightKeywords})
      ..addAll({'shortDescription': shortDescription})
      ..addAll({'type': type})
      ..addAll({'padding': padding?.toJson()})
      ..addAll({'margin': margin?.toJson()})
      ..addAll({'urlImageCard': urlImageCard})
      ..addAll({'courses': courses?.map((x) => x.toJson()).toList()});

    if (description != null) {
      result.addAll({'description': description});
    }

    return result;
  }

  factory CategoryMeditation.fromJson(Map<String, dynamic> map) {
    return CategoryMeditation(
      urlBackground: map['urlBackground'] ?? '',
      title: map['title'] ?? '',
      type: map['type'] ?? '',
      urlImageCard: map['urlImageCard'] ?? '',
      shortDescription: map['shortDescription'] ?? '',
      highlightKeywords: List<String>.from(map['highlightKeywords'] ?? []),
      description: map['description'],
      padding: map['padding'] != null
          ? SpaceSize.fromJson(Map<String, dynamic>.from(map['padding']))
          : map['padding'],
      margin: map['margin'] != null
          ? SpaceSize.fromJson(Map<String, dynamic>.from(map['margin']))
          : map['margin'],
      courses: map['courses'] != null
          ? List<Map<String, dynamic>>.from(map['courses'])
              .map(CourseMeditation.fromJson)
              .toList()
          : <CourseMeditation>[],
    );
  }
}

class LessonData {
  final String id;
  final String title;
  final String categoryId;

  LessonData({
    required this.id,
    required this.title,
    required this.categoryId,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'id': id})
      ..addAll({'title': title})
      ..addAll({'categoryId': categoryId});

    return result;
  }

  factory LessonData.fromJson(Map<String, dynamic> map) {
    return LessonData(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      categoryId: map['categoryId'] ?? '',
    );
  }
}

class CourseMeditation {
  final String primaryColor;
  final String secondaryColor;
  final String? title;
  final String? time;
  final String? description;
  final String imageCard;
  final String backgroundImage;
  final String primaryColorItem;
  final String secondaryColorItem;
  final String? categoryId;
  final AlignmentGeometry alignment;
  final List<LessonData> lessons;
  final bool? isPaid;

  CourseMeditation({
    required this.primaryColor,
    required this.secondaryColor,
    this.title,
    this.time,
    this.description,
    this.isPaid,
    this.categoryId,
    required this.imageCard,
    required this.backgroundImage,
    required this.primaryColorItem,
    required this.secondaryColorItem,
    required this.lessons,
    this.alignment = Alignment.center,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'primaryColor': primaryColor})
      ..addAll({'secondaryColor': secondaryColor})
      ..addAll({'time': time})
      ..addAll({'description': description})
      ..addAll({'imageCard': imageCard})
      ..addAll({'isPaid': isPaid})
      ..addAll({'backgroundImage': backgroundImage})
      ..addAll({'primaryColorItem': primaryColorItem})
      ..addAll({'secondaryColorItem': secondaryColorItem})
      ..addAll({'lessons': lessons})
      ..addAll({'categoryId': categoryId})
      ..addAll({'title': title});

    return result;
  }

  factory CourseMeditation.fromJson(Map<String, dynamic> map) {
    AlignmentGeometry _toAlignment(String? key) {
      switch (key) {
        case 'topCenter':
          return Alignment.topCenter;
        case 'topLeft':
          return Alignment.topLeft;
        case 'topRight':
          return Alignment.topRight;
        case 'bottomCenter':
          return Alignment.bottomCenter;
        case 'bottomLeft':
          return Alignment.bottomLeft;
        case 'bottomRight':
          return Alignment.bottomRight;
        case 'center':
        default:
          return Alignment.center;
      }
    }

    return CourseMeditation(
      primaryColor: map['primaryColor'] ?? '',
      secondaryColor: map['secondaryColor'] ?? '',
      title: map['title'],
      categoryId: map['categoryId'] ?? '',
      time: map['time'],
      description: map['description'],
      imageCard: map['imageCard'] ?? '',
      isPaid: map['isPaid'],
      backgroundImage: map['backgroundImage'] ?? '',
      alignment: _toAlignment(map['alignBackgroundImage']),
      primaryColorItem: map['primaryColorItem'] ?? '',
      secondaryColorItem: map['secondaryColorItem'] ?? '',
      lessons: map['lessons'] != null
          ? List<Map<String, dynamic>>.from(map['lessons'])
              .map(LessonData.fromJson)
              .toList()
          : <LessonData>[],
    );
  }

  CategoryData? toCategoryData(List<CategoryData> categories) {
    if (lessons.isNotEmpty) {
      final listAudio = <AudioData>[];
      for (var lesson in lessons) {
        final indexCate =
            categories.indexWhere((element) => element.id == lesson.categoryId);
        if (indexCate >= 0) {
          final category = categories[indexCate];
          final indexAudio =
              category.audios?.indexWhere((element) => element.id == lesson.id);

          if (indexAudio != null && indexAudio >= 0) {
            var audio = category.audios![indexAudio];
            if (lesson.title.isNotEmpty) {
              audio = audio.copyWith(audioName: lesson.title);
            }

            listAudio.add(audio);
          }
        }
      }

      if (listAudio.isNotEmpty) {
        return CategoryData(
          name: title!,
          id: UniqueKey().toString(),
          audios: listAudio,
          description: description,
          isPaid: isPaid ?? false,
          coverImage: backgroundImage,
          time: time,
        );
      }
    }

    return null;
  }
}
