import '../../data/models/lesson_rawdata.dart';
import '../entities/category_data.dart';

abstract class CategoryRepository {
  Future<List> getCategories();
  Future<List<CategoryData>> getCategoriesData();
  Future<LessonRawDataModel?> getLessonFromCategory({
    String? categoryName,
    String? lessonName,
    required bool isUnLock,
  });
  Future<CategoryData?> getCategoryByName(String categoryName);
  Future<void> handleAudio();
}
