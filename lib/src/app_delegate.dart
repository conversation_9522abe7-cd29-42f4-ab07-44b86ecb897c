import 'dart:async';
import 'dart:io';

import 'package:audio_service/audio_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:meditaguru/src/app.dart';
import 'package:meditaguru/src/core/base/styles/style.dart';
import 'package:meditaguru/src/core/config/build_config.dart';
import 'package:meditaguru/src/core/configurations/configurations.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/network/http_overrides.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_crashlytics_wapper.dart';
import 'package:meditaguru/src/core/services/firebase/firebase_core_service.dart';
import 'package:meditaguru/src/core/utils/logs.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/song/audio_service_2/audio_player_handler.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'core/services/device_info_service.dart';
import 'core/services/tracking_log_service.dart';
import 'presentation/shared/account/account_bloc.dart';
import 'presentation/shared/app_bloc/loading_bloc.dart';
import 'presentation/shared/categories/categories_model.dart';
import 'presentation/shared/daily_inspiration/daily_inspiration_model.dart';
import 'presentation/shared/navigation/navigation_bloc.dart';
import 'presentation/song/download_audio/download_audio_model.dart';

AudioPlayerHandler? audioHandler;

// Ref: https://github.com/firebase/flutterfire/blob/master/packages/firebase_messaging/firebase_messaging/example/lib/main.dart#L46
// Docs: https://firebase.google.com/docs/cloud-messaging/flutter/receive#apple_platforms_and_android

/// When using Flutter version 3.3.0 or higher, the message handler must be
/// annotated with @pragma('vm:entry-point') right above the function
/// declaration (otherwise it may be removed during tree shaking for release
/// mode).
// Background messages
// It must be a top-level function (e.g. not a class method which requires initialization). before runApp
@pragma('vm:entry-point')
Future<void> onBackgroundMessageHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  // await FirebaseCoreService.init();
}

class AppDelegate {
  static void run(Map<String, dynamic> env) {
    runZonedGuarded(() async {
      WidgetsFlutterBinding.ensureInitialized();
      Configurations().setConfigurationValues(env);
      await Injection.inject();
      final downloadAudioModel = DownloadAudioModel(injector.get());
      final trackingLogService = injector.get<TrackingLogService>();

      if (audioHandler == null) {
        try {
          audioHandler = await AudioService.init(
            builder: () => AudioPlayerHandler(
              downloadAudioModel,
              trackingLogService,
            ),

            config: const AudioServiceConfig(
              androidNotificationChannelId: 'com.inapps.medita.audio',
              androidNotificationChannelName: 'Medita Audio Playback',
              androidNotificationOngoing: true,
              notificationColor: Color(0xFF447b91),
              androidNotificationIcon: 'drawable/app_icon',
            ),
            cacheManager: CacheManager(
              Config(
                'medita_audio_cache',
                maxNrOfCacheObjects: 100,
                stalePeriod: const Duration(days: 14),
                fileSystem: IOFileSystem('medita_audio_cache'),
                fileService: HttpFileService(),
              ),
            ),

            // cacheManager:
          );
        } catch (e) {
          print(e.toString());
        }
      }
      BuildConfig.init2(flavor: Flavor.DEVELOPMENT);

      try {
        // firebase core
        await FirebaseCoreService.init();
        // background FCM:
        FirebaseMessaging.onBackgroundMessage(onBackgroundMessageHandler);
        // setup crashlytics
        await FirebaseCrashlyticsService.init(DeviceInfoService.deviceInfo);
        await FirebaseAnalyticsWapper().init();
        await Injection.prepareData();
        HttpOverrides.global = MyHttpOverrides();
        Style.styleDefault();
      } catch (e, t) {
        printError('[App] Error: $e');
        printError('[App] StackTrace: $t');
        unawaited(
          FirebaseAnalyticsWapper().analytics.logEvent(
            name: 'Bugs',
            parameters: {'name': e.toString()},
          ),
        );
      }

      final app = await _onCreate(
        providers: [
          Provider<LoadingBloc>(
            create: (_) => injector<LoadingBloc>(),
            dispose: (_, bloc) => bloc.dispose(),
          ),
          Provider<NavigationBloc>(
            create: (_) => injector<NavigationBloc>(),
            dispose: (_, bloc) => bloc.dispose(),
          ),
          ChangeNotifierProvider<CategoriesModel>(
            create: (_) => injector<CategoriesModel>(),
          ),
          ChangeNotifierProvider<DailyInspirationModel>(
            create: (_) => injector<DailyInspirationModel>(),
          ),
          Provider<AccountBloc>(
            create: (_) => injector<AccountBloc>(),
            dispose: (_, _bloc) => _bloc.dispose(),
          ),
          ChangeNotifierProvider<DownloadAudioModel>(
            create: (_) => downloadAudioModel,
          ),
        ],
      );

      trackingLogService.sendCustomLog({'message': 'App started'});

      runApp(app);
    }, printError);
  }

  static FutureOr<StatefulWidget> _onCreate(
      {required List<SingleChildWidget> providers}) async {
    ErrorWidget.builder = (FlutterErrorDetails details) {
      Zone.current.handleUncaughtError(details.exception, details.stack!);
      return Container(color: Colors.transparent);
    };

    return Application(providers: providers);
  }
}
