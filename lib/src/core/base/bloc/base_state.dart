import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/life_cycle/life_cycle.dart';
import 'package:meditaguru/src/core/network/api_code.dart';
import 'package:meditaguru/src/core/widgets/dialog/dialog_widgets.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/handle_ui_bloc.dart';

/// Provides common utilities and functions to build ui and handle app lifecycle
abstract class BaseState<T extends StatefulWidget> extends LifeCycleState<T> {
  /// Called when the app is temporarily closed or a new route is pushed
  @override
  void onPause() {}

  /// Called when users return to the app or the adjacent route of this widget is popped
  @override
  void onResume() {}

  /// Called onnce when this state's widget finished building
  @override
  void onFirstFrame() {}

  @override
  void onDetach() {}

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }
}

abstract class BaseStateScreen<T extends StatefulWidget>
    extends LifeCycleState<T> {
  HandleUIBloc handleUIBloc = HandleUIBloc();

  @override
  void initState() {
    super.initState();
    handleUIBloc.uiHandleController.listen((value) {
      if (value.isError) {
        if (value.customException != null &&
            value.customException?.errorCode == 401) {
          DialogManager.showConfirmDialog(
              context: AppBloc.navigatorKey.currentContext ?? context,
              message: value.message,
              onAccept: () async {
                context.startLogin();
              },
              titleAccept:
                  S.of(AppBloc.navigatorKey.currentContext ?? context).login);
        } else if (value.customException != null &&
            value.customException?.errorCode == NET_WORK) {
          handleNetworkSettings(message: value.message);
        } else {
          DialogManager.showFailureDialog(
              context: AppBloc.navigatorKey.currentContext ?? context,
              message: value.message);
        }
      } else {
        DialogManager.showSuccessDialog(
            context: AppBloc.navigatorKey.currentContext ?? context,
            message: value.message);
      }
    });
  }

  void handleNetworkSettings({String? message}) {
    DialogManager.showConfirmDialog(
      context: AppBloc.navigatorKey.currentContext ?? context,
      message: message ??
          S
              .of(AppBloc.navigatorKey.currentContext ?? context)
              .connectionProblem,
      onAccept: () async {
        await AppSettings.openAppSettings(type: AppSettingsType.wifi);
      },
      titleAccept:
          S.of(AppBloc.navigatorKey.currentContext ?? context).networkSettings,
      titleCancel: S.of(AppBloc.navigatorKey.currentContext ?? context).close,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Called when the app is temporarily closed or a new route is pushed
  @override
  void onPause() {}

  /// Called when users return to the app or the adjacent route of this widget is popped
  @override
  void onResume() {}

  /// Called onnce when this state's widget finished building
  @override
  void onFirstFrame() {}

  @override
  void didPop() {}

  @override
  void didPush() {}

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }
}
