import 'package:flutter/material.dart';

import 'colors.dart';
import 'fonts.dart';

IconThemeData _customIconTheme(IconThemeData original) {
  return original.copyWith(color: AppColors.contentDarkGreyColor);
}

ThemeData buildLightTheme(String language) {
  final base = ThemeData.light();
  return base.copyWith(
    cardColor: Colors.white,
    buttonTheme: const ButtonThemeData(
        colorScheme: kColorScheme,
        textTheme: ButtonTextTheme.normal,
        buttonColor: AppColors.kDarkBG),
    primaryColorLight: AppColors.kLightBG,
    primaryIconTheme: _customIconTheme(base.iconTheme),
    textTheme: _buildTextTheme(base.textTheme, language),
    primaryTextTheme: _buildTextTheme(base.primaryTextTheme, language),
    iconTheme: _customIconTheme(base.iconTheme),
    hintColor: Colors.black26,
    primaryColor: AppColors.kLightPrimary,
    // cursorColor: AppColors.kLightAccent,
    scaffoldBackgroundColor: AppColors.kLightBG,
    appBarTheme: AppBarTheme(
      elevation: 0,
      iconTheme: const IconThemeData(
        color: AppColors.kLightAccent,
      ),
      toolbarTextStyle: const TextTheme(
        titleLarge: TextStyle(
          color: AppColors.kDarkBG,
          fontSize: AppFontSize.normalFontSize,
          fontWeight: FontWeight.w800,
        ),
      ).bodyMedium,
      titleTextStyle: const TextTheme(
        titleLarge: TextStyle(
          color: AppColors.kDarkBG,
          fontSize: AppFontSize.normalFontSize,
          fontWeight: FontWeight.w800,
        ),
      ).titleLarge,
    ),
    pageTransitionsTheme: const PageTransitionsTheme(builders: {
      TargetPlatform.iOS: FadeUpwardsPageTransitionsBuilder(),
      TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
    }),
    tabBarTheme: const TabBarTheme(
      labelColor: Colors.black,
      unselectedLabelColor: Colors.black,
      labelPadding: EdgeInsets.zero,
      labelStyle: TextStyle(fontSize: AppFontSize.xSmallFontSize),
      unselectedLabelStyle: TextStyle(fontSize: AppFontSize.xSmallFontSize),
    ),
    colorScheme: kColorScheme
        .copyWith(surface: AppColors.backgroundColor)
        .copyWith(error: AppColors.errorRedColor)
        .copyWith(secondary: AppColors.kLightAccent),
  );
}

TextTheme _buildTextTheme(TextTheme base, String language) {
  return kTextTheme(base, language)
      .copyWith(
        headlineSmall: base.headlineSmall!
            .copyWith(fontWeight: FontWeight.w500, color: Colors.red),
        titleLarge:
            base.titleLarge?.copyWith(fontSize: AppFontSize.largeFontSize),
        bodySmall: base.bodySmall?.copyWith(
          fontWeight: FontWeight.w400,
          fontSize: AppFontSize.smallFontSize,
        ),
        titleMedium: base.titleMedium?.copyWith(
          fontWeight: FontWeight.w400,
          fontSize: AppFontSize.titleFontSize,
        ),
        labelLarge: base.labelLarge?.copyWith(
          fontWeight: FontWeight.w400,
          fontSize: AppFontSize.titleSubFontSize,
        ),
      )
      .apply(
        displayColor: AppColors.contentDarkGreyColor,
        bodyColor: AppColors.contentDarkGreyColor,
      )
      .copyWith(
          headlineMedium: kHeadlineTheme(base).headlineMedium?.copyWith());
}

const ColorScheme kColorScheme = ColorScheme(
  primary: AppColors.primaryColor,
  secondary: AppColors.secondaryColor,
  surface: AppColors.whiteColor,
  error: AppColors.errorRedColor,
  onPrimary: AppColors.kDarkBG,
  onSecondary: AppColors.contentDarkGreyColor,
  onSurface: AppColors.contentDarkGreyColor,
  onError: AppColors.backgroundColor,
  brightness: Brightness.light,
);

ThemeData buildDarkTheme(String language) {
  final base = ThemeData.dark();
  return base.copyWith(
    textTheme: _buildTextTheme(base.textTheme, language).apply(
      displayColor: AppColors.kLightBG,
      bodyColor: AppColors.kLightBG,
    ),
    primaryTextTheme: _buildTextTheme(base.primaryTextTheme, language).apply(
      displayColor: AppColors.kLightBG,
      bodyColor: AppColors.kLightBG,
    ),
    cardColor: AppColors.kDarkBgLight,
    brightness: Brightness.dark,
    primaryColor: AppColors.kDarkBG,
    primaryColorLight: AppColors.kDarkBgLight,
    scaffoldBackgroundColor: AppColors.kDarkBG,
    // cursorColor: AppColors.kDarkAccent,
    appBarTheme: AppBarTheme(
      elevation: 0,
      iconTheme: const IconThemeData(
        color: AppColors.kDarkAccent,
      ),
      toolbarTextStyle: const TextTheme(
        titleLarge: TextStyle(
          color: AppColors.kDarkBG,
          fontSize: AppFontSize.normalFontSize,
          fontWeight: FontWeight.w800,
        ),
      ).bodyMedium,
      titleTextStyle: const TextTheme(
        titleLarge: TextStyle(
          color: AppColors.kDarkBG,
          fontSize: AppFontSize.normalFontSize,
          fontWeight: FontWeight.w800,
        ),
      ).titleLarge,
    ),
    buttonTheme: ButtonThemeData(
        colorScheme: kColorScheme.copyWith(onPrimary: AppColors.kLightBG)),
    pageTransitionsTheme: const PageTransitionsTheme(builders: {
      TargetPlatform.iOS: FadeUpwardsPageTransitionsBuilder(),
      TargetPlatform.android: FadeUpwardsPageTransitionsBuilder(),
    }),
    tabBarTheme: const TabBarTheme(
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white,
      labelPadding: EdgeInsets.zero,
      labelStyle: TextStyle(fontSize: AppFontSize.xSmallFontSize),
      unselectedLabelStyle: TextStyle(fontSize: AppFontSize.xSmallFontSize),
    ),
    colorScheme: const ColorScheme.dark().copyWith(
      surface: AppColors.kDarkBG,
      secondary: AppColors.kDarkAccent,
    ),
  );
}
