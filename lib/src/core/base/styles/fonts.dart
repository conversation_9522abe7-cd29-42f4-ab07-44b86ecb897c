import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';

/// Google fonts constant setting: https://fonts.google.com/
TextTheme kTextTheme(theme, String language) {
  // switch (language) {
  //   case 'vi':
  //     return GoogleFonts.krubTextTheme(theme);
  //   case 'ar':
  //     return GoogleFonts.ralewayTextTheme(theme);
  //   default:
  //     return GoogleFonts.krubTextTheme(theme);
  // }
  return TextTheme(
    headlineSmall: kTextStyle(),
    titleLarge: kTextStyle(),
    bodySmall: kTextStyle(),
    titleMedium: kTextStyle(),
    labelLarge: kTextStyle(),
    headlineMedium: kTextStyle(),
  );
}

TextTheme kHeadlineTheme(theme, [language = 'en']) {
  // switch (language) {
  //   case 'vi':
  //     return GoogleFonts.krubTextTheme(theme);
  //   case 'ar':
  //     return GoogleFonts.ralewayTextTheme(theme);
  //   default:
  //     return GoogleFonts.krubTextTheme(theme);
  // }
  return TextTheme(headlineMedium: kTextStyle());
}

TextStyle kTextStyle() {
  // return GoogleFonts.krub();
  return const TextStyle(fontFamily: 'MyFont');
}

class AppFontSize {
//Action Bar	10pt	Don't go smaller than this
  static const double xSmallFontSize = 10.0;

  static const double smallFontSize = 12.0;
  static const double normalFontSize = 14.0;
  static const double normalFontSize2 = 15.0;
  static const double titleSubFontSize = 17.0;

  static const double titleSubFontSize2 = 19.0;
  static const double titleFontSize = 23.0;
  static const double titleToolbarFontSize = 23.0;

  static const double largeFontSize = 25.0;
  static const double xLargeFontSize = 32.0;

  static const double iconFontSize = 30.0;
}
