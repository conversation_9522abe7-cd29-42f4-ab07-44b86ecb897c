import 'package:flutter/material.dart';

class AppColors {
  // Color primaryColor = Color(0xFFB74951);
  // Color darkPrimaryColor = Color(0xFFAB313A);
  static const iconColor = Colors.white;
  static const textColor = Colors.white;
  static const splashIndicatorColor = Colors.grey;

  static const transparent = Colors.transparent;

  static const textBackgroundColor = Colors.white;
  static const changeBackgroundColor = Colors.white;

  static const kEmptyColor = 0x0D000000;

  static const primaryColor = Color(0xFF000000);
  // static const primaryColor = Color(0xFF447b91);
  static const darkPrimaryColor = Color(0xFF254450);
  static const primary4Color = Color(0xFFE7F2E6);

  // static const secondaryColor = Color(0xFF447b91);
  static const secondaryColor = Color(0xFFF6FC9C);
  static const secondary2Color = Color(0xFFEAF818);
  static const secondary3Color = Color(0xFFAEC200);
  static const secondary4Color = Color(0xFFECF939);
  static const secondary5Color = Color(0xFFFFE70E);
  static const secondary6Color = Color(0xFFFFF48E);
  static const secondary7Color = Color(0xFFF3FB81);

  // background
  static const backgroundColor = Color(0xFFFFFFFF);
  static const backgroundButton = Color(0xFFE7F2E6);
  static const backgroundDisableButton = Color(0xFFCBCCCB);

  static const whiteColor = Color(0xFFFFFFFF);
  static const grayLightColor = Color(0xFFF2F2F2);
  static const grayColor = Color(0xFFC4C4C4);
  static const dividerColor = Color(0xFF3E434E);
  static const borderInput = Color(0xFFC0C2C0);
  static const boxColor = Color(0xFF1D242F);
  static const borderColor = Color(0xFF475467);
  static const yellow80 = Color(0xFF191912);

  // content
  static const contentBlackColor = Color(0xFFFFFFFF);
  static const contentDarkGreyColor =
      Color(0xFFFFFFFF); // default content color
  static const contentDarkGrey2Color = Color(0xFFFFFFFF);
  static const contentLightGreyColor = Color(0xFFFFFFFF);
  static const contentPrimaryColor = Color(0xFFFFFFFF);
  static const divColor = Color(0xFFFFFFFF);
  // static const contentBlackColor = Color(0xFF2D302C);
  // static const contentDarkGreyColor = Color(0xFF4B514B); // default content color
  // static const contentDarkGrey2Color = Color(0xFF5D5F5D);
  // static const contentLightGreyColor = Color(0xFF90948F);
  // static const contentPrimaryColor = Color(0xFF31642D);
  // static const divColor = Color(0xFF727C8E);

  static const errorRedColor = Color(0xFFe74c3c);

  /// color for theme
  /// light
  static const kLightPrimary = Color(0xFFFFFFFF);
  // static const kLightPrimary = Color(0xfffcfcff);
  static const kLightAccent = Color(0xFF447b91);
  static const kLightBG = Color(0xFFFFFFFF);
  // static const kLightBG = Color(0xFFF6F6F6);
// dark
  static const kDarkAccent = Color(0xffF4F5F5);
  static const kDarkBG = Color(0xff121212);
  static const kDarkBgLight = Color(0xff1E1E1E);

  static const startColor = Color(0xFFF6A800);
  static const startUnCheckColor = Color(0xFFCECECE);

  static const subTitleColor = Color(0xff98A2B3);
  static const grey60 = Color(0xff363A43);
  static const grey70 = Color(0xff292B32);
  static const grey75 = Color(0xff1D2424);
  static const grey80 = Color(0xff191911);
  static const purple85 = Color(0xff5925DC);
  static const purple90 = Color(0xff4910D9);
  // Red Color.
// Orange Color.
// Yellow Color.
// Green Color.
// Blue Color.
// Indigo Color.
// Violet Color.
  static const kColor7LX = [
    // Colors.red,
    Color(0xFFE57373),
    // Colors.orange,
    Color(0xFFFFB74D),
    // Colors.yellow,
    Color(0xFFFFF176),
    // Colors.green,
    Color(0xFF81C784),
    // Colors.blue,
    Color(0xFF64B5F6),
    // Colors.indigo,
    Color(0xFF7986CB),
    // Color(0xFF7f00ff),
    Color(0xFF7f50ff)
  ];

  static const kColor7LX2 = [
    Colors.red,
    Colors.orange,
    Colors.yellow,
    Colors.green,
    Colors.blue,
    Colors.indigo,
    Color(0xFF7f00ff),
  ];
}
