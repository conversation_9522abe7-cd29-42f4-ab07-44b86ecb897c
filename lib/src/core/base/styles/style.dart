import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../constants.dart';
import '../../styles.dart';

class Style {
  static void styleDefault() async {
    if (!kIsWeb) {
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
          overlays: [SystemUiOverlay.bottom]);

      await SystemChrome.setPreferredOrientations(
          [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

      // SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      //   statusBarColor: Colors.transparent,
      // ));

      // SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      //     statusBarColor: Colors.transparent,
      //     statusBarIconBrightness: Brightness.light,
      //     systemNavigationBarColor: mColorNavigationBar,
      //     systemNavigationBarIconBrightness: Brightness.light)
      // );
    }
  }

  static LinearGradient linearGradientTTT1() => const LinearGradient(
        // cos 30 = 0,8666
        // sin 30 = 0,5
        begin: Alignment(0.7, 0.85),
        end: Alignment(-0.46, -1.9),
        colors: [Color(0xff000000), Color(0xff414141)],
        stops: [0.0, 1],
      );

  static LinearGradient linearGradientTTT2() => const LinearGradient(
        begin: Alignment(-0.51, 0.91),
        end: Alignment(0.46, -1.98),
        colors: [Color(0xff000000), Color(0xff414141)],
        stops: [0.0, 1.0],
      );

  static LinearGradient linearGradient() => const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        // begin: Alignment.topCenter,
        // end: Alignment.bottomCenter,
        colors: [...AppColors.kColor7LX],
      );

  static LinearGradient linearGradient2() => const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        // begin: Alignment.topCenter,
        // end: Alignment.bottomCenter,
        colors: [...AppColors.kColor7LX2],
      );
}

const kMessageContainerDecoration = BoxDecoration(
  border: Border(
    top: BorderSide(color: AppColors.primaryColor, width: 2.0),
  ),
);

const kSendButtonTextStyle = TextStyle(
  color: AppColors.primaryColor,
  fontWeight: FontWeight.bold,
  fontSize: 18.0,
);

const kMessageTextFieldDecoration = InputDecoration(
  contentPadding: EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
  hintText: 'Type your message here...',
  border: InputBorder.none,
);

const kProductTitleStyleLarge =
    TextStyle(fontSize: 18, fontWeight: FontWeight.bold);

const kTextField = InputDecoration(
  hintText: 'Enter your value',
  contentPadding: EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
  border: OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(3.0)),
  ),
  enabledBorder: OutlineInputBorder(
    borderSide: BorderSide(color: Colors.blueGrey, width: 1.0),
    borderRadius: BorderRadius.all(Radius.circular(3.0)),
  ),
);
