import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/styles.dart';

class DateTimeDialog {
  static Future<DateTime?> selectDate(BuildContext context, DateTime initDate,
      {initialEntryMode = DatePickerEntryMode.calendar}) async {
    if (DateTime(DateTime.now().year - 100).isAfter(initDate)) {
      initDate = DateTime(DateTime.now().year - 50);
    }
    final picked = await showDatePicker(
      context: context,
      locale: const Locale('vi', 'VN'),
      fieldHintText: 'dd/mm/yyyy',
      initialDatePickerMode: DatePickerMode.year,
      initialDate: initDate,
      firstDate: DateTime(DateTime.now().year - 100),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.dark().copyWith(
            // accentColor: AppColors.secondary3Color,
            colorScheme: const ColorScheme.dark(
              // change the border color
              primary: AppColors.secondaryColor,
              // secondary: AppColors.errorRedColor,
              // change the text color
              onSurface: AppColors.whiteColor,
            ),
          ),
          // isMaterialAppTheme: true,
          // data: Theme.of(context).copyWith(
          //   primaryColor: AppColors.primaryColor,
          //   accentColor: AppColors.primaryColor,
          //   buttonColor: AppColors.primaryColor,
          //   colorScheme: ColorScheme.light(
          //     // change the border color
          //     primary: AppColors.secondary3Color,
          //     // change the text color
          //     // onSurface: AppColors.primaryColor,
          //   ),
          // ),
          child: child ?? Container(),
        );
      },
      initialEntryMode: initialEntryMode,
    );
    if (picked != null && picked != initDate) {
      return picked;
    }

    return null;
  }

  static Future<TimeOfDay?> selectTime(
      BuildContext context, TimeOfDay initTime) async {
    final picked = await showTimePicker(
      context: context,
      initialTime: initTime,
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child ?? Container(),
        );
      },
    );
    if (picked != null && picked != initTime) {
      return picked;
    }

    return null;
  }
}
