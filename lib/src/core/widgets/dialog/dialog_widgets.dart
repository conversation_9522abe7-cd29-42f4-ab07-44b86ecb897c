import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

class DialogManager {
  static dynamic showSuccessDialog({
    required BuildContext context,
    message,
    title,
    titleAccept,
    Function()? onAccept,
  }) {
    return ModalManager.pushShowDialog(
      context,
      (context) => SuccessDialog(
        title: title,
        message: message,
        onAccept: onAccept,
        titleAccept: titleAccept,
      ),
    );
  }

  static dynamic showFailureDialog({
    required BuildContext context,
    message,
    Function()? onClose,
    title,
    titleClose,
  }) {
    return ModalManager.pushShowDialog(
      context,
      (context) => FailureDialog(
        title: title,
        message: message,
        onClose: onClose,
        titleClose: titleClose,
      ),
    );
  }

  static dynamic showConfirmDialog({
    required BuildContext context,
    message,
    Function()? onAccept,
    Function()? onCancel,
    String titleAccept = 'OK',
    String titleCancel = 'Đóng',
    String? title,
  }) {
    return ModalManager.pushShowDialog(
      context,
      (context) => ConfirmDialog(
        message: message,
        onAccept: onAccept,
        onCancel: onCancel,
        titleAccept: titleAccept,
        titleCancel: titleCancel,
        title: title,
      ),
    );
  }
}

class SuccessDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final Function()? onAccept;
  final String? titleAccept;

  const SuccessDialog(
      {Key? key, this.message, this.onAccept, this.title, this.titleAccept})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MDialogContainer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              MButtonImage(
                onTap: () {
                  context.pop();
                },
                child: const Icon(
                  Icons.clear_rounded,
                  color: AppColors.whiteColor,
                ),
              )
            ],
          ),
          Padding(
            padding: EdgeInsets.only(
                top: 10.h, bottom: 22.h, left: 24.w, right: 24.w),
            child: Column(
              children: [
                if (title != null) ...[
                  CText.title2(
                    title ?? '',
                    color: AppColors.contentBlackColor,
                    fontWeight: FontWeight.bold,
                  ),
                  MBlock(12),
                ],
                if (message != null) ...[
                  CText.body(
                    message ?? '',
                    textAlign: TextAlign.center,
                    color: AppColors.contentBlackColor,
                  ),
                ],
                MBlock(32),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  child: CButton(
                    titleAccept ?? 'OK',
                    onTap: () {
                      context.pop();
                      if (onAccept != null) {
                        onAccept!();
                      }
                    },
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class FailureDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final Function()? onClose;
  final String? titleClose;

  const FailureDialog(
      {Key? key, this.message, this.onClose, this.title, this.titleClose})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MDialogContainer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              MButtonImage(
                onTap: () {
                  context.pop();
                },
                child: const Icon(
                  Icons.clear_rounded,
                  color: AppColors.kDarkBG,
                ),
              )
            ],
          ),
          Padding(
            padding: EdgeInsets.only(
                top: 10.h, bottom: 22.h, left: 24.w, right: 24.w),
            child: Column(
              children: [
                if (title != null) ...[
                  CText.title2(
                    title ?? '',
                    color: AppColors.contentBlackColor,
                    fontWeight: FontWeight.bold,
                  ),
                  MBlock(12),
                ],
                if (message != null) ...[
                  CText.body(
                    message ?? '',
                    textAlign: TextAlign.center,
                    color: AppColors.contentBlackColor,
                  ),
                ],
                MBlock(32),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  child: CButton.negative(
                    titleClose ?? 'Đóng',
                    onTap: () {
                      context.pop();
                      if (onClose != null) {
                        onClose!();
                      }
                    },
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class ConfirmDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final String? titleAccept;
  final String? titleCancel;
  final Function()? onAccept;
  final Function()? onCancel;

  const ConfirmDialog(
      {Key? key,
      this.message,
      this.onAccept,
      this.onCancel,
      this.titleAccept,
      this.titleCancel,
      this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MDialogContainer(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              MButtonImage(
                onTap: () {
                  context.pop();
                },
                child: const Icon(
                  Icons.clear_rounded,
                  color: AppColors.kDarkBG,
                ),
              )
            ],
          ),
          Padding(
            padding: EdgeInsets.only(
                top: 10.h, bottom: 22.h, left: 24.w, right: 24.w),
            child: Column(
              children: [
                if (title != null) ...[
                  CText.title2(
                    title ?? 'Title',
                    color: AppColors.kDarkBG,
                    fontWeight: FontWeight.bold,
                  ),
                  MBlock(12),
                ],
                if (message != null) ...[
                  CText.body(
                    message ?? '',
                    textAlign: TextAlign.center,
                    color: AppColors.kDarkBG,
                  ),
                ],
                MBlock(32),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: CButton.negative(
                          titleCancel ?? 'Đóng',
                          onTap: () {
                            context.pop();
                            if (onCancel != null) {
                              onCancel!();
                            }
                          },
                        ),
                      ),
                      MBlock(15),
                      Expanded(
                        child: CButton(
                          titleAccept ?? 'OK',
                          onTap: () {
                            context.pop();
                            if (onAccept != null) {
                              onAccept!();
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
