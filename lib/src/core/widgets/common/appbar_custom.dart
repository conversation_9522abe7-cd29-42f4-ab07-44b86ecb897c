import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

const _leadingWidth = 115.0;
const _backgroundColor = Colors.transparent;
const _automaticallyImplyLeading = false;
const _title = SizedBox();

class AppBarCustom extends AppBar {
  AppBarCustom({
    super.key,
    Future<void> Function()? overrideOnBack,
    bool showLabel = true,
    Widget? title,
    bool centerTitle = false,
    TextStyle titleTextStyle = const TextStyle(
        color: Colors.white,
        fontSize: 17,
        fontWeight: FontWeight.w600,
        height: 22 / 17),
  }) : super(
          title: title ?? _title,
          leading: Builder(
              builder: (context) => _leadingWidget(context,
                  onBack: overrideOnBack, showLabel: showLabel)),
          leadingWidth: title != null ? 50 : _leadingWidth,
          backgroundColor: _backgroundColor,
          automaticallyImplyLeading: _automaticallyImplyLeading,
          centerTitle: centerTitle,
          shadowColor: Colors.transparent,
          foregroundColor: Colors.transparent,
          toolbarHeight: 50,
          titleTextStyle: titleTextStyle,
        );
}

class SliverAppBarCustom extends StatelessWidget {
  const SliverAppBarCustom({
    super.key,
    this.pinned = true,
    this.floating = false,
    this.snap = false,
    this.showLabel = true,
    this.centerTitle = false,
  });

  final bool floating;
  final bool pinned;
  final bool snap;
  final bool showLabel;
  final bool centerTitle;

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      floating: floating,
      pinned: pinned,
      snap: snap,
      leading: _leadingWidget(context, showLabel: showLabel),
      title: _title,
      leadingWidth: _leadingWidth,
      backgroundColor: _backgroundColor,
      automaticallyImplyLeading: _automaticallyImplyLeading,
      centerTitle: centerTitle,
    );
  }
}

Widget _leadingWidget(
  BuildContext context, {
  Future<void> Function()? onBack,
  bool showLabel = true,
}) {
  final canPop = ModalRoute.of(context)?.canPop ?? false;
  return canPop
      ? GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            if (onBack != null) {
              await onBack();
            } else {
              Navigator.of(context).pop();
            }
          },
          child: Row(
            children: [
              const Icon(
                CupertinoIcons.back,
                color: Colors.white,
                size: 35,
              ),
              Text(
                showLabel ? 'Quay lại' : '   ',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
            ],
          ),
        )
      : const SizedBox();
}
