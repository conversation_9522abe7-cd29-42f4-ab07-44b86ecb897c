import 'dart:math';

import 'package:flutter/material.dart';

const _extentSizeForBottom = 10.0;

class PageDynamicWidget extends StatefulWidget {
  const PageDynamicWidget({
    super.key,
    required this.pageController,
    required this.children,
    this.heightDefault = 220.0,
    this.useMaxHeight = true,
  });

  final PageController pageController;
  final List<Widget> children;
  final double heightDefault;
  final bool useMaxHeight;

  @override
  State<PageDynamicWidget> createState() => _PageDynamicWidgetState();
}

class _PageDynamicWidgetState extends State<PageDynamicWidget> {
  final _heightOfTab = [];
  late var _maxHeight = widget.heightDefault;
  late final _sizePage = ValueNotifier(_heightTitlePage);
  int get pageCurrent =>
      (widget.pageController.hasClients && widget.pageController.page != null)
          ? widget.pageController.page!.toInt()
          : 0;

  double get _heightTitlePage => widget.heightDefault;

  void _onChangePage() {
    if (widget.useMaxHeight) {
      _sizePage.value = _maxHeight;
    } else if (widget.pageController.hasClients &&
        widget.pageController.page != null)
      _sizePage.value =
          _heightOfTab[widget.pageController.page!.toInt()] ?? _heightTitlePage;
  }

  @override
  void initState() {
    super.initState();
    widget.pageController.addListener(_onChangePage);
    for (var i = 0; i < widget.children.length; i++) {
      _heightOfTab.add(_heightTitlePage);
    }
  }

  @override
  void dispose() {
    widget.pageController.removeListener(_onChangePage);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSize(
      duration: const Duration(milliseconds: 500),
      child: ValueListenableBuilder<double>(
        valueListenable: _sizePage,
        builder: (_, heightPage, __) {
          return SizedBox(
            height: heightPage,
            child: PageView(
              controller: widget.pageController,
              children: List.generate(
                widget.children.length,
                (index) {
                  final item = widget.children[index];
                  return _DetectorSizeChildWidget(
                    onGetSize: (p0, p1) {
                      _heightOfTab[index] = p0.height + _extentSizeForBottom;

                      if (widget.useMaxHeight) {
                        _maxHeight = max(_maxHeight, _heightOfTab[index]);
                        _sizePage.value = _maxHeight;
                      } else if (index == pageCurrent) {
                        _sizePage.value =
                            _heightOfTab[index] ?? _heightTitlePage;
                      }
                    },
                    child: item,
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

class _DetectorSizeChildWidget extends StatefulWidget {
  const _DetectorSizeChildWidget({
    required this.child,
    required this.onGetSize,
  });

  final Widget child;
  final void Function(Size, BuildContext) onGetSize;

  @override
  State<_DetectorSizeChildWidget> createState() =>
      _DetectorSizeChildWidgetState();
}

class _DetectorSizeChildWidgetState extends State<_DetectorSizeChildWidget> {
  GlobalKey globalKeyCenterButton = GlobalKey();

  void _getSize() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (globalKeyCenterButton.currentContext != null) {
        final RenderBox renderBox = globalKeyCenterButton.currentContext!
            .findRenderObject() as RenderBox;
        widget.onGetSize(renderBox.size, context);
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _getSize();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: SizedBox(
        key: globalKeyCenterButton,
        child: widget.child,
      ),
    );
  }
}
