import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:intl/intl.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/widgets/common/tag_widget.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

import '../../../../generated/l10n.dart';
import '../../../di/injection/injection.dart';
import '../../../presentation/payment/subscription_bloc.dart';
import '../../../presentation/shared/account/account_bloc.dart';
import '../../../presentation/shared/app_bloc/app_bloc.dart';
import '../../services/firebase/analytics/firebase_analytics_wapper.dart';

class SubcriptionWidget extends StatefulWidget {
  final String title;
  final int? percent;
  final String description;
  final int oldPrice;
  final int newPrice;
  final Function()? onPressed;
  final bool? isSubcription;

  const SubcriptionWidget({
    super.key,
    required this.title,
    this.percent,
    required this.description,
    required this.oldPrice,
    required this.newPrice,
    this.onPressed,
    this.isSubcription = true,
  });

  @override
  State<SubcriptionWidget> createState() => _SubcriptionWidgetState();
}

class _SubcriptionWidgetState extends State<SubcriptionWidget> {
  late SubscriptionBloc _subscriptionBloc;

  @override
  void initState() {
    super.initState();
    _subscriptionBloc = Provider.of<SubscriptionBloc>(context, listen: false);
    _subscriptionBloc.handleUIBloc.uiHandleController.listen((value) async {
      var context = AppBloc.navigatorKey.currentState!.context;
      if (value.isError) {
        await Alert(
          context: context,
          type: AlertType.error,
          title: S.of(context).failed,
          desc: value.message,
          buttons: [
            DialogButton(
              onPressed: () => context.pop(),
              width: 120,
              child: Text(
                S.of(context).tryAgain,
                style: const TextStyle(color: Colors.white, fontSize: 20),
              ),
            )
          ],
        ).show();
      }

      if (value.isSucess) {
        await context.startPaymentSuccess(message: value.message);
      }
    });
    _subscriptionBloc.init();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      // width: double.infinity,
      decoration: const BoxDecoration(
        color: AppColors.kDarkBG,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 7.5),
          Container(
            width: MediaQuery.of(context).size.width / 6,
            height: 5,
            decoration: BoxDecoration(
              color: AppColors.whiteColor,
              borderRadius: BorderRadius.circular(5),
            ),
          ),
          const SizedBox(height: 30),
          Row(
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  color: AppColors.whiteColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 10),
              const TagWidget(
                text: 'Premium',
                colorTag: AppColors.secondary2Color,
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              ),
            ],
          ),
          if (widget.percent != null) const SizedBox(height: 40),
          if (widget.percent != null)
            Text(
              'Giảm ${widget.percent}%',
              style: const TextStyle(
                color: AppColors.secondary2Color,
                fontSize: 40,
                fontWeight: FontWeight.bold,
                letterSpacing: 2.0,
              ),
            ),
          if (widget.percent != null)
            const Text(
              'Khi đăng ký ngay hôm nay',
              style: TextStyle(
                color: AppColors.subTitleColor,
                fontSize: 16,
                letterSpacing: 0.5,
              ),
            ),
          const SizedBox(height: 30),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              widget.description,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: AppColors.subTitleColor,
                fontSize: 16,
                letterSpacing: 0.5,
              ),
            ),
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: AppColors.whiteColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        'Giảm ${widget.percent}%, giúp bạn tiết kiệm',
                        style: const TextStyle(
                          color: AppColors.grey60,
                          fontSize: 14,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    ButtonGradiantWidget.secondary01(
                        paddingContent: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        title:
                            '${NumberFormat.simpleCurrency(locale: 'vi').format(widget.oldPrice - widget.newPrice)}/năm'),
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '12 tháng ${NumberFormat.simpleCurrency(locale: 'vi').format(widget.newPrice)}',
                      style: const TextStyle(
                        color: AppColors.grey60,
                        fontSize: 16,
                        letterSpacing: 0.5,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const Text(
                      '...',
                      style: TextStyle(
                        color: AppColors.subTitleColor,
                        fontSize: 16,
                        letterSpacing: 0.5,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      '${NumberFormat.simpleCurrency(locale: 'vi').format(widget.oldPrice)}',
                      style: const TextStyle(
                        color: AppColors.subTitleColor,
                        fontSize: 16,
                        letterSpacing: 0.5,
                        fontWeight: FontWeight.w700,
                        decoration: TextDecoration.lineThrough,
                        decorationColor: AppColors.subTitleColor,
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          const SizedBox(height: 30),
          Container(
              constraints: const BoxConstraints(minWidth: double.infinity),
              child: widget.isSubcription!
                  ? ButtonGradiantWidget.primary(
                      onPressed: () async {
                        if (_subscriptionBloc.selectedProduct != null) {
                          await FirebaseAnalyticsWapper()
                              .analytics
                              .logEvent(name: 'Purchase', parameters: {
                            'action': 'continue',
                            'product_id': _subscriptionBloc.selectedProduct!.id,
                            'product_title':
                                _subscriptionBloc.selectedProduct!.title,
                          });

                          if (injector<AccountBloc>().isUXCamInit) {
                            await FlutterUxcam.logEventWithProperties(
                                'Purchase', {
                              'action': 'continue',
                              'product_id':
                                  _subscriptionBloc.selectedProduct?.id,
                              'product_title':
                                  _subscriptionBloc.selectedProduct?.title,
                            });
                          }
                        }

                        _subscriptionBloc.buyProduct();
                      },
                      title: 'Đăng ký ngay')
                  : const ButtonGradiantWidget.secondary02(
                      onPressed: null, title: 'Đăng ký ngay')),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
