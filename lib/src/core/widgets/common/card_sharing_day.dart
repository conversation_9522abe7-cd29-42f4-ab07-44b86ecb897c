import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/constants/icons.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/common/image_linear_gradient_widget.dart';
import 'package:meditaguru/src/core/widgets/common/tag_widget.dart';

const _minHeightCard = 200.0;

class CardSharingDayWidget extends StatelessWidget {
  final String imageUrl;
  final int? timeMinute;
  final bool isPremium;
  final bool isPaid;
  final String description;
  final double? height;
  final double? width;
  final String? countLessonText;
  final void Function()? onTap;
  final AlignmentGeometry? alignment;

  const CardSharingDayWidget({
    super.key,
    required this.imageUrl,
    this.timeMinute,
    this.countLessonText,
    this.isPremium = false,
    this.isPaid = false,
    required this.description,
    this.height,
    this.width,
    this.onTap,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          minHeight: _minHeightCard,
        ),
        child: ImageLinearGradientWidget(
          imageUrl,
          width: width,
          height: height,
          stops: [0.0, 1],
          alignment: alignment ?? Alignment.center,
          gradiantColors: [
            Colors.black.withOpacity(0.5),
            Colors.transparent,
          ],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (countLessonText?.isNotEmpty ?? false)
                    SizedBox(
                      height: 24,
                      child: TagWidget(
                        iconPath: IconConstants.music02,
                        text: countLessonText!,
                        colorTag: AppColors.whiteColor.withOpacity(0.6),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 3),
                        fontSize: 11,
                        iconSize: 14,
                      ),
                    )
                  else if (timeMinute != null)
                    TagWidget(
                        iconPath: IconConstants.music02,
                        text: '$timeMinute Phút',
                        colorTag: AppColors.whiteColor.withOpacity(0.6),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 6))
                  else
                    const SizedBox(),
                  if (isPremium)
                    TagWidget(
                      iconPath: isPaid ? null : IconConstants.lock,
                      text: 'Premium',
                      colorTag: AppColors.secondary2Color,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                    ),
                ],
              ),
              SizedBox(
                height: 55,
                child: Align(
                  alignment: AlignmentDirectional.centerStart,
                  child: Text(
                    description,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 0.6,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
