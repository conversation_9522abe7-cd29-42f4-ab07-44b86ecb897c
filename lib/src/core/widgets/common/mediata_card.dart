import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/button_gradiant_widget.dart';
import 'package:meditaguru/src/core/widgets/common/image_linear_gradient_widget.dart';
import 'package:meditaguru/src/core/widgets/common/tag_widget.dart';
import 'package:meditaguru/src/core/wrappers/description_highlight_text.dart';

import '../../base/styles/colors.dart';
import '../../constants/icons.dart';

class CardMediataWidget extends StatelessWidget {
  final String title;
  final String description;
  final List<String> highlightText;
  final int? countChildItems;
  final String imageUrl;
  final Function()? onTap;
  final double? width;
  final double? height;

  const CardMediataWidget({
    super.key,
    required this.title,
    required this.description,
    required this.highlightText,
    required this.imageUrl,
    this.onTap,
    this.width,
    this.height,
    this.countChildItems,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: ImageLinearGradientWidget(
        imageUrl,
        width: width,
        height: height,
        radius: 24,
        border: Border.all(
          width: 1,
          color: const Color(0xFF475467),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (countChildItems != null)
              SizedBox(
                height: 24,
                child: TagWidget(
                  iconPath: IconConstants.music02,
                  text: '$countChildItems khoá thiền',
                  colorTag: AppColors.whiteColor.withOpacity(0.6),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                  fontSize: 11,
                  iconSize: 14,
                ),
              ),
            const Spacer(),
            MBlock(6),
            KeywordsHighlightWidget(
              text: description,
              keywordsHighlight: highlightText,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            MBlock(16),
            ButtonGradiantWidget.secondary02(
              onPressed: onTap,
              title: S.of(context).meditationNow,
            ),
          ],
        ),
      ),
    );
  }
}
