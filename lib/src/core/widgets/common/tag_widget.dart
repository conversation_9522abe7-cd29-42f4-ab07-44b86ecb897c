import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

import 'image_widget.dart';

class TagWidget extends StatelessWidget {
  final String? iconPath;
  final String text;
  final Color colorTag;
  final EdgeInsetsGeometry padding;
  final BorderRadius? borderRadius;
  final double? fontSize;
  final double? iconSize;

  const TagWidget({
    super.key,
    this.iconPath,
    required this.text,
    required this.colorTag,
    required this.padding,
    this.borderRadius,
    this.fontSize,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: colorTag,
        borderRadius:
            borderRadius ?? const BorderRadius.all(Radius.circular(30)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (iconPath != null)
            ImageWidget(
              iconPath!,
              width: iconSize ?? 20,
            ),
          if (iconPath != null) const SizedBox(width: 5),
          Text(
            text,
            style: TextStyle(
              color: AppColors.kDarkBG,
              fontSize: fontSize ?? 14,
              fontWeight: FontWeight.bold,
            ),
          )
        ],
      ),
    );
  }
}
