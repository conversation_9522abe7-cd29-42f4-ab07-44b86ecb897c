import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';

import '../base_widgets/lib_base_widgets.dart';
import 'appbar_custom.dart';

class BackgroundLayoutWidget extends StatelessWidget {
  const BackgroundLayoutWidget({
    super.key,
    required this.backgroundImage,
    required this.body,
    this.useLoading = false,
    this.useBackgroundOverlay = true,
    this.heightBackground,
    this.widthBackground,
    this.showLabelAppBar = true,
  });

  final String backgroundImage;
  final Widget body;
  final bool useLoading;
  final bool useBackgroundOverlay;
  final double? heightBackground;
  final double? widthBackground;
  final bool showLabelAppBar;

  @override
  Widget build(BuildContext context) {
    final heightImage =
        heightBackground ?? MediaQuery.sizeOf(context).height * 0.34;
    final widthImage = widthBackground ?? MediaQuery.sizeOf(context).width;

    return MScaffold(
      backgroundColor: Colors.black,
      safeAreaTop: false,
      useLoading: useLoading,
      body: Stack(
        children: [
          Positioned(
            child: SizedBox(
              height: heightImage,
              width: widthImage,
              child: ImageWidget(
                backgroundImage,
                fit: BoxFit.fitWidth,
              ),
            ),
          ),
          if (useBackgroundOverlay)
            Container(
              height: heightImage,
              width: widthImage,
              decoration: BoxDecoration(color: Colors.black.withOpacity(0.1)),
            ),
          Positioned(
            child: Container(
              height: heightImage,
              width: widthImage,
              decoration: useBackgroundOverlay
                  ? BoxDecoration(
                      gradient: LinearGradient(
                        stops: [0.08, 0.9],
                        colors: [
                          Colors.black.withOpacity(1),
                          Colors.transparent,
                        ],
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                      ),
                    )
                  : null,
            ),
          ),
          Positioned.fill(
            child: Column(
              children: [
                AppBarCustom(showLabel: showLabelAppBar),
                Expanded(child: body),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
