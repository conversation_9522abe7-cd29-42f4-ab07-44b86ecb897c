import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class TabbarAutoScrollWidget extends StatefulWidget {
  final List<ItemTabBar> items;
  final ValueChanged<int>? onTap;
  final PageController? controller;
  final bool useScroll;
  final EdgeInsets? paddingContent;

  final Widget Function(dynamic item, int index, bool isSelected)? itemBuilder;

  const TabbarAutoScrollWidget({
    super.key,
    this.onTap,
    this.controller,
    required this.items,
    this.itemBuilder,
    this.useScroll = false,
    this.paddingContent,
  });

  @override
  State<TabbarAutoScrollWidget> createState() => _TabbarAutoScrollWidgetState();
}

class _TabbarAutoScrollWidgetState extends State<TabbarAutoScrollWidget> {
  late AutoScrollController controller;
  final _indexSelected = ValueNotifier<int>(0);
  bool _isTap = false;

  void _onTapItem(int index) {
    _isTap = true;
    widget.controller?.animateToPage(index,
        curve: Curves.easeIn, duration: const Duration(milliseconds: 300));
    controller.scrollToIndex(index, preferPosition: AutoScrollPosition.middle);
    widget.onTap?.call(index);
    _indexSelected.value = index;
  }

  @override
  void initState() {
    super.initState();
    widget.controller?.addListener(() {
      final pageRound = widget.controller?.page?.round();

      if (_isTap) {
        _isTap = pageRound != _indexSelected.value;
      } else if (pageRound != _indexSelected.value) {
        _indexSelected.value = pageRound ?? 0;
        controller.scrollToIndex(pageRound ?? 0,
            preferPosition: AutoScrollPosition.middle);
      }
    });

    controller = AutoScrollController(
      viewportBoundaryGetter: () =>
          Rect.fromLTRB(0, 0, 0, MediaQuery.of(context).padding.bottom),
      axis: Axis.horizontal,
    );
  }

  @override
  Widget build(BuildContext context) {
    final listItem = List.generate(
      widget.items.length,
      (index) {
        final item = widget.items[index];
        return ValueListenableBuilder<int>(
          valueListenable: _indexSelected,
          builder: (_, indexSelected, __) {
            final isSelected = index == _indexSelected.value;

            return AutoScrollTag(
              controller: controller,
              key: ValueKey('news_K$index'),
              index: index,
              child: Padding(
                padding: EdgeInsetsDirectional.only(
                    end: index != (widget.items.length - 1) ? 7 : 0),
                child: GestureDetector(
                  onTap: () => _onTapItem(index),
                  child: widget.itemBuilder != null
                      ? widget.itemBuilder!(item, index, isSelected)
                      : Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: ButtonSelectorWidget(
                                onPressed: () => _onTapItem(index),
                                iconUrl: item.icon,
                                isSelected: isSelected,
                                labelText: item.title,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            );
          },
        );
      },
    );

    if (widget.useScroll == false) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: listItem,
      );
    }

    return ListView(
      padding: widget.paddingContent,
      shrinkWrap: true,
      controller: controller,
      scrollDirection: Axis.horizontal,
      children: listItem,
    );
  }
}

class ItemTabBar {
  final String title;
  final String icon;

  ItemTabBar(this.title, this.icon);
}
