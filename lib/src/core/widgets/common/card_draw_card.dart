import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/meditaguru.dart';

const _heightCard = 230.0;
const _maxWidthCard = 400.0;
const _widthImage = 99.0;
const _heightImage = 100.0;
const _borderRadius = 24.0;

class CardDrawCardWidget extends StatelessWidget {
  final Function()? onPressed;
  final EdgeInsetsGeometry? paddingWhenHasData;

  const CardDrawCardWidget({
    super.key,
    this.onPressed,
    this.paddingWhenHasData,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(_borderRadius),
      child: Container(
        constraints: const BoxConstraints(maxWidth: _maxWidthCard),
        height: _heightCard,
        decoration: const BoxDecoration(
          color: AppColors.grey80,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(_borderRadius),
            topRight: Radius.circular(_borderRadius),
          ),
        ),
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ...[
                    const ImageWidget(
                      ImageConstants.cardDraw,
                      width: _widthImage,
                      height: _heightImage,
                    ),
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Text(
                        S.of(context).messageEveryday,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          color: AppColors.whiteColor,
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          height: 20 / 15,
                        ),
                      ),
                    ),
                  ].map((e) => Center(child: e)).toList(),
                  const SizedBox(height: 12),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 55),
                    child: ButtonSelectorWidget(
                      isSelected: true,
                      onPressed: onPressed,
                      labelText: S.of(context).drawCard,
                      styleLabel: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          height: 20 / 15),
                      backgroundColor: AppColors.grey80,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                  ),
                ],
              ),
            ),
            const Positioned(
              bottom: 0,
              right: 3,
              child: Opacity(
                opacity: 0.4,
                child: ImageWidget(
                  IconConstants.lotus,
                ),
              ),
            ),
            const Positioned(
              bottom: 0,
              left: 10,
              child: ImageWidget(
                IconConstants.moon,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
