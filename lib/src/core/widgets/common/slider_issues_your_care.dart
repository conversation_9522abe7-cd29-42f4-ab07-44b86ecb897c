import 'dart:math';

import 'package:carousel_slider_plus/carousel_slider_plus.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';

const _aspectRatio = 31 / 28;
const _maxWidth = 400.0;
const _ratioWidth = 0.8267;

class SliderIssuesYourCareWidget extends StatelessWidget {
  final double? widthEveryElement;
  final double? heightEveryElement;
  final double? spacing;
  final List<CategoryMeditation> categories;
  final bool? autoPlay;
  final void Function(CategoryMeditation category) onPressed;
  final Duration duration;

  const SliderIssuesYourCareWidget({
    super.key,
    required this.categories,
    this.widthEveryElement,
    this.heightEveryElement,
    this.spacing,
    this.autoPlay = false,
    required this.onPressed,
    this.duration = const Duration(milliseconds: 800),
  });

  @override
  Widget build(BuildContext context) {
    var widthItem = MediaQuery.sizeOf(context).width * _ratioWidth;
    widthItem = min(widthItem, _maxWidth);
    final height = widthItem / _aspectRatio;

    return SizedBox(
      height: height,
      child: CarouselSlider(
        options: CarouselOptions(
          height: heightEveryElement,
          aspectRatio: _aspectRatio,
          viewportFraction: 0.912,
          initialPage: 0,
          enableInfiniteScroll: false,
          reverse: false,
          autoPlay: autoPlay ?? false,
          autoPlayInterval: const Duration(seconds: 3),
          autoPlayAnimationDuration: duration,
          autoPlayCurve: Curves.fastOutSlowIn,
          enlargeCenterPage: false,
          enlargeFactor: 0.3,
          scrollDirection: Axis.horizontal,
          padEnds: false,
        ),
        items: categories.isEmpty
            ? []
            : List<Widget>.generate(
                categories.length,
                (i) {
                  final item = categories[i];

                  return CardMediataWidget(
                    title: item.type,
                    height: height,
                    width: widthItem,
                    description: item.shortDescription,
                    highlightText: item.highlightKeywords,
                    countChildItems: item.courses?.length,
                    imageUrl: item.urlImageCard,
                    onTap: () => onPressed(item),
                  );
                },
              ),
      ),
    );
  }
}
