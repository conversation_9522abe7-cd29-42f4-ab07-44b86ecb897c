import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

import '../../../../../generated/l10n.dart';

const _shape = RoundedRectangleBorder(
  side: BorderSide(
    color: Colors.white,
    width: 0.2,
  ),
  borderRadius: BorderRadius.all(
    Radius.circular(10),
  ),
);

const _style = TextStyle(color: Colors.white);
const _backgroundColor = Colors.black;

class AlertDialogCommon {
  static Widget exitApp(BuildContext context) {
    return AlertDialog(
      shape: _shape,
      title: Text(
        S.of(context).exit,
        style: _style,
      ),
      content: Text(
        S.of(context).exitConfirm,
        style: _style,
      ),
      backgroundColor: _backgroundColor,
      actions: <Widget>[
        TextButton(
          onPressed: () => context.pop(false),
          child: Text(S.of(context).no),
        ),
        TextButton(
          onPressed: () {
            context.pop(true);
            SystemNavigator.pop(animated: true);
          },
          child: Text(
            S.of(context).yes,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  static Widget logOutAccount(BuildContext context) {
    return AlertDialog(
      shape: _shape,
      title: Text(
        S.of(context).logout,
        style: _style,
      ),
      content: Text(
        S.of(context).logoutConfirm,
        style: _style,
      ),
      backgroundColor: _backgroundColor,
      actions: <Widget>[
        TextButton(
          onPressed: () => context.pop(false),
          child: Text(S.of(context).no),
        ),
        TextButton(
          onPressed: () {
            context.pop(true);
          },
          child: Text(
            S.of(context).yes,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  static Widget reportCourse(
    BuildContext context, {
    required String courseName,
  }) {
    return AlertDialog(
      shape: _shape,
      title: Text(
        S.of(context).reportError,
        style: _style,
      ),
      content: Text(
        S.of(context).reportErrorMessage,
        style: _style,
      ),
      backgroundColor: _backgroundColor,
      actions: <Widget>[
        TextButton(
          onPressed: () => context.pop(false),
          child: Text(S.of(context).no),
        ),
        TextButton(
          onPressed: () {
            context.pop(true);
          },
          child: Text(
            S.of(context).yes,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }
}
