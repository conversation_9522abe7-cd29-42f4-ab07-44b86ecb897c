import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';

class ButtonSelectorWidget extends StatelessWidget {
  const ButtonSelectorWidget({
    super.key,
    this.labelText,
    this.iconUrl,
    this.label,
    this.icon,
    this.colorSelected = const Color(0xffECF939),
    this.backgroundColor = const Color(0xff373D44),
    required this.onPressed,
    this.isSelected = false,
    this.padding = EdgeInsets.zero,
    this.styleLabel,
  });

  final String? labelText;
  final String? iconUrl;
  final Widget? label;
  final Widget? icon;
  final Color colorSelected;
  final Color backgroundColor;
  final bool isSelected;
  final EdgeInsetsGeometry padding;
  final void Function()? onPressed;
  final TextStyle? styleLabel;

  @override
  Widget build(BuildContext context) {
    final color = isSelected ? colorSelected : Colors.white;
    Widget? iconWidget;
    if (icon != null) {
      iconWidget = icon;
    } else if (iconUrl?.isNotEmpty ?? false) {
      iconWidget = ImageWidget(
        iconUrl!,
        width: 20,
        height: 20,
        color: iconUrl!.contains('svg') ? color : null,
      );
    }

    Widget? labelWidget;
    final styleT = styleLabel ?? const TextStyle();
    if (label != null) {
      labelWidget = label!;
    } else if (labelText?.isNotEmpty ?? false) {
      labelWidget = Text(
        labelText!,
        style: styleT.copyWith(color: color),
      );
    }

    final style = ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      side: isSelected
          ? BorderSide(
              color: color,
              width: 1,
            )
          : null,
    );

    if (iconWidget != null) {
      return ElevatedButton.icon(
        onPressed: onPressed,
        icon: iconWidget,
        label: labelWidget ?? const SizedBox(),
        style: style,
      );
    }
    return ElevatedButton(
      onPressed: onPressed,
      child: Padding(
        padding: padding,
        child: labelWidget,
      ),
      style: style,
    );
  }
}
