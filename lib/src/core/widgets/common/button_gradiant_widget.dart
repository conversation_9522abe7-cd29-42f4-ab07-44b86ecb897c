import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/base/styles/colors.dart';

class ButtonGradiantWidget extends StatelessWidget {
  final void Function()? onPressed;
  final String title;
  final EdgeInsets paddingContent;
  final List<Color> colors;
  final BorderRadius? borderRadius;
  final TextStyle? style;
  final bool _useDisableType;
  final Widget? icon;
  final BoxBorder? border;

  const ButtonGradiantWidget({
    super.key,
    required this.onPressed,
    required this.title,
    this.paddingContent =
        const EdgeInsets.symmetric(horizontal: 30, vertical: 8),
    required this.colors,
    this.borderRadius,
    this.style,
    this.icon,
    this.border,
  }) : _useDisableType = true;

  const ButtonGradiantWidget.primary({
    super.key,
    required this.onPressed,
    required this.title,
    this.paddingContent =
        const EdgeInsets.symmetric(horizontal: 10, vertical: 14),
    this.borderRadius,
    this.icon,
    this.border,
  })  : colors = const [
          AppColors.secondary5Color,
          AppColors.secondary4Color,
        ],
        style = const TextStyle(
          fontWeight: FontWeight.w700,
          fontSize: 15,
        ),
        _useDisableType = true;

  const ButtonGradiantWidget.secondary01({
    super.key,
    required this.title,
    void Function()? onTap,
    this.paddingContent =
        const EdgeInsets.symmetric(vertical: 4, horizontal: 15),
    this.borderRadius,
    this.icon,
    this.border,
  })  : colors = const [
          AppColors.purple85,
          AppColors.purple90,
        ],
        style = const TextStyle(fontSize: 14, color: Colors.white),
        onPressed = onTap,
        _useDisableType = false;

  const ButtonGradiantWidget.secondary02({
    super.key,
    required this.onPressed,
    required this.title,
    this.paddingContent =
        const EdgeInsets.symmetric(vertical: 12, horizontal: 22),
    this.borderRadius,
    this.icon,
    this.border,
  })  : colors = const [
          AppColors.secondary4Color,
          AppColors.secondary4Color,
        ],
        style = const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
        _useDisableType = true;

  const ButtonGradiantWidget.icon(
    Widget iconWidget, {
    super.key,
    required this.onPressed,
    required this.title,
    this.paddingContent =
        const EdgeInsets.symmetric(vertical: 12, horizontal: 22),
    this.borderRadius,
    this.border = const Border.fromBorderSide(
      BorderSide(
        color: Color(0xff656651),
        width: 4,
      ),
    ),
  })  : colors = const [
          AppColors.secondary4Color,
          AppColors.secondary4Color,
        ],
        style = const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
        _useDisableType = true,
        icon = iconWidget;

  @override
  Widget build(BuildContext context) {
    final isDisable = _useDisableType && onPressed == null;

    return Container(
      decoration: BoxDecoration(
        border: border,
        borderRadius: borderRadius ?? BorderRadius.circular(50),
        gradient: LinearGradient(
            colors:
                isDisable ? [Colors.grey[200]!, Colors.grey[200]!] : colors),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: borderRadius ?? BorderRadius.circular(50),
          onTap: onPressed,
          child: Padding(
            padding: paddingContent,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null) ...[icon!, const SizedBox(width: 8)],
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style:
                      isDisable ? const TextStyle(color: Colors.grey) : style,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
