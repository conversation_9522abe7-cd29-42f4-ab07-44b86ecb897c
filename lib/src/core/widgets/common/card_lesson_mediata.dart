import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/constants/tracking.dart';
import 'package:meditaguru/src/core/extension.dart';
import 'package:meditaguru/src/core/extensions/duration_ext.dart';
import 'package:meditaguru/src/domain/entities/category_data.dart';

import '../../../di/injection/injection.dart';
import '../../../presentation/shared/account/account_bloc.dart';
import '../../../presentation/song/download_audio/download_audio_model.dart';
import '../../constants.dart';
import '../../services/firebase/analytics/firebase_analytics_wapper.dart';
import '../../utils/string_utils.dart';
import 'easy_debounce.dart';

const _sizeLogo = 43.0;

class CardLessonMeditaWidget extends StatefulWidget {
  final EdgeInsetsGeometry padding;
  final Color primaryColor;
  final Color secondaryColor;
  final String imageBackground;
  final AudioData audio;
  final bool isPaid;
  final CategoryData? category;

  const CardLessonMeditaWidget({
    super.key,
    this.padding = const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
    required this.primaryColor,
    required this.secondaryColor,
    required this.audio,
    required this.imageBackground,
    required this.isPaid,
    this.category,
  });

  @override
  State<CardLessonMeditaWidget> createState() => _CardLessonMeditaWidgetState();
}

class _CardLessonMeditaWidgetState extends State<CardLessonMeditaWidget> {
  String? audioCoverImage;
  AudioData get audio => widget.audio;
  bool _isLoading = false;
  String? _urlFile;
  String? _fileName;

  bool get _isLocked =>
      widget.isPaid && injector<AccountBloc>().isUnLock == false;

  DownloadAudioModel get _downloadAudioModel =>
      context.read<DownloadAudioModel>();

  Stream<double>? get streamPercent {
    if (_downloadAudioModel.isEnableCacheAudio(name: audio.audioName) &&
        _urlFile != null) {
      return _downloadAudioModel.getStreamPercent(audio.audioName, _urlFile!);
    }
    return null;
  }

  void onSongTap() async {
    if (_urlFile?.isEmpty ?? true) {
      await _getUrlFile();

      if (_urlFile?.isEmpty ?? true) {
        return;
      }
    }

    var audioTypes = [
      '.amr',
      '.ogg',
      '.m4a',
      '.3gp',
      '.aac',
      '.mp3',
      '.wav',
      '.flac'
    ];

    var urlFile = _urlFile!;
    var isAudio = false;

    if (StringUtils.isEmpty(_fileName)) {
      urlFile = audio.urlFile ?? '';
      isAudio = audio.typeFile != 1; // 0 : audio - 1: video
    } else {
      audioTypes.forEach((element) async {
        if (_fileName?.contains(element) ?? false) {
          isAudio = true;
          return;
        }
      });
    }

    unawaited(FirebaseAnalyticsWapper()
        .analytics
        .logEvent(name: TrackingEvent.playAudio, parameters: {
      TrackingParameter.audioName: audio.audioName,
      TrackingParameter.audioLength: audio.duration.toString(),
      TrackingParameter.audioCategory: widget.category?.name ?? '',
    }));

    if (isAudio) {
      await context.startSongBackgroundScreen(
        url: urlFile,
        audioName: audio.audioName,
        description: audio.description,
        imageUrl: audioCoverImage,
        backgroundImage: widget.imageBackground,
      );
    } else {
      await context.startVideoScreen(
        url: urlFile,
        videoName: audio.audioName,
        description: audio.description,
        imageUrl: audioCoverImage,
      );
    }
    await FirebaseAnalyticsWapper()
        .analytics
        .logEvent(name: 'Lesson', parameters: {
      'audioName': audio.audioName,
    });
    if (injector<AccountBloc>().isUXCamInit) {
      await FlutterUxcam.logEventWithProperties('Lesson', {
        'audioName': audio.audioName,
      });
    }

    _isLoading = false;
  }

  Future<void> _getUrlFile({bool isShowLocked = true}) async {
    _isLoading = true;

    if (_isLocked) {
      if (isShowLocked) {
        await context.startPayment();
      }
    } else {
      _fileName = await audio.files[0].get().then((documentSnapshot) {
        return documentSnapshot.data()['file'];
      });

      _urlFile = _fileName?.mediaLinkFirebase;
    }

    _isLoading = false;
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    // audioCoverImage = widget.data['audioCoverImageUrl'];
    if (widget.audio.coverImage.isNotEmpty) {
      widget.audio.coverImage[0].get().then((documentSnapshot) {
        setState(() {
          audioCoverImage =
              documentSnapshot.data()['file']?.toString().mediaLinkFirebase;
        });
      });
    }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _getUrlFile(isShowLocked: false);
    });
  }

  @override
  Widget build(BuildContext context) {
    final gradiant = LinearGradient(
      colors: [
        widget.secondaryColor,
        widget.primaryColor,
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );

    return InkWell(
      onTap: () {
        if (_isLoading == false) {
          _isLoading = true;
          EasyDebounce.debounce('debounceActionOpenAudio',
              const Duration(microseconds: 400), onSongTap);
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: widget.padding,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.boxColor,
        ),
        child: Row(
          children: [
            SizedBox(
              height: _sizeLogo,
              width: _sizeLogo,
              child: Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: gradiant,
                    ),
                  ),
                  Positioned.fill(
                    child: Align(
                      child: Container(
                        margin: const EdgeInsets.all(5.0),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: gradiant,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.25),
                              blurRadius: 3,
                              offset: const Offset(0, 1),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  Positioned.fill(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(7.0),
                        child: ClipRRect(
                          child: ImageWidget(
                            widget.imageBackground,
                            borderRadius: 60,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.audio.audioName,
                    style: const TextStyle(
                      color: AppColors.whiteColor,
                      fontSize: 14.0,
                      letterSpacing: 19 / 14,
                    ),
                  ),
                  if (widget.audio.duration != 0)
                    Row(
                      children: [
                        const ImageWidget(
                          IconConstants.clock,
                          height: 16,
                          width: 16,
                          color: Color(0xff98A2B3),
                        ),
                        Text(
                          Duration(seconds: widget.audio.duration).timeText(),
                          style: const TextStyle(
                            color: Color(0xff98A2B3),
                            fontSize: 11.0,
                            letterSpacing: 16 / 11,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            if (_isLocked)
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xffE4CF13).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                padding: const EdgeInsets.all(7),
                child: const ImageWidget(
                  IconConstants.lock,
                  color: Color(0xffE4CF13),
                ),
              )
            else
              ValueListenableBuilder(
                valueListenable: _downloadAudioModel.listenerEnableDownload,
                builder: (_, listEnableDownload, ___) {
                  final enableDownload = _downloadAudioModel.isEnableCacheAudio(
                      name: audio.audioName);

                  /// Listener percent downloaded
                  return StreamBuilder<double?>(
                    stream: streamPercent,
                    key: Key('key_enable_download_$enableDownload'),
                    builder: (context, snapshot) {
                      double? percent;
                      if (snapshot.hasData &&
                          snapshot.data != null &&
                          enableDownload) {
                        percent = snapshot.data;
                      }

                      print('percent-->: $percent ${audio.audioName}');
                      final button = const ImageWidget(
                        IconConstants.play,
                        color: AppColors.grayColor,
                      );

                      if (percent != null) {
                        return Stack(
                          alignment: Alignment.center,
                          children: [
                            CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                percent < 1 ? Colors.blue[600]! : Colors.green,
                              ),
                              strokeWidth: 2,
                              value: percent,
                            ),
                            button,
                          ],
                        );
                      }

                      return button;
                    },
                  );
                },
              )
          ],
        ),
      ),
    );
  }
}
