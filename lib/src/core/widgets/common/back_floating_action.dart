import 'package:flutter/material.dart';

class BackFloatingAction extends StatelessWidget {
  final Function()? onPressed;

  const BackFloatingAction({Key? key, this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onPressed != null) {
          onPressed!();
        } else {
          Navigator.pop(context);
        }
      },
      child: Container(
        margin: const EdgeInsets.only(top: 10),
        width: 29.0,
        height: 29.0,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            begin: Alignment(-0.6, 0.39),
            end: Alignment(1.0, -0.69),
            colors: [Colors.black, Color(0xFF414141)],
          ),
          border: Border.all(
            width: 1.0,
            color: const Color(0xFF707070),
          ),
        ),
        child: const Icon(
          Icons.arrow_back,
          color: Colors.white,
        ),
      ),
    );
  }
}
