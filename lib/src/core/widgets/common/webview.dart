import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/m_appbar.dart';
import 'package:webview_flutter/webview_flutter.dart' as _widget;
import 'package:webview_flutter/webview_flutter.dart';

class WebView extends StatefulWidget {
  final String? url;
  final String? title;

  WebView({Key? key, this.title, required this.url}) : super(key: key);

  @override
  _WebViewState createState() => _WebViewState();

  static Future navigate({
    required BuildContext context,
    required String url,
    required String title,
  }) =>
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => WebView(
                    url: url,
                    title: title,
                  )));
}

class _WebViewState extends State<WebView> {
  bool isLoading = true;

  late final _widget.WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = _widget.WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url ?? ''));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: MAppbar.normal(
        context,
        title: widget.title ?? '',
      ),
      body: Stack(
        children: <Widget>[
          _widget.WebViewWidget(
            controller: _controller,
          ),
          if (isLoading) kLoadingWidget(context),
        ],
      ),
    );
  }
}
