import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';

class ImageLinearGradientWidget extends StatefulWidget {
  final double? width;
  final double? height;
  final String imageUrl;
  final double radius;
  final EdgeInsets paddingContent;
  final Widget child;
  final List<Color>? gradiantColors;
  final List<double> stops;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  final BoxBorder? border;
  final AlignmentGeometry alignment;

  const ImageLinearGradientWidget(
    this.imageUrl, {
    super.key,
    this.width,
    this.height,
    this.radius = 16.0,
    this.paddingContent = const EdgeInsets.all(16.0),
    required this.child,
    this.gradiantColors,
    this.alignment = Alignment.center,
    this.stops = const [0.0, 0.75],
    this.begin = Alignment.bottomCenter,
    this.end = Alignment.topCenter,
    this.border,
  });

  @override
  State<ImageLinearGradientWidget> createState() =>
      _ImageLinearGradientWidgetState();
}

class _ImageLinearGradientWidgetState extends State<ImageLinearGradientWidget> {
  Size? _size;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        setState(() {
          _size = context.size;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final height = widget.height ?? _size?.height;
    final width = widget.width ?? _size?.width;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.radius),
        border: widget.border,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.radius),
        child: SizedBox(
          width: width,
          height: height,
          child: Stack(
            children: [
              Positioned.fill(
                child: ImageWidget(
                  widget.imageUrl,
                  fit: BoxFit.cover,
                  width: width,
                  height: height,
                  alignment: widget.alignment,
                ),
              ),
              Container(
                width: width,
                height: height,
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.all(Radius.circular(widget.radius)),
                  gradient: LinearGradient(
                    stops: widget.stops,
                    begin: widget.begin,
                    end: widget.end,
                    colors: widget.gradiantColors ??
                        [
                          Colors.black,
                          Colors.transparent,
                        ],
                  ),
                ),
                child: Padding(
                  padding: widget.paddingContent,
                  child: widget.child,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
