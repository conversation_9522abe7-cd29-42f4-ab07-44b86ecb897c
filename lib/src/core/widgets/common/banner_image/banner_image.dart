import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/extensions/date_time_extension.dart';
import 'package:meditaguru/src/core/extensions/int_extension.dart';
import 'package:meditaguru/src/core/widgets/common/banner_image/full_banner_image.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

class BannerImage extends StatelessWidget {
  const BannerImage({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.sizeOf(context).width.toInt();
    final backgroundColor = Theme.of(context).colorScheme.surface;
    final bannerIndex = DateTime.now().dayOfYear.bannerIndex;

    return GestureDetector(
      onTap: () {
        context.startScreen(FullBannerImage(bannerIndex));
      },
      child: Container(
        foregroundDecoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              backgroundColor.withOpacity(0.6),
              backgroundColor.withOpacity(0.2),
              backgroundColor.withOpacity(0.1),
              backgroundColor.withOpacity(0.6),
            ],
            stops: [
              -0.2,
              0.2,
              0.8,
              1.2,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ExtendedImage.asset(
          bannerIndex.todayBannerImage(),
          cacheWidth: width * 2,
        ),
      ),
    );
  }
}
