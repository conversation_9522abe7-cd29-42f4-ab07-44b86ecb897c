import 'dart:math';

import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class BannerImageRegisterPremiumWidget extends StatelessWidget {
  const BannerImageRegisterPremiumWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // SizedBox(
        //   height: MediaQuery.sizeOf(context).height * 0.4,
        //   child: const ImageWidget('assets/img/3x/moon_sleep.png'),
        // ),
        Center(
            child: GradientText(
          '2225',
          textAlign: TextAlign.center,
          colors: [
            const Color(0xffECF939),
            const Color(0xffD68D00),
          ],
          style: const TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.w600,
            color: AppColors.secondary2Color,
          ),
        )),
        const Center(
          child: Text(
            '<PERSON><PERSON>t thiền chất lượng',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w600,
              color: AppColors.primary4Color,
            ),
          ),
        ),
        const SizedBox(height: 16),
        const Center(
          child: Text(
            'Mở khoá tất cả bài thiền nâng cao \nđể nhận được trọn vẹn nhất\nlợi lạc của sự thư giãn và tỉnh thức.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.subTitleColor,
            ),
          ),
        ),
        const SizedBox(height: 24),
        Center(
          child: SizedBox(
            width: min(MediaQuery.sizeOf(context).width * 0.6, 300),
            child: ButtonGradiantWidget.primary(
              onPressed: context.startPayment,
              title: 'Khám phá ngay',
            ),
          ),
        ),
      ],
    );
  }
}
