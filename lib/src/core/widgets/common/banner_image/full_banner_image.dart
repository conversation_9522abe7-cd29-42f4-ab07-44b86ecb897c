import 'dart:async';
import 'dart:math';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../services/url_launcher_service.dart';

class FullBannerImage extends StatefulWidget {
  final int index;

  const FullBannerImage(this.index);

  @override
  State<FullBannerImage> createState() => _FullBannerImageState();
}

class _FullBannerImageState extends State<FullBannerImage>
    with SingleTickerProviderStateMixin {
  late final ExtendedPageController _pageController;
  // late final PageController _smoothPageController;
  StreamController<int> rebuildIndex = StreamController<int>.broadcast();
  StreamController<bool> rebuildSwiper = StreamController<bool>.broadcast();
  AnimationController? _animationController;
  Animation<double>? _animation;
  VoidCallback? animationListener;

  List<double> doubleTapScales = <double>[1.0, 2.0];

  late int currentIndex;
  bool _showSwiper = true;

  @override
  void initState() {
    super.initState();
    currentIndex = widget.index;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _pageController = ExtendedPageController(
      initialPage: currentIndex,
      pageSpacing: 4,
    );
  }

  @override
  void dispose() {
    if (animationListener != null) {
      _animation?.removeListener(animationListener!);
    }
    rebuildIndex.close();
    rebuildSwiper.close();
    _animationController?.dispose();
    _pageController.dispose();
    clearGestureDetailsCache();
    //cancelToken?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black,
      shadowColor: Colors.transparent,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return ExtendedImageSlidePage(
            onSlidingPage: (state) {
              final showSwiper = !state.isSliding;
              if (showSwiper != _showSwiper) {
                _showSwiper = showSwiper;
                rebuildSwiper.add(_showSwiper);
              }
            },
            child: Stack(
              fit: StackFit.expand,
              children: <Widget>[
                ExtendedImageGesturePageView.builder(
                  itemBuilder: (BuildContext context, int index) {
                    final thumbnailImage = index.todayFullBannerImage();
                    Widget image = ExtendedImage.asset(
                      thumbnailImage,
                      fit: BoxFit.contain,
                      enableSlideOutPage: true,
                      mode: ExtendedImageMode.gesture,
                      loadStateChanged: (ExtendedImageState state) {
                        switch (state.extendedImageLoadState) {
                          case LoadState.loading:

                            /// Use thumbnail image has loaded as loading image
                            /// Waiting the larger image show
                            debugPrint(
                              'loading thumbnailImage: $thumbnailImage',
                            );
                            return ExtendedImage.network(thumbnailImage);
                          case LoadState.completed:
                            return state.completedWidget;
                          case LoadState.failed:
                            return ColoredBox(
                              color: Theme.of(context).colorScheme.surface,
                              child: const Center(
                                child: Icon(Icons.error),
                              ),
                            );
                        }
                      },
                      initGestureConfigHandler: (state) {
                        double? initialScale = 1.0;

                        if (state.extendedImageInfo != null) {
                          initialScale = _initalScale(
                            size: Size(
                              constraints.maxWidth,
                              constraints.maxHeight,
                            ),
                            initialScale: initialScale,
                            imageSize: Size(
                              state.extendedImageInfo!.image.width.toDouble(),
                              state.extendedImageInfo!.image.height.toDouble(),
                            ),
                          );
                        }

                        return GestureConfig(
                          inPageView: true,
                          minScale: 1.0,
                          initialScale: initialScale!,
                          maxScale: max(initialScale, 5.0),
                          animationMaxScale: max(initialScale, 5.0),
                        );
                      },
                      onDoubleTap: (ExtendedImageGestureState state) {
                        final pointerDownPosition = state.pointerDownPosition;
                        final begin = state.gestureDetails!.totalScale;
                        double end;

                        //remove old
                        _animation?.removeListener(
                          animationListener as void Function(),
                        );

                        //stop pre
                        _animationController!.stop();

                        //reset to use
                        _animationController!.reset();

                        end = begin == doubleTapScales[0]
                            ? doubleTapScales[1]
                            : doubleTapScales[0];

                        animationListener = () {
                          //print(_animation.value);
                          state.handleDoubleTap(
                            scale: _animation!.value,
                            doubleTapPosition: pointerDownPosition,
                          );
                        };
                        _animation = _animationController!
                            .drive(Tween<double>(begin: begin, end: end));

                        _animation!.addListener(
                          animationListener as void Function(),
                        );

                        _animationController!.forward();
                      },
                    );

                    return ExtendedImageSlidePage(
                      onSlidingPage: (state) {
                        final showSwiper = !state.isSliding;
                        if (showSwiper != _showSwiper) {
                          _showSwiper = showSwiper;
                          rebuildSwiper.add(_showSwiper);
                        }
                      },
                      child: image,
                    );
                  },
                  itemCount: kBannerImageCount,
                  onPageChanged: (int index) {
                    currentIndex = index;
                    rebuildIndex.add(index);
                  },
                  controller: _pageController,
                  //physics: ClampingScrollPhysics(),
                ),
                Align(
                  alignment: const Alignment(0, 0.98),
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: ListenableBuilder(
                        listenable: _pageController,
                        builder: (_, __) {
                          return AnimatedSmoothIndicator(
                            activeIndex:
                                _pageController.page?.round() ?? currentIndex,
                            count: kBannerImageCount,
                            effect: ScrollingDotsEffect(
                              spacing: 8.0,
                              radius: 5.0,
                              dotWidth: 6.0,
                              dotHeight: 6.0,
                              paintStyle: PaintingStyle.fill,
                              strokeWidth: 1.5,
                              dotColor: Colors.black12,
                              activeDotColor: Theme.of(context).primaryColor,
                            ),
                          );
                        }),
                  ),
                ),
                Positioned(
                  top: 36,
                  right: 12,
                  child: StreamBuilder<Object>(
                    stream: rebuildIndex.stream,
                    initialData: currentIndex,
                    builder: (context, snapshot) {
                      return IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          context.pop(snapshot.data);
                        },
                      );
                    },
                  ),
                ),
                Align(
                  alignment: const Alignment(0, 0.89),
                  child: GestureDetector(
                    onTap: () {
                      UrlLauncherService.launchURL(
                        Urls.hdsdTamMinh,
                        mode: LaunchMode.inAppWebView,
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .scaffoldBackgroundColor
                                .withOpacity(0.8),
                            // color: Colors.black.withOpacity(0.2),
                            blurRadius: 24,
                            spreadRadius: 2,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Image.asset(
                        'assets/img/hdsd_tam_minh.png',
                        // width: 100,
                        height: 125,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  bool? defaultSlideEndHandler(
    Offset offset, {
    required ExtendedImageSlidePageState state,
    // required ScaleEndDetails details,
  }) {
    if (offset.distance > 200.0) {
      state.context.pop();
    }

    return true;
  }

  double? _initalScale({
    required Size imageSize,
    required Size size,
    double? initialScale,
  }) {
    final n1 = imageSize.height / imageSize.width;
    final n2 = size.height / size.width;
    if (n1 > n2) {
      final fittedSizes = applyBoxFit(BoxFit.contain, imageSize, size);
      //final Size sourceSize = fittedSizes.source;
      final destinationSize = fittedSizes.destination;

      return size.width / destinationSize.width;
    } else if (n1 / n2 < 1 / 4) {
      final fittedSizes = applyBoxFit(BoxFit.contain, imageSize, size);
      //final Size sourceSize = fittedSizes.source;
      final destinationSize = fittedSizes.destination;

      return size.height / destinationSize.height;
    }

    return initialScale;
  }
}
