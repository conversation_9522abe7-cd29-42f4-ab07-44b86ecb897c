import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';

class CardContentHomeWidget extends StatelessWidget {
  final EdgeInsetsGeometry? padding;
  final DailyInspirationData card;
  final Color? backgroundColor;

  CardContentHomeWidget({
    super.key,
    this.padding,
    required this.card,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: backgroundColor,
        ),
        child: Stack(
          children: [
            Padding(
              padding: padding ?? EdgeInsets.zero,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.min,
                children: [
                  ConstrainedBox(
                    constraints: const BoxConstraints(
                      minHeight: 100,
                      maxHeight: 180,
                    ),
                    child: SingleChildScrollView(
                      child: Text(
                        '"${card.content}"',
                        style: const TextStyle(
                          decoration: TextDecoration.none,
                          fontSize: 18.0,
                          fontWeight: FontWeight.w400,
                          color: Colors.white,
                          fontFamily: 'MyFont3',
                          fontStyle: FontStyle.italic,
                          letterSpacing: 0.5,
                          height: 1.25,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    '___ ${card.author}',
                    textAlign: TextAlign.left,
                    style: const TextStyle(
                      decoration: TextDecoration.none,
                      fontSize: 17.0,
                      fontWeight: FontWeight.w400,
                      color: AppColors.secondary7Color,
                      fontFamily: 'MyFont3',
                      fontStyle: FontStyle.italic,
                      letterSpacing: 0.5,
                      height: 1.25,
                    ),
                  ),
                ],
              ),
            ),
            if (backgroundColor != null) ...[
              const Positioned(
                bottom: 0,
                left: 0,
                child: ImageWidget(IconConstants.moon),
              ),
              const Positioned(
                bottom: 0,
                right: 0,
                child: Opacity(
                  opacity: 0.3,
                  child: ImageWidget(IconConstants.lotus),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
