import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/widgets/common/appbar_custom.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';

class BreathingExerciseLayout extends StatelessWidget {
  const BreathingExerciseLayout({
    super.key,
    required this.body,
    required this.title,
    this.onBack,
  });

  final Widget body;
  final String title;
  final VoidCallback? onBack;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Container(color: Colors.black),
        ),
        Positioned.fill(
          child: Container(color: const Color.fromARGB(0, 211, 210, 210)),
        ),
        const Positioned.fill(
          child: ImageWidget('assets/img/bg_breathing_02.png'),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBarCustom(
            title: Text(title),
            showLabel: false,
            centerTitle: true,
            overrideOnBack: () async {
              onBack?.call();
              Navigator.of(context).pop();
            },
          ),
          body: body,
        ),
      ],
    );
  }
}
