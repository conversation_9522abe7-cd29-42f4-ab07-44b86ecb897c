import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/presentation/shared/daily_inspiration/daily_inspiration_model.dart';

class DrawCardWidget extends StatelessWidget {
  const DrawCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final model = context.read<DailyInspirationModel>();

    return AnimatedBuilder(
      animation: model,
      builder: (context, child) {
        if (model.canGetCard) {
          return CardDrawCardWidget(
            onPressed: () {
              context.startChooseCardScreen();
            },
          );
        }

        return CardContentHomeWidget(
          backgroundColor: AppColors.yellow80,
          padding: const EdgeInsets.all(16),
          card: model.cardSelected!.card,
        );
      },
    );
  }
}
