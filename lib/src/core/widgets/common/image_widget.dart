import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';

class ImageWidget extends StatefulWidget {
  static String? packageDefault;
  static ErrorWidgetBuilder errorWidgetBuilder = () => null;

  final String source;
  final BoxFit fit;
  final double? width;
  final double? height;
  final bool usePlaceHolder;
  final Color? color;
  final double? borderRadius;
  final String? package;
  final int? cacheWidth;
  final int? cacheHeight;
  final double aspectRatio;
  final bool useFadeInAnimation;
  final Widget? errorWidget;
  final AlignmentGeometry alignment;

  const ImageWidget(
    this.source, {
    Key? key,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.usePlaceHolder = true,
    this.color,
    this.borderRadius,
    this.package,
    this.cacheWidth,
    this.cacheHeight,
    this.aspectRatio = 2.0,
    this.useFadeInAnimation = true,
    this.errorWidget,
    this.alignment = Alignment.center,
  }) : super(key: key);

  const ImageWidget.avatar(
    this.source, {
    Key? key,
    this.fit = BoxFit.cover,
    this.width = 40,
    this.height = 40,
    this.usePlaceHolder = true,
    this.color,
    this.borderRadius = 10,
    this.package,
    this.cacheWidth,
    this.cacheHeight,
    this.aspectRatio = 2.0,
    this.useFadeInAnimation = true,
    this.alignment = Alignment.center,
    this.errorWidget,
  }) : super(key: key);

  Widget copyWith(
      {Color? color,
      double? width,
      double? height,
      AlignmentGeometry? alignment}) {
    return ImageWidget(
      source,
      key: key,
      fit: fit,
      width: width ?? this.width,
      height: height ?? this.height,
      usePlaceHolder: usePlaceHolder,
      color: color ?? this.color,
      borderRadius: borderRadius,
      alignment: alignment ?? this.alignment,
    );
  }

  @override
  State<ImageWidget> createState() => _ImageWidgetState();
}

class _ImageWidgetState extends State<ImageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  dynamic _getFile(String source) => File(source);
  @override
  void initState() {
    _controller = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 300),
        lowerBound: 0.0,
        upperBound: 1.0);
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget body;

    if (widget.source.isEmpty) {
      body = const Placeholder();
    } else if (widget.source.contains('.svg')) {
      body = widget.source.contains('http')
          ? SvgPicture.network(
              widget.source,
              fit: widget.fit,
              colorFilter: widget.color != null
                  ? ColorFilter.mode(widget.color!, BlendMode.srcIn)
                  : null,
              width: widget.width,
              height: widget.height,
              alignment: widget.alignment,
            )
          : SvgPicture.asset(
              widget.source,
              fit: widget.fit,
              colorFilter: widget.color != null
                  ? ColorFilter.mode(widget.color!, BlendMode.srcIn)
                  : null,
              width: widget.width,
              height: widget.height,
              alignment: widget.alignment,
              package: widget.package ?? ImageWidget.packageDefault,
            );
    } else if (widget.source.contains('http')) {
      body = ExtendedImage.network(
        widget.source,
        cache: true,
        fit: widget.fit,
        color: widget.color,
        loadStateChanged: loadStateChanged,
        alignment: widget.alignment,
        cacheWidth: widget.cacheWidth,
        cacheHeight: widget.cacheHeight,
        width: widget.width ?? MediaQuery.sizeOf(context).width,
        height: widget.height ?? MediaQuery.sizeOf(context).height,
      );
    } else if (widget.source.startsWith('/') ||
        widget.source.startsWith('file://') ||
        widget.source.substring(1).startsWith(':\\')) {
      body = ExtendedImage.file(
        _getFile(widget.source),
        fit: widget.fit,
        color: widget.color,
        width: widget.width,
        alignment: widget.alignment,
        height: widget.height,
        loadStateChanged: loadStateChanged,
      );
    } else if (widget.source.contains('.json')) {
      return Lottie.asset(
        widget.source,
        package: widget.package ?? ImageWidget.packageDefault,
        width: widget.width,
        alignment: widget.alignment,
        height: widget.height,
        fit: widget.fit,
      );
    } else {
      body = Image.asset(
        widget.source,
        fit: widget.fit,
        color: widget.color,
        width: widget.width,
        height: widget.height,
        alignment: widget.alignment,
        package: widget.package ?? ImageWidget.packageDefault,
      );
    }
    if (widget.borderRadius != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius!),
        child: body,
      );
    }

    return body;
  }

  Widget? loadStateChanged(ExtendedImageState state) {
    late Widget image;

    switch (state.extendedImageLoadState) {
      case LoadState.loading:
        _controller.reset();

        return Skeleton(
          forceTheme: Brightness.dark,
          width: widget.width ?? MediaQuery.sizeOf(context).width,
          height: widget.height ?? MediaQuery.sizeOf(context).height,
        );

      case LoadState.completed:
        _controller.forward();

        if (widget.useFadeInAnimation) {
          image = FadeTransition(
              opacity: _controller,
              child: ExtendedRawImage(
                image: state.extendedImageInfo?.image,
                width: widget.width,
                alignment: widget.alignment,
                height: widget.height,
                fit: widget.fit,
              ));
        } else {
          image = ExtendedRawImage(
            image: state.extendedImageInfo?.image,
            width: widget.width,
            alignment: widget.alignment,
            height: widget.height,
            fit: widget.fit,
          );
        }
        break;
      case LoadState.failed:
        _controller.reset();
        if (widget.errorWidget != null) {
          image = widget.errorWidget!;
        } else {
          final errorWidget = ImageWidget.errorWidgetBuilder();
          if (errorWidget != null) {
            image = SizedBox(
              width: widget.width,
              height: widget.height,
              child: errorWidget,
            );
          } else {
            image = Container(
              width: widget.width,
              height: widget.height,
              color: Colors.grey,
            );
          }
        }
        break;
    }
    return image;
  }
}

typedef ErrorWidgetBuilder = Widget? Function();

class Skeleton extends StatefulWidget {
  final double height;
  final double width;
  final double cornerRadius;
  final Brightness? forceTheme;

  const Skeleton({
    super.key,
    this.height = 20,
    this.width = 200,
    this.cornerRadius = 4,
    this.forceTheme,
  });

  @override
  SkeletonState createState() => SkeletonState();
}

class SkeletonState extends State<Skeleton>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;

  late Animation gradientPosition;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        duration: const Duration(milliseconds: 1500), vsync: this);

    gradientPosition = Tween<double>(
      begin: -3,
      end: 10,
    ).animate(
      CurvedAnimation(parent: _controller!, curve: Curves.linear),
    )..addListener(() {
        setState(() {});
      });

    _controller!.repeat();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme =
        (widget.forceTheme ?? Theme.of(context).brightness) == Brightness.dark;

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(widget.cornerRadius),
          gradient: LinearGradient(
              begin: Alignment(gradientPosition.value, 0),
              end: const Alignment(-1, 0),
              colors: isDarkTheme
                  ? [
                      const Color.fromARGB(102, 95, 91, 91),
                      const Color.fromARGB(153, 103, 98, 98),
                      const Color.fromARGB(102, 99, 97, 97)
                    ]
                  : [
                      const Color(0x0D000000),
                      const Color(0x1A000000),
                      const Color(0x0D000000)
                    ])),
    );
  }
}
