import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';
import 'package:meditaguru/src/core/widgets/common/tag_widget.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../../constants/icons.dart';
import '../../lib_common.dart';

const _heightCard = 140.0;
const _maxWidthCard = 400.0;
const _widthImage = 110.0;
const _scaleImage = 1.8;

class CardBreathingExcercisesWidget extends StatelessWidget {
  final int timeMinute;
  final String imageUrl;

  const CardBreathingExcercisesWidget({
    super.key,
    required this.timeMinute,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    final style = const TextStyle(
      fontSize: 14.0,
      color: Colors.white,
      height: 19 / 14,
      fontWeight: FontWeight.w400,
    );
    return GestureDetector(
      onTap: context.startBreathingExerciseIntro,
      behavior: HitTestBehavior.translucent,
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          bottomLeft: Radius.circular(12),
        ),
        child: Container(
          constraints: const BoxConstraints(maxWidth: _maxWidthCard),
          height: _heightCard,
          decoration: const BoxDecoration(color: AppColors.grey60),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      UnconstrainedBox(
                        child: TagWidget(
                          iconPath: IconConstants.music02,
                          text: '$timeMinute Phút',
                          colorTag: AppColors.whiteColor.withOpacity(0.6),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 6),
                        ),
                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: RichText(
                            text: TextSpan(
                              children: [
                                WidgetSpan(
                                  child: GradientText(
                                    'Bài hướng dẫn tập thở',
                                    style: style,
                                    colors: [
                                      const Color(0xffFF842A),
                                      const Color(0xffF8C614),
                                    ],
                                  ),
                                  alignment: PlaceholderAlignment.middle,
                                ),
                                TextSpan(
                                  text:
                                      ' giúp giảm căng thẳng, giữ cơ thể dẻo dai',
                                  style: style,
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 25),
              SizedBox(
                width: _widthImage,
                child: Transform.scale(
                  scale: _scaleImage,
                  alignment: Alignment.center,
                  child: SizedBox(
                    width: _widthImage,
                    child: ImageWidget(
                      imageUrl,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
