import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/constants/icons.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';

const _heightCard = 100.0;
const _stopsGradiant = [
  0.01,
  1.0,
];

class MeditationDetailWidget extends StatelessWidget {
  const MeditationDetailWidget({
    super.key,
    required this.title,
    required this.gradiantPrimaryColor,
    required this.gradiantSecondaryColor,
    this.onTap,
  });
  final String title;
  final Color gradiantPrimaryColor;
  final Color gradiantSecondaryColor;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap?.call(),
      child: Container(
        height: _heightCard,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              gradiantSecondaryColor.withOpacity(0.2),
              gradiantPrimaryColor.withOpacity(0.8),
            ],
            stops: _stopsGradiant,
            begin: Alignment.bottomRight,
            end: Alignment.topLeft,
          ),
        ),
        child: <PERSON>ack(
          children: [
            const Align(
              child: ImageWidget(IconConstants.circleTopRight),
              alignment: Alignment.bottomLeft,
            ),
            const Align(
              child: ImageWidget(IconConstants.circleBottomLeft),
              alignment: Alignment.topRight,
            ),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
