import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';

import '../../utils.dart';

class EmptyResultWidget extends StatelessWidget {
  final Function()? onTap;
  final String? title;

  const EmptyResultWidget({Key? key, this.onTap, this.title}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 25.w),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Tools.image(
                    url: StringUtils.getAssetPath('images/not_found.png'),
                    width: 180.w,
                    height: 180.w),
                <PERSON>lock(32),
                CText.title2(
                  title ?? S.of(context).notFound,
                  color: AppColors.contentLightGreyColor,
                  fontWeight: FontWeight.w400,
                  textAlign: TextAlign.center,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
