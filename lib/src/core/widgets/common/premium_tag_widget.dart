import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/constants/icons.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

import 'button_gradiant_widget.dart';
import 'image_widget.dart';

class PremiumTagWidget extends StatelessWidget {
  const PremiumTagWidget({
    super.key,
    required this.isPaid,
  });
  final bool isPaid;

  @override
  Widget build(BuildContext context) {
    if (isPaid) {
      return ButtonGradiantWidget.secondary02(
        title: 'Premium',
        onPressed: () {},
      );
    }

    return ButtonGradiantWidget.icon(
      const ImageWidget(IconConstants.lock),
      title: 'Mở khoá và thiền ngay',
      onPressed: context.startPayment,
    );
  }
}
