import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';

class TakeADeepBreathWidget extends StatefulWidget {
  const TakeADeepBreathWidget({Key? key, this.loadAnimationDone})
      : super(key: key);

  @override
  _TakeADeepBreathWidgetState createState() => _TakeADeepBreathWidgetState();

  final Function()? loadAnimationDone;
}

class _TakeADeepBreathWidgetState extends State<TakeADeepBreathWidget>
    with TickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> _animation;

  late AnimationController controller2;
  late Animation<double> _animation2;

  @override
  void initState() {
    super.initState();
    controller = AnimationController(
        duration: const Duration(milliseconds: 3000), vsync: this);
    _animation = Tween<double>(begin: 1, end: 0.4).animate(controller)
      ..addStatusListener(
        (status) {
          if (status == AnimationStatus.completed) {
            if (widget.loadAnimationDone != null) {
              // widget.loadAnimationDone!();
              // controller2.forward();
            }
          }
        },
      );

    controller2 = AnimationController(
        duration: const Duration(milliseconds: 6000), vsync: this);
    _animation2 = Tween<double>(begin: 0.5, end: 1).animate(controller2)
      ..addStatusListener(
        (status) {
          if (status == AnimationStatus.completed) {
            if (widget.loadAnimationDone != null) {
              widget.loadAnimationDone!();
            }
          }
        },
      );

    controller.forward();
    controller2.forward();
  }

  Future<void> showLoading() async {
    controller.reset();
    await controller.forward();

    controller2.reset();
    await controller2.forward();
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      body: InkWell(
        onTap: () {
          widget.loadAnimationDone?.call();
        },
        child: AnimatedSwitcher(
          duration: const Duration(seconds: 1),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedBuilder(
                  animation: _animation,
                  builder: (BuildContext context, Widget? child) {
                    return Text(
                      S.of(context).takeADeepBreath,
                      style: TextStyle(
                        fontFamily: 'MyFont2',
                        fontSize: 25.0,
                        color: Colors.white.withOpacity(_animation.value),
                        // height: 2.35,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 30),
                AnimatedBuilder(
                  animation: _animation2,
                  builder: (BuildContext context, Widget? child) {
                    return Text(
                      S.of(context).breatheOutGently,
                      style: TextStyle(
                        fontFamily: 'MyFont2',
                        fontSize: 25.0,
                        color: Colors.white.withOpacity(_animation2.value),
                        // height: 2.35,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    controller.dispose();
    controller2.dispose();
    super.dispose();
  }
}
