import 'package:flutter/material.dart';

const ColorFilter _greyscale = ColorFilter.matrix(<double>[
  0.2126, 0.7152, 0.0722, 0, 0, //
  0.2126, 0.7152, 0.0722, 0, 0, //
  0.2126, 0.7152, 0.0722, 0, 0, //
  0, 0, 0, 1, 0, //
]);

class DisableColorWidget extends StatelessWidget {
  const DisableColorWidget({
    super.key,
    this.disable = true,
    required this.child,
  });

  final bool disable;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    if (disable) {
      return ColorFiltered(
        colorFilter: _greyscale,
        child: child,
      );
    }

    return child;
  }
}
