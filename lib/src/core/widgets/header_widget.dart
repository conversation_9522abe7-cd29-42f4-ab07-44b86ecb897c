import 'package:flutter/material.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({
    Key? key,
    required this.text,
    this.padding = EdgeInsets.zero,
    this.maxLines = 2,
  }) : super(key: key);

  final String text;
  final EdgeInsets padding;
  final int maxLines;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.w600,
          height: 24 / 19,
        ),
        maxLines: maxLines,
      ),
    );
  }
}
