// scaffold chứa các loại safearea và background , kèm loading cho mỗi màn hình có chứa bloc loading
// hide keyboard

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/m_loading.dart';
import 'package:meditaguru/src/core/widgets/common/auto_hide_keyboard.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';

class MScaffoldCus extends Scaffold {
  MScaffoldCus({Key? key}) : super(key: key);

  MScaffoldCus.normal({
    Key? key,
    PreferredSizeWidget? appBar,
    Widget? body,
    Color backgroundColor = AppColors.backgroundColor,
  }) : super(
          key: key,
          appBar: appBar,
          body: body,
          backgroundColor: backgroundColor,
        );
}

class MScaffold extends StatelessWidget {
  final Widget body;
  final Color? backgroundColor;
  final Color? backgroundAppColor;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? appbar;
  final AppBar? appBarMaterial;
  final Widget? floatingActionButton;
  final bool resizeToAvoidBottom;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final bool safeAreaTop;
  final bool safeAreaBottom;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final String? backgroundImage;
  final AlignmentGeometry alignmentBackgroundImage;
  final BoxFit fitBackgroundImage;
  final bool useLoading;

  /// Only use background color when backgroundColor isn't null.
  final bool onlyUseBgColor;

  const MScaffold({
    Key? key,
    required this.body,
    this.backgroundColor,
    this.useLoading = true,
    this.bottomNavigationBar,
    this.drawer,
    this.appbar,
    this.resizeToAvoidBottom = true,
    this.floatingActionButton,
    this.appBarMaterial,
    this.backgroundAppColor,
    this.scaffoldKey,
    this.safeAreaTop = true,
    this.safeAreaBottom = true,
    this.floatingActionButtonLocation,
    this.backgroundImage,
    this.alignmentBackgroundImage = Alignment.topCenter,
    this.fitBackgroundImage = BoxFit.cover,
    this.onlyUseBgColor = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.sizeOf(context).height * 2;
    var width = MediaQuery.sizeOf(context).width * 2;

    return Stack(
      children: [
        Container(
          width: double.infinity,
          height: double.infinity,
          child: SafeArea(
            top: safeAreaTop,
            bottom: safeAreaBottom,
            child: Scaffold(
              key: scaffoldKey,
              appBar: appBarMaterial,
              backgroundColor: backgroundColor ?? Colors.black,
              resizeToAvoidBottomInset: resizeToAvoidBottom,
              body: Stack(
                children: [
                  if (backgroundColor == null || onlyUseBgColor == false)
                    ImageWidget(
                      backgroundImage ?? ImageConstants.backgroundHome,
                      width: width,
                      height: height,
                      alignment: alignmentBackgroundImage,
                      fit: fitBackgroundImage,
                    ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      if (appbar != null) appbar!,
                      Expanded(
                        child: AutoHideKeyboard(child: body),
                      )
                    ],
                  ),
                ],
              ),
              bottomNavigationBar: bottomNavigationBar,
              drawer: drawer,
              floatingActionButton: floatingActionButton,
              floatingActionButtonLocation: floatingActionButtonLocation,
            ),
          ),
        ),
        if (useLoading) CusLoadingStream(injector<LoadingBloc>())
      ],
    );
  }
}

class MScaffoldPage extends StatelessWidget {
  final Widget body;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? appbar;
  final AppBar? appBarMaterial;
  final Widget? floatingActionButton;
  final bool resizeToAvoidBottom;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final bool safeAreaTop;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final String? backgroundImage;

  const MScaffoldPage({
    Key? key,
    required this.body,
    this.bottomNavigationBar,
    this.drawer,
    this.appbar,
    this.appBarMaterial,
    this.floatingActionButton,
    this.resizeToAvoidBottom = true,
    this.scaffoldKey,
    this.safeAreaTop = true,
    this.floatingActionButtonLocation,
    this.backgroundImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.sizeOf(context).height * 2;
    var width = MediaQuery.sizeOf(context).width * 2;
    return Scaffold(
      key: scaffoldKey,
      appBar: appBarMaterial,
      // resizeToAvoidBottomPadding: resizeToAvoidBottom,
      resizeToAvoidBottomInset: resizeToAvoidBottom,
      // backgroundColor: Colors.transparent,
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          ImageWidget(
            // Path 660
            backgroundImage ?? ImageConstants.backgroundHome,
            width: width,
            alignment: Alignment.topCenter,
            height: height,
            fit: BoxFit.cover,
          ),
          SafeArea(
            top: safeAreaTop,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                if (appbar != null) appbar!,
                Expanded(
                  child: AutoHideKeyboard(child: body),
                )
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
    );
  }
}
