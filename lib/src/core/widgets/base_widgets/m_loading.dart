import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:meditaguru/src/presentation/song/riple_circle/ripple_animation.dart';

import '../../utils.dart';

class CusLoading extends StatelessWidget {
  final bool isLoading;

  CusLoading(this.isLoading);

  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.sizeOf(context).height * 2;
    var width = MediaQuery.sizeOf(context).width * 2;
    return AnimatedSwitcher(
      duration: const Duration(seconds: 2),
      child: isLoading
          ? Stack(
              children: <Widget>[
                SvgPicture.string(
                  SvgString.backgroundGradiant,
                  width: width,
                  height: height,
                  fit: BoxFit.cover,
                  colorFilter: const ColorFilter.mode(
                    Colors.transparent,
                    BlendMode.color,
                  ),
                ),
                Center(
                    child: Container(
                  child: RipplesAnimation(
                    size: 180,
                    color: AppColors.secondaryColor,
                    child: Container(
                      width: 92.0,
                      height: 92.0,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryColor,
                        image: DecorationImage(
                          image: AssetImage(
                              StringUtils.getAssetPath('img/logoWhite.png')),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                )
                    // kLoadingWhiteWidget(context)
                    // CircularProgressIndicator(backgroundColor: AppColors.backgroundColor,)
                    )
              ],
            )
          : Container(),
    );
  }
}

class CusLoadingAnim extends StatefulWidget {
  final bool isLoading;

  const CusLoadingAnim({Key? key, this.isLoading = false}) : super(key: key);

  @override
  _CusLoadingAnimState createState() => _CusLoadingAnimState();
}

class _CusLoadingAnimState extends State<CusLoadingAnim>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 5000),
      vsync: this,
    );
    _controller.addStatusListener((AnimationStatus status) {
      if (status == AnimationStatus.completed && widget.isLoading) {
        _controller.repeat();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      _controller.forward();
    }
    return widget.isLoading
        ? Stack(
            children: <Widget>[
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.black12.withOpacity(0.4),
              ),
              Center(
                  child: RotationTransition(
                turns: Tween(begin: 0.0, end: pi).animate(_controller),
                child: const Icon(
                  FontAwesomeIcons.spinner,
                  color: AppColors.whiteColor,
                  size: 40,
                ),
              ))
            ],
          )
        : Container();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class CusLoadingStream extends StatelessWidget {
  final LoadingBloc loadingBloc;

  CusLoadingStream(this.loadingBloc);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: loadingBloc.loadingController,
      initialData: false,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data == true) {
          // return CusLoadingAnim(isLoading: snapshot.data);
          return CusLoading(true);
        } else {
          return Container();
        }
      },
    );
  }
}
