import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class LoginCircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    // path start with (0.0, 0.0) point
    var path = Path()
      ..lineTo(0.0, size.height - 50.h)
      ..quadraticBezierTo(
          size.width / 2, size.height, size.width, size.height - 130.h)
      ..lineTo(size.width, 0.0);
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}

class HomeCircleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    // path start with (0.0, 0.0) point
    var path = Path()
      ..lineTo(0.0, size.height - 50.h)
      ..quadraticBezierTo(
          size.width / 2, size.height, size.width, size.height - 80.h)
      ..lineTo(size.width, 0.0);
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}
