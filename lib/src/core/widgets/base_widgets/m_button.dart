// các lo<PERSON>i button : flat, ........

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/m_text.dart';
import 'package:meditaguru/src/core/widgets/common/text_background.dart';

class MButtonGradientCircle extends StatelessWidget {
  final Function()? onTap;
  final Widget? child;

  const MButtonGradientCircle({Key? key, this.onTap, this.child})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 55,
        width: 55,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          gradient: Style.linearGradient2(),
        ),
        child: Container(
          decoration: BoxDecoration(
            // // color: Colors.black54,
            // gradient: LinearGradient(
            //     begin: Alignment.topCenter,
            //     end: Alignment.bottomCenter,
            //     colors: [Colors.black54, Colors.black26]
            // ),
            borderRadius: BorderRadius.circular(50),
          ),
          child: Center(
            child:
                child ?? const Icon(Icons.arrow_forward, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class MButtonGradient extends StatelessWidget {
  final Function()? onTap;
  final String? title;

  const MButtonGradient({Key? key, this.onTap, this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(gradient: Style.linearGradient2()),
        // padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            TextBackground(
              circle: 0,
              isFitWidth: false,
              child: CText.body(
                title ?? '',
                color: AppColors.whiteColor,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class CButton extends StatelessWidget {
  final String title;
  final Function()? onTap;
  final bool showIconRight;
  final double? height;
  final Color? titleColor;
  final Color? backgroundColor;
  final bool enabled;

  CButton(
    this.title, {
    Key? key,
    this.onTap,
    this.showIconRight = false,
    this.height,
    this.titleColor,
    this.backgroundColor,
    this.enabled = true,
  }) : super(key: key);

  CButton.negative(
    this.title, {
    Key? key,
    this.onTap,
    this.showIconRight = false,
    this.height,
    this.titleColor = AppColors.kDarkBG,
    this.backgroundColor = AppColors.backgroundButton,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var _titleColor = titleColor;
    if (enabled == false) {
      _titleColor = AppColors.kDarkBgLight;
      // backgroundColor = AppColors.backgroundDisableButton;
    }
    return Material(
      color: AppColors.secondaryColor,
      clipBehavior: Clip.hardEdge,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      elevation: 1,
      child: InkWell(
        onTap: enabled ? onTap : null,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          height: height == null ? 44.h : height?.h,
          child: Stack(
            children: [
              Align(
                alignment: Alignment.center,
                child: CText.button(
                  title,
                  color: _titleColor ?? AppColors.kDarkBgLight,
                ),
              ),
              if (showIconRight) ...[
                const Align(
                  alignment: Alignment.centerRight,
                  child: Icon(
                    FontAwesomeIcons.arrowRight,
                    color: AppColors.whiteColor,
                  ),
                )
              ]
            ],
          ),
        ),
      ),
    );
  }
}

class MButtonNegative extends StatelessWidget {
  final String? title;
  final Function()? onTap;

  const MButtonNegative(this.title, {Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      clipBehavior: Clip.hardEdge,
      borderRadius: BorderRadius.all(Radius.circular(8.h)),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          height: 44.h,
          decoration: BoxDecoration(
            color: AppColors.backgroundButton,
            borderRadius: BorderRadius.all(Radius.circular(8.h)),
            border: Border.all(color: AppColors.backgroundButton, width: 1),
          ),
          child: Center(
            child: CText.title3(
              title ?? '',
              color: AppColors.contentPrimaryColor,
            ),
          ),
        ),
      ),
    );
  }
}

class MButtonContainer extends StatelessWidget {
  final Widget? child;
  final Function()? onTap;

  const MButtonContainer({Key? key, this.onTap, this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Theme.of(context).primaryColor,
        clipBehavior: Clip.hardEdge,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.h),
        ),
        elevation: 1,
        child: InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: child,
          ),
        ));
  }
}

class MButtonSquare extends StatelessWidget {
  final String title;
  final Function()? onTap;

  const MButtonSquare(this.title, {Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      clipBehavior: Clip.hardEdge,
      borderRadius: BorderRadius.all(Radius.circular(2.h)),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 8.h),
          // height: 32.h,
          decoration: BoxDecoration(
            color: AppColors.contentLightGreyColor,
            borderRadius: BorderRadius.all(
              Radius.circular(2.h),
            ),
            border:
                Border.all(color: AppColors.contentLightGreyColor, width: 1),
          ),
          child: Center(
            child: CText.title3(
              title,
              color: AppColors.whiteColor,
            ),
          ),
        ),
      ),
    );
  }
}
