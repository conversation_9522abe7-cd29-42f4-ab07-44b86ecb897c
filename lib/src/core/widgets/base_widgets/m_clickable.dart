import 'package:flutter/material.dart';

class MClickable extends StatelessWidget {
  final Widget? child;
  final Function()? onTap;
  final double padding;
  final double radius;

  const MClickable(
      {Key? key, this.child, this.onTap, this.padding = 5, this.radius = 20})
      : super(key: key);

  const MClickable.noPadding(
      {Key? key, this.child, this.onTap, this.padding = 0, this.radius = 20})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      clipBehavior: Clip.hardEdge,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all(padding),
          child: child,
        ),
      ),
    );
  }
}
