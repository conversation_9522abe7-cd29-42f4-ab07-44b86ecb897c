import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class MIcon extends StatelessWidget {
  final Widget? child;
  final double size;
  final double padding;
  final Color background;

  const MIcon({
    Key? key,
    this.child,
    this.size = 48,
    this.padding = 10,
    this.background = Colors.transparent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      clipBehavior: Clip.hardEdge,
      color: background,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Container(
          width: size,
          height: size,
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Center(child: child),
          )),
    );
  }
}

class MIconContainer extends StatelessWidget {
  final Widget? child;
  final double size;

  const MIconContainer({Key? key, this.child, this.size = 40})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      clipBehavior: Clip.hardEdge,
      color: Colors.transparent,
      child: Container(
          width: size.w,
          height: size.w,
          child: Padding(
            padding: const EdgeInsets.all(0),
            child: child,
          )),
    );
  }
}

class MIconContainerNotResize extends StatelessWidget {
  final Widget? child;
  final double size;

  const MIconContainerNotResize({Key? key, this.child, this.size = 40})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      clipBehavior: Clip.hardEdge,
      color: Colors.transparent,
      child: Container(
        width: size,
        height: size,
        child: Padding(
          padding: const EdgeInsets.all(0),
          child: child,
        ),
      ),
    );
  }
}
