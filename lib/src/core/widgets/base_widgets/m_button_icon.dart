import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class MButtonImage extends StatelessWidget {
  final Function()? onTap;
  final Widget? child;

  const MButtonImage({Key? key, this.onTap, this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconButton(
        icon: Container(
            width: 40,
            height: 40,
            child: Padding(
              padding: const EdgeInsets.all(3),
              child: child,
            )),
        onPressed: onTap);
    // return Material(
    //   clipBehavior: Clip.hardEdge,
    //   color: Colors.transparent,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(24.0),
    //   ),
    //   child: InkWell(
    //     onTap: onTap,
    //     child: Container(
    //         width: 48,
    //         height: 48,
    //         child: Padding(
    //           padding: const EdgeInsets.all(10),
    //           child: child,
    //         )),
    //   ),
    // );
  }
}

class MButtonCircleImage extends StatelessWidget {
  final Function()? onTap;
  final Widget? child;

  const MButtonCircleImage({Key? key, this.onTap, this.child})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Container(
        decoration: BoxDecoration(
            color: AppColors.whiteColor.withOpacity(0.8),
            borderRadius: const BorderRadius.all(Radius.circular(20))),
        width: 40,
        height: 40,
        child: Padding(
          padding: const EdgeInsets.all(5),
          child: child,
        ),
      ),
      onPressed: onTap,
    );
  }
}
