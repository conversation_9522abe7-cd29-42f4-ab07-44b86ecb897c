// container của appbar các tham số button đầu - title giữa - các list actions

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

class MAppbar extends AppBar {
  MAppbar.normal(
    BuildContext context, {
    Key? key,
    String? title,
    List<Widget>? actions,
    Function()? onBack,
  }) : super(
          // elevation: 2,
          key: key,
          backgroundColor: AppColors.primaryColor,
          leading: IconButton(
              icon: const Icon(
                Icons.arrow_back_ios,
                color: AppColors.whiteColor,
                size: 12,
              ),
              onPressed: () {
                if (onBack != null) {
                  onBack();
                  return;
                }
                context.pop();
              }),
          centerTitle: true,
          title: CText.title1(
            title ?? '',
            color: AppColors.whiteColor,
          ),
          actions: actions,
        );

  MAppbar.titleHome({
    Key? key,
    String? title,
    List<Widget>? actions,
  }) : super(
          // elevation: 2,
          key: key,
          backgroundColor: AppColors.primaryColor,
          centerTitle: true,
          title: CText.title1(
            title ?? '',
            color: AppColors.whiteColor,
          ),
          actions: actions,
        );

  MAppbar.back(
    BuildContext context, {
    Key? key,
    Function()? onBack,
  }) : super(
          elevation: 0,
          key: key,
          backgroundColor: AppColors.whiteColor,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: AppColors.contentDarkGreyColor,
              size: 12,
            ),
            onPressed: () {
              if (onBack != null) {
                onBack();
                return;
              }
              context.pop();
            },
          ),
        );

  MAppbar.titleCustom(
    BuildContext context, {
    Key? key,
    Widget? title,
    List<Widget>? actions,
  }) : super(
          elevation: 0,
          // elevation: 2,
          key: key,
          backgroundColor: AppColors.primaryColor,
          leading: IconButton(
              icon: const Icon(
                Icons.arrow_back_ios,
                color: AppColors.whiteColor,
                size: 12,
              ),
              onPressed: () {
                context.pop();
              }),
          title: title,
          actions: actions,
        );
}
