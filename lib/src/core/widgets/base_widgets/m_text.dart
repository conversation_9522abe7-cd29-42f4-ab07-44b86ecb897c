import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/styles.dart';

class CText extends Text {
  CText.header1(
    String data, {
    Color color = AppColors.contentDarkGreyColor,
    FontWeight? fontWeight,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.xLargeFontSize,
            color: color,
            fontWeight: fontWeight ?? FontWeight.bold,
          ),
        );

  CText.header2(
    String data, {
    Color color = AppColors.contentDarkGreyColor,
    FontWeight fontWeight = FontWeight.w400,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontStyle: FontStyle.normal,
            fontSize: AppFontSize.largeFontSize,
            color: color,
            fontWeight: fontWeight,
          ),
        );

  CText.title1(
    String data, {
    TextAlign textAlign = TextAlign.start,
    Color color = AppColors.contentDarkGreyColor,
    FontWeight fontWeight = FontWeight.w400,
    double? fontSize,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontSize: fontSize ?? AppFontSize.titleFontSize,
            color: color,
            fontWeight: fontWeight,
          ),
          textAlign: textAlign,
        );

  CText.title2(
    String data, {
    Color color = AppColors.contentDarkGreyColor,
    TextOverflow textOverflow = TextOverflow.visible,
    FontWeight fontWeight = FontWeight.normal,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
    double? fontSize,
  }) : super(
          data,
          overflow: textOverflow,
          textAlign: textAlign,
          maxLines: maxLines,
          style: kTextStyle().copyWith(
            fontSize: fontSize ?? AppFontSize.titleSubFontSize,
            color: color,
            fontWeight: fontWeight,
          ),
        );

  CText.title3(
    String data, {
    TextAlign textAlign = TextAlign.start,
    TextOverflow overflow = TextOverflow.visible,
    Color color = AppColors.contentDarkGreyColor,
    int? maxLines,
  }) : super(
          data,
          maxLines: maxLines,
          overflow: overflow,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.normalFontSize,
            color: color,
            fontWeight: FontWeight.w700,
          ),
          textAlign: textAlign,
        );

  CText.title4(
    String data, {
    TextAlign textAlign = TextAlign.start,
    TextOverflow overflow = TextOverflow.visible,
    Color color = AppColors.contentDarkGrey2Color,
  }) : super(
          data,
          overflow: overflow,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.smallFontSize,
            color: color,
            fontWeight: FontWeight.w700,
          ),
          textAlign: textAlign,
        );

  CText.title5(
    String data, {
    TextAlign textAlign = TextAlign.start,
    Color color = AppColors.contentDarkGreyColor,
    FontWeight fontWeight = FontWeight.w700,
    double? fontSize,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontSize: fontSize ?? AppFontSize.titleSubFontSize2,
            color: color,
            fontWeight: fontWeight,
          ),
          textAlign: textAlign,
        );

  CText.body(
    String data, {
    TextAlign textAlign = TextAlign.start,
    Color color = AppColors.contentDarkGreyColor,
    int? maxLines,
    TextOverflow textOverflow = TextOverflow.visible,
    double? fontSize,
    FontWeight? fontWeight,
    TextDecoration? textDecoration,
  }) : super(
          data,
          maxLines: maxLines,
          overflow: textOverflow,
          style: kTextStyle().copyWith(
            fontSize: fontSize ?? AppFontSize.normalFontSize,
            color: color,
            fontWeight: fontWeight ?? FontWeight.w400,
            decoration: textDecoration,
          ),
          textAlign: textAlign,
        );

  CText.body1(
    String data, {
    TextAlign textAlign = TextAlign.start,
    Color color = AppColors.contentDarkGreyColor,
    TextOverflow textOverflow = TextOverflow.ellipsis,
  }) : super(
          data,
          overflow: textOverflow,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.normalFontSize,
            color: color,
            fontWeight: FontWeight.w600,
          ),
          textAlign: textAlign,
        );

  CText.body2(
    String data, {
    TextAlign textAlign = TextAlign.start,
    Color color = AppColors.contentDarkGreyColor,
    TextDecoration? textDecoration,
    int? maxLines,
    TextOverflow? textOverflow,
    FontWeight fontWeight = FontWeight.w400,
  }) : super(
          data,
          maxLines: maxLines,
          overflow: textOverflow,
          style: kTextStyle().copyWith(
            decoration: textDecoration,
            fontSize: AppFontSize.smallFontSize,
            color: color,
            fontWeight: fontWeight,
          ),
          textAlign: textAlign,
        );

  CText.button(
    String data, {
    Color color = AppColors.primaryColor,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.titleSubFontSize,
            color: color,
            fontWeight: FontWeight.w600,
          ),
        );

  CText.bottomBar(
    String data, {
    TextAlign textAlign = TextAlign.center,
    Color color = AppColors.contentLightGreyColor,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.xSmallFontSize,
            color: color,
            fontWeight: FontWeight.normal,
          ),
          textAlign: textAlign,
        );

  CText.caption(
    String data, {
    TextAlign textAlign = TextAlign.start,
    TextOverflow textOverflow = TextOverflow.visible,
    int? maxLines,
    Color color = AppColors.contentDarkGreyColor,
    FontWeight fontWeight = FontWeight.normal,
  }) : super(
          data,
          maxLines: maxLines,
          overflow: textOverflow,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.xSmallFontSize,
            color: color,
            fontWeight: fontWeight,
          ),
          textAlign: textAlign,
        );

  CText.link(
    String data, {
    TextAlign textAlign = TextAlign.start,
  }) : super(
          data,
          style: kTextStyle().copyWith(
            fontSize: AppFontSize.normalFontSize,
            color: AppColors.primaryColor,
            // decoration: TextDecoration.underline,
            fontWeight: FontWeight.w400,
          ),
          textAlign: textAlign,
        );
}
