import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';

class ModalManager {
  static Future showBottomSheet(
      BuildContext context, Widget Function(BuildContext) builder,
      {bool isScrollControlled = false}) {
    return showModalBottomSheet(
      backgroundColor: Colors.transparent,
      context: context,
      builder: builder,
      isScrollControlled: isScrollControlled,
      useRootNavigator: false,
      isDismissible: true,
      enableDrag: true,
    );
  }

  static Future<void> pushShowDialog(
      BuildContext context, Widget Function(BuildContext) builder,
      {barrierDismissible = true}) {
    return showDialog<void>(
      context: context,
      builder: builder,
      barrierDismissible: barrierDismissible,
    );
  }
}

class MDialogScaffold extends StatelessWidget {
  final Widget? child;
  final Function()? onOutsideClick;

  const MDialogScaffold({Key? key, this.child, this.onOutsideClick})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onTap: onOutsideClick,
            child: Container(
              width: double.infinity,
              height: double.infinity,
            ),
          ),
          Center(
            child: Container(
                width: double.infinity,
                margin: EdgeInsets.all(24.w),
                decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(12)),
                    color: AppColors.whiteColor),
                child: Material(
                  color: Colors.transparent,
                  child: SingleChildScrollView(
                    child: child,
                  ),
                )),
          ),
        ],
      ),
    );
  }
}

class MDialogContainer extends StatelessWidget {
  final Widget? child;

  const MDialogContainer({Key? key, this.child}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return MBackdropFilterFull(
      child: Center(
        child: Container(
          width: double.infinity,
          margin: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            color: AppColors.primaryColor,
            border: Border.all(
              width: 1.0,
              color: const Color(0xFF707070),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF626262).withOpacity(0.6),
                offset: const Offset(2.0, 2.0),
                blurRadius: 6.0,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: SingleChildScrollView(
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}
