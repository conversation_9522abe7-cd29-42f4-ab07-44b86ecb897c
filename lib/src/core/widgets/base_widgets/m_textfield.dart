import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';

import '../../utils.dart';

OutlineInputBorder inputBorderDisable() {
  return const OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.borderInput, width: 0.5),
      borderRadius: BorderRadius.all(Radius.circular(4)));
}

OutlineInputBorder inputBorderFocus() {
  return const OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.primaryColor, width: 0.5),
      borderRadius: BorderRadius.all(Radius.circular(4)));
}

class MTextFieldInputRight extends StatelessWidget {
  final String title;
  final Function(String? value)? onSaved;
  final FormFieldValidator<String>? validator;
  final TextInputType? textInputType;
  final bool? obscureText;
  final bool? enabled;
  final String? initialValue;
  final TextEditingController? textEditingController;

  MTextFieldInputRight(
      {Key? key,
      required this.title,
      this.onSaved,
      this.validator,
      this.textInputType,
      this.obscureText,
      this.enabled,
      this.textEditingController,
      this.initialValue})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 55.0,
      alignment: Alignment.center,
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.whiteColor)),
      ),
      child: TextFormField(
        initialValue: initialValue,
        controller: textEditingController,
        enabled: enabled ?? true,
        obscureText: obscureText ?? false,
        style: CText.body(
          '',
        ).style?.copyWith(),
        textAlign: TextAlign.end,
        keyboardType: textInputType ?? TextInputType.emailAddress,
        decoration: InputDecoration(
          hintStyle: CText.body('').style,
          icon: CText.caption(title),
          border: InputBorder.none,
        ),
        onSaved: (String? value) {
          if (onSaved != null) {
            onSaved!(value ?? '');
          }
        },
        validator: validator,
      ),
    );
  }
}

class MPriceTextField extends StatelessWidget {
  final Color cursorColor;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final Function(String)? onChange;
  final String? hintText;
  final TextInputType textInputType;
  final TextAlign textAlign;
  final Function()? onEditingComplete;

  const MPriceTextField(
      {Key? key,
      this.cursorColor = AppColors.primaryColor,
      this.validator,
      this.controller,
      this.onChange,
      this.hintText,
      this.textInputType = TextInputType.text,
      this.textAlign = TextAlign.start,
      this.onEditingComplete})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      inputFormatters: [
        FilteringTextInputFormatter.allow(ValidateData.number()),
      ],
      textDirection: TextDirection.ltr,
      onEditingComplete: onEditingComplete,
      keyboardType: textInputType,
      onChanged: onChange,
      controller: controller,
      validator: validator,
      enabled: true,
      cursorColor: cursorColor,
      style: CText.title2('').style,
      textAlign: textAlign,
      decoration: InputDecoration(
        border: inputBorderDisable(),
        hintStyle: CText.title2('')
            .style
            ?.copyWith(color: AppColors.contentLightGreyColor),
        hintText: hintText,
        contentPadding: EdgeInsets.symmetric(horizontal: 15.w),
        focusedBorder: inputBorderFocus(),
      ),
    );
  }
}

// ignore: must_be_immutable
class CTextField extends TextFormField {
  @override
  String? Function(String?)? validator;
  String? hintText;
  TextInputType? textInputType;
  @override
  Function(String)? onChanged;
  @override
  TextEditingController? controller;
  InputBorder? inputBorder;
  InputBorder? inputFocusBorder;
  TextStyle? style;
  TextStyle? hintStyle;
  TextAlign textAlign;
  @override
  bool enabled;
  Color? cursorColor;
  bool? obscureText;
  FocusNode? focusNode;
  Widget? suffix;

  // full option
  CTextField.normal({
    this.hintText,
    this.validator,
    this.textInputType = TextInputType.text,
    this.onChanged,
    this.controller,
    this.inputBorder = InputBorder.none,
    this.inputFocusBorder = InputBorder.none,
    this.style,
    this.hintStyle,
    this.enabled = true,
    this.textAlign = TextAlign.start,
    this.cursorColor = AppColors.contentLightGreyColor,
    this.obscureText = false,
    this.focusNode,
    this.suffix,
  }) : super(
          obscureText: obscureText ?? false,
          validator: validator,
          keyboardType: textInputType,
          textAlign: textAlign,
          onChanged: onChanged,
          controller: controller,
          enabled: enabled,
          cursorColor: cursorColor,
          style: style ?? CText.body('').style,
          focusNode: focusNode,
          decoration: InputDecoration(
            suffix: suffix,
            hintText: hintText,
            hintStyle: hintStyle,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.w),
            border: inputBorder,
            focusedBorder: inputFocusBorder,
          ),
        );

  CTextField.inputBorder({
    this.hintText,
    this.validator,
    this.textInputType = TextInputType.text,
    this.onChanged,
    this.controller,
    this.enabled = true,
    this.textAlign = TextAlign.start,
    this.cursorColor = AppColors.contentLightGreyColor,
    this.obscureText = false,
    this.focusNode,
  }) : super(
          obscureText: obscureText ?? false,
          validator: validator,
          keyboardType: textInputType,
          textAlign: textAlign,
          onChanged: onChanged,
          controller: controller,
          enabled: enabled,
          cursorColor: cursorColor,
          style: CText.title2('').style,
          focusNode: focusNode,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: CText.title2('')
                .style
                ?.copyWith(color: AppColors.contentLightGreyColor),
            contentPadding: EdgeInsets.symmetric(horizontal: 20.w),
            border: inputBorderDisable(),
            enabledBorder: inputBorderDisable(),
            disabledBorder: inputBorderDisable(),
            focusedBorder: inputBorderFocus(),
          ),
        );

  CTextField.password({
    this.hintText,
    this.validator,
    this.textInputType = TextInputType.text,
    this.onChanged,
    this.controller,
    this.enabled = true,
    this.textAlign = TextAlign.start,
    this.cursorColor = AppColors.contentLightGreyColor,
    this.obscureText = true,
    this.focusNode,
    this.suffix,
  }) : super(
          obscureText: obscureText ?? false,
          validator: validator,
          keyboardType: textInputType,
          textAlign: textAlign,
          onChanged: onChanged,
          controller: controller,
          enabled: enabled,
          cursorColor: cursorColor,
          style: CText.title2('').style,
          focusNode: focusNode,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: CText.title2('')
                .style
                ?.copyWith(color: AppColors.contentLightGreyColor),
            suffix: suffix,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            // border: inputBorderDisable(),
            // enabledBorder: inputBorderDisable(),
            // disabledBorder: inputBorderDisable(),
            // focusedBorder: inputBorderFocus()
          ),
        );

  CTextField.dobInput({
    this.hintText,
    this.validator,
    this.textInputType = TextInputType.number,
    this.onChanged,
    this.controller,
    this.enabled = true,
    this.textAlign = TextAlign.left,
    this.cursorColor = AppColors.whiteColor,
    this.focusNode,
    this.suffix,
  }) : super(
          validator: validator,
          textDirection: TextDirection.ltr,
          keyboardType: textInputType,
          textAlign: textAlign,
          onChanged: onChanged,
          controller: controller,
          enabled: enabled,
          cursorColor: cursorColor,
          style: CText.title2('').style?.copyWith(color: AppColors.whiteColor),
          focusNode: focusNode,
          enableInteractiveSelection: false,
          maxLength: 8,
          inputFormatters: [
            FilteringTextInputFormatter.allow(ValidateData.number()),
          ],
          decoration: InputDecoration(
            counterText: '',
            hintText: hintText,
            hintStyle:
                CText.title2('').style?.copyWith(color: AppColors.whiteColor),
            suffix: suffix,
            contentPadding: const EdgeInsets.symmetric(horizontal: 0),
            // border: inputBorderDisable(),
            // enabledBorder: inputBorderDisable(),
            // disabledBorder: inputBorderDisable(),
            // focusedBorder: inputBorderFocus()
          ),
        );
}
