import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/widgets/common/auto_hide_keyboard.dart';

class MBackdropFilter extends StatelessWidget {
  final Widget? child;

  const MBackdropFilter({Key? key, this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: child,
      ),
    );
  }
}

class MBackdropFilterFull extends StatelessWidget {
  final Widget? child;

  const MBackdropFilterFull({Key? key, this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: Container(
          decoration: const BoxDecoration(
              // color: Color.fromRGBO(0, 0, 0, 0.4)
              ),
          child: child),
    );
  }
}

class MBackdropFilterFullWithScaffold extends StatelessWidget {
  final Widget? child;

  const MBackdropFilterFullWithScaffold({Key? key, this.child})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: AutoHideKeyboard(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
              decoration: const BoxDecoration(
                  // color: Color.fromRGBO(0, 0, 0, 0.4)
                  ),
              child: child),
        ),
      ),
    );
  }
}
