import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:search_highlight_text/search_highlight_text.dart';

const _letterSpacing = 1.0;

class KeywordsHighlightWidget extends StatelessWidget {
  final String text;
  final List<String> keywordsHighlight;
  final TextOverflow? overflow;
  final int? maxLines;
  final TextAlign? textAlign;
  final double? fontSize;
  final double? letterSpacing;

  const KeywordsHighlightWidget({
    super.key,
    required this.text,
    required this.keywordsHighlight,
    this.overflow,
    this.maxLines,
    this.textAlign,
    this.fontSize,
    this.letterSpacing,
  });

  @override
  Widget build(BuildContext context) {
    final regex = listKeywordsToRegex();

    return SearchTextInheritedWidget(
      searchRegExp: RegExp(regex, caseSensitive: false),
      highlightStyle: TextStyle(
        color: AppColors.secondary2Color,
        fontFamily: 'MyFont3',
        fontWeight: FontWeight.w700,
        fontSize: fontSize ?? 22.0,
        letterSpacing: _letterSpacing,
      ),
      child: SearchHighlightText(
        text,
        overflow: overflow,
        maxLines: maxLines,
        textAlign: textAlign ?? TextAlign.start,
        style: TextStyle(
          color: AppColors.whiteColor,
          fontFamily: 'MyFont3',
          fontWeight: FontWeight.w700,
          fontSize: fontSize ?? 22.0,
          letterSpacing: letterSpacing ?? 1.0,
        ),
      ),
    );
  }

  String listKeywordsToRegex() {
    String regexString = '';

    for (int n = 0; n < keywordsHighlight.length; n++) {
      regexString += keywordsHighlight[n];
      if (n < keywordsHighlight.length - 1) {
        regexString += '|';
      }
    }

    return regexString;
  }
}
