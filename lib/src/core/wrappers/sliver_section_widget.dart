import 'package:flutter/material.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';

class SliverSectionWidget extends StatelessWidget {
  const SliverSectionWidget({
    super.key,
    required this.header,
    required this.sliver,
    this.isSticky = false,
    this.overlapsContent = false,
  });

  final Widget header;
  final Widget sliver;
  final bool isSticky;
  final bool overlapsContent;

  @override
  Widget build(BuildContext context) {
    return SliverStickyHeader(
      sticky: false,
      header: header,
      sliver: sliver,
      overlapsContent: overlapsContent,
    );
  }
}
