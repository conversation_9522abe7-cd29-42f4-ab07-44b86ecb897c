import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';

import '../../di/injection/injection.dart';
import '../../presentation/shared/account/account_bloc.dart';
import '../utils.dart';

class MyRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  static String currentRootName = 'root';
  void _sendScreenView(PageRoute<dynamic> route) {
    var screenName = route.settings.name;
    Log.info('MyrouteObserve', 'screenName $screenName');
    currentRootName = screenName ?? '';
    // do something with it, ie. send it to your analytics service collector
    FirebaseAnalyticsWapper().analytics.logEvent(
        name: 'screen${screenName?.replaceAll('/', '_').replaceAll('-', '_')}');
    // FlutterUxcam.logEvent('screen${screenName?.replaceAll('/', '_').replaceAll('-', '_')}');
    try {
      if (injector<AccountBloc>().isUXCamInit) {
        FlutterUxcam.tagScreenName(
            'Screen${screenName?.replaceAll('/', '_').replaceAll('-', '_')}');
      }
    } catch (e) {
      Log.printSimpleLog(e.toString());
    }
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute) {
      _sendScreenView(route);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute is PageRoute) {
      _sendScreenView(newRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute is PageRoute && route is PageRoute) {
      // _sendScreenView(previousRoute);
    }
  }
}
