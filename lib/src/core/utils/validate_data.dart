class ValidateData {
  static RegExp validEmail() {
    return RegExp(r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+');
  }

  static RegExp valiUser() {
    return RegExp(r'^[a-zA-Z0-9._-]{0,50}$');
  }

  static RegExp phoneNumber() {
    // return RegExp(r'(03|07|08|09|01[2|6|8|9])+([0-9]{8})\b');
    return RegExp(r'([0-9]{9,})\b');
  }

  static RegExp password() {
    // https://stackoverflow.com/questions/19605150/regex-for-password-must-contain-at-least-eight-characters-at-least-one-number-a
    // Minimum eight characters, at least one uppercase letter, one lowercase letter and one number:
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$');
  }

  static RegExp dateInput() {
    return RegExp(r'([0-9]{10})\b');
  }

  static RegExp number() {
    return RegExp(r'([0-9])');
  }

  static RegExp containsDateFormat() {
    // return RegExp(r'(03|07|08|09|01[2|6|8|9])+([0-9]{8})\b');
    return RegExp(r'(D|M|Y)\b');
  }

  static RegExp dateDDMMYYYY() {
    // return RegExp(r'(03|07|08|09|01[2|6|8|9])+([0-9]{8})\b');
    return RegExp(
        r'^(?:0[1-9]|[12]\d|3[01])([\/.-])(?:0[1-9]|1[012])\1(?:19|20)\d\d$');
  }
}
