import 'dart:core';

import 'package:intl/intl.dart';

class MDateUtils {
  static final MDateUtils _singleton = MDateUtils._internal();

  var formatYMD = DateFormat('yyyy-MM-dd');
  // ignore: non_constant_identifier_names
  var formatYMD_HHMMSS = DateFormat('yyyy-MM-dd HH:mm:ss');
  // ignore: non_constant_identifier_names
  var formatYMD_HHMM = DateFormat('dd/MM/yyyy HH:mm');
  var formatDMY = DateFormat('dd/MM/yyyy');
  var formatMDY = DateFormat('MM/dd/yyyy');

  MDateUtils._internal();

  factory MDateUtils() {
    return _singleton;
  }

  String getToDayString() {
    return formatYMD.format(DateTime.now());
  }

  String getDateTimeString() {
    return formatYMD_HHMMSS.format(DateTime.now());
  }

  String getDateByFormat(DateTime date) {
    return formatYMD.format(date);
  }

  String getYYMMDD(DateTime dateTime) {
    return formatYMD.format(dateTime);
  }

  String getDDMMYY(DateTime? dateTime) {
    return dateTime == null ? '' : formatDMY.format(dateTime.toUtc().toLocal());
  }

  DateTime getDDMMYYDate(String strDate) {
    return formatDMY.parse(strDate);
  }

  String getMMDDYY(DateTime? dateTime) {
    return dateTime == null ? '' : formatMDY.format(dateTime.toUtc().toLocal());
  }

  String getDateTimeSimple(DateTime? dateTime) {
    return dateTime == null
        ? ''
        : formatYMD_HHMM.format(dateTime.toUtc().toLocal());
  }

  bool isSameDate(DateTime date1, DateTime date2) {
    return date1.month == date2.month &&
        date1.year == date2.year &&
        date1.day == date2.day;
  }

  int dayOfYear(DateTime date) {
    return int.parse(DateFormat('D').format(date));
  }

  int weekOfYear(DateTime date) {
    return ((dayOfYear(date) - date.weekday + 10) / 7).floor();
  }

  String getYesterday() {
    var now = DateTime.now();
    var yesterday = DateTime(now.year, now.month, now.day - 1);
    return formatYMD.format(yesterday);
  }

  String getFirstDayOfWeek() {
    var now = DateTime.now();
    var firtDayOfWeek =
        DateTime(now.year, now.month, now.day - now.weekday + 1); //tinh thu 2
    return formatYMD.format(firtDayOfWeek);
  }

  String getEndDayOfWeek() {
    var now = DateTime.now();
    var endDayOfWeek =
        DateTime(now.year, now.month, now.day + (7 - now.weekday));
    return formatYMD.format(endDayOfWeek);
  }

  String getFirstDayOfLastWeek() {
    var now = DateTime.now();
    var firtDayOfWeek =
        DateTime(now.year, now.month, now.day - now.weekday + 1 - 7);
    return formatYMD.format(firtDayOfWeek);
  }

  String getEndDayOfLastWeek() {
    var now = DateTime.now();
    var endDayOfWeek =
        DateTime(now.year, now.month, now.day + (7 - now.weekday) - 7);
    return formatYMD.format(endDayOfWeek);
  }

  String getFirstDayOfMonth() {
    var now = DateTime.now();
    var first = DateTime(now.year, now.month, 1);
    return formatYMD.format(first);
  }

  String getEndDayOfMonth() {
    var now = DateTime.now();
    var end = DateTime(now.year, now.month + 1, 0);
    return formatYMD.format(end);
  }

  String getFirstDayOfLastMonth() {
    var now = DateTime.now();
    var first = DateTime(now.year, now.month - 1, 1);
    return formatYMD.format(first);
  }

  String getEndDayOfLastMonth() {
    var now = DateTime.now();
    var end = DateTime(now.year, now.month, 0);
    return formatYMD.format(end);
  }

  String getFirstDayOfThisQuarter() {
    var now = DateTime.now();
    DateTime first;
    if (now.month < 4) {
      first = DateTime(now.year, 1, 1);
    } else if (now.month < 7) {
      first = DateTime(now.year, 4, 1);
    } else if (now.month < 10) {
      first = DateTime(now.year, 7, 1);
    } else {
      first = DateTime(now.year, 10, 1);
    }
    return formatYMD.format(first);
  }

  String getEndDayOfThisQuarter() {
    var now = DateTime.now();
    DateTime end;
    if (now.month < 4) {
      end = DateTime(now.year, 4, 0);
    } else if (now.month < 7) {
      end = DateTime(now.year, 7, 0);
    } else if (now.month < 10) {
      end = DateTime(now.year, 10, 0);
    } else {
      end = DateTime(now.year + 1, 1, 0);
    }
    return formatYMD.format(end);
  }

  String getFirstDayOfLastQuarter() {
    var now = DateTime.now();
    DateTime first;
    if (now.month < 4) {
      first = DateTime(now.year, 10, 1);
    } else if (now.month < 7) {
      first = DateTime(now.year, 1, 1);
    } else if (now.month < 10) {
      first = DateTime(now.year, 4, 1);
    } else {
      first = DateTime(now.year, 7, 1);
    }
    return formatYMD.format(first);
  }

  String getEndDayOfLastQuarter() {
    var now = DateTime.now();
    DateTime end;
    if (now.month < 4) {
      end = DateTime(now.year + 1, 1, 0);
    } else if (now.month < 7) {
      end = DateTime(now.year, 4, 0);
    } else if (now.month < 10) {
      end = DateTime(now.year, 7, 0);
    } else {
      end = DateTime(now.year, 10, 0);
    }
    return formatYMD.format(end);
  }

  String getFirstDayOfThisYear() {
    var now = DateTime.now();
    var first = DateTime(now.year, 1, 1);
    return formatYMD.format(first);
  }

  String getEndDayOfThisYear() {
    var now = DateTime.now();
    var end = DateTime(now.year + 1, 1, 0);
    return formatYMD.format(end);
  }
}
