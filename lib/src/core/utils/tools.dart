import 'dart:convert' as convert;

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/common/skeleton.dart';
import 'package:shimmer/shimmer.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:transparent_image/transparent_image.dart';

class Tools {
  static double formatDouble(dynamic value) => value * 1.0;

  static String formatDateString(String date) {
    var timeFormat = DateTime.parse(date);
    final timeDif = DateTime.now().difference(timeFormat);
    return timeago.format(DateTime.now().subtract(timeDif), locale: 'en');
  }

  static String getCurrecyFormatted(price, Map<String, dynamic> rates,
      {currency}) {
    var defaultCurrency =
        kAdvanceConfigRaw['DefaultCurrency'] as Map<String, dynamic>;
    var currencies = (kAdvanceConfigRaw['Currencies'] as List?) ?? [];
    if (currency != null && currencies.isNotEmpty) {
      currencies.forEach((item) {
        if ((item as Map<String, dynamic>)['currency'] == currency) {
          defaultCurrency = item;
        }
      });
    }

    if (rates[defaultCurrency['currency']] != null) {
      price =
          getPriceValueByCurrency(price, defaultCurrency['currency'], rates);
    }

    final formatCurrency = NumberFormat.currency(
        symbol: '', decimalDigits: defaultCurrency['decimalDigits']);
    try {
      var number = '';
      if (price is String) {
        number =
            formatCurrency.format(price.isNotEmpty ? double.parse(price) : 0);
      } else {
        number = formatCurrency.format(price);
      }
      return defaultCurrency['symbolBeforeTheNumber']
          ? defaultCurrency['symbol'] + number
          : number + defaultCurrency['symbol'];
    } catch (err) {
      return defaultCurrency['symbolBeforeTheNumber']
          ? defaultCurrency['symbol'] + formatCurrency.format(0)
          : formatCurrency.format(0) + defaultCurrency['symbol'];
    }
  }

  static double getPriceValueByCurrency(
      price, String currency, Map<String, dynamic> rates) {
    double rate = rates[currency] ?? 1.0;

    if (price == '' || price == null) {
      return 0;
    }
    return double.parse(price.toString()) * rate;
  }

  static Future<List<dynamic>> loadStatesByCountry(String country) async {
    try {
      // load local config
      var path = 'lib/config/states/state_${country.toLowerCase()}.json';
      final appJson = await rootBundle.loadString(path);
      return List<dynamic>.from(convert.jsonDecode(appJson));
    } catch (e) {
      return [];
    }
  }

  static String getHigherResProviderPhotoUrl(String photoURL) {
    print(photoURL);
    var result = photoURL;
    if (photoURL.contains('google')) {
      result = photoURL.replaceFirst('s96-c', 's400-c');
    } else if (photoURL.contains('facebook')) {
      result = '$photoURL?type=large';
    }
    return result;
  }

  /// Smart image function to load image cache and check empty URL to return empty box
  /// Only apply for the product image resize with (small, medium, large)
  static Widget image({
    String? url,
    double? width,
    double? height,
    BoxFit? fit,
    String? tag,
    double offset = 0.0,
    isVideo = false,
    hidePlaceHolder = false,
  }) {
    if (height == null && width == null) {
      width = 200;
    }

    if (url == null || url == '') {
      return Skeleton(
        width: width ?? 200,
        height: height ?? (width ?? 200) * 1.2,
      );
    }

    if (isVideo) {
      return Stack(
        children: <Widget>[
          Container(
            width: width,
            height: height,
            decoration: BoxDecoration(color: Colors.black12.withOpacity(1)),
            child: ExtendedImage.network(
              url,
              width: width,
              height: height ?? (width ?? 200) * 1.2,
              fit: fit,
              cache: true,
              enableLoadState: false,
              alignment: Alignment(
                  (offset >= -1 && offset <= 1)
                      ? offset
                      : (offset > 0)
                          ? 1.0
                          : -1.0,
                  0.0),
            ),
          ),
          Positioned.fill(
            child: Icon(
              Icons.play_circle_outline,
              color: Colors.white70.withOpacity(0.5),
              size: width == null ? 30 : width / 1.7,
            ),
          ),
        ],
      );
    }

    if (kIsWeb) {
      return FadeInImage.memoryNetwork(
        image: url,
        fit: fit,
        width: width,
        height: height,
        placeholder: kTransparentImage,
      );
    }

    if (url.contains('http')) {
      return ExtendedImage.network(
        url,
        width: width,
        height: height,
        fit: fit,
        cache: true,
        // enableLoadState: false,
        // alignment: Alignment(
        //   (offset >= -1 && offset <= 1) ? offset : (offset > 0) ? 1.0 : -1.0,
        //   0.0,
        // ),
        loadStateChanged: (ExtendedImageState state) {
          Widget? widget;
          switch (state.extendedImageLoadState) {
            case LoadState.completed:
            case LoadState.loading:
              widget = AnimatedSwitcher(
                  duration: const Duration(seconds: 2),
                  child: state.extendedImageLoadState == LoadState.completed
                      ? ExtendedRawImage(
                          image: state.extendedImageInfo?.image,
                          width: width,
                          height: height,
                          fit: fit,
                        )
                      : SizedBox(
                          height: height ?? 140,
                          width: width ?? 100,
                          child: Shimmer.fromColors(
                              baseColor: Colors.white,
                              highlightColor: AppColors.darkPrimaryColor,
                              child: Container()),
                        ));
              break;
            case LoadState.failed:
              widget = Container(
                width: width,
                height: height ?? (width ?? 200) * 1.2,
                color: const Color(AppColors.kEmptyColor),
              );
              break;
          }
          return widget;
        },
      );
    } else {
      return ExtendedImage.asset(
        url,
        width: width,
        height: height,
        fit: fit,
        // enableLoadState: false,
        // alignment: Alignment(
        //   (offset >= -1 && offset <= 1) ? offset : (offset > 0) ? 1.0 : -1.0,
        //   0.0,
        // ),
        loadStateChanged: (ExtendedImageState state) {
          Widget? widget;
          switch (state.extendedImageLoadState) {
            case LoadState.loading:
              widget = hidePlaceHolder
                  ? const SizedBox()
                  : Skeleton(
                      width: width ?? 100,
                      height: height ?? 140,
                    );
              break;
            case LoadState.completed:
              widget = ExtendedRawImage(
                image: state.extendedImageInfo?.image,
                width: width,
                height: height,
                fit: fit,
              );
              break;
            case LoadState.failed:
              widget = Container(
                width: width,
                height: height ?? (width ?? 200) * 1.2,
                color: const Color(AppColors.kEmptyColor),
              );
              break;
          }
          return widget;
        },
      );
    }
  }

  static String getErrorMessage(body) {
    String message = body['message'];
    if (body['parameters'] != null && body['parameters'].length > 0) {
      final params = body['parameters'];
      final keys = params is List ? params : params.keys.toList();
      for (var i = 0; i < keys.length; i++) {
        if (params is List) {
          message = message.replaceAll('%${i + 1}', keys[i].toString());
        } else {
          message =
              message.replaceAll('%${keys[i]}', params[keys[i]].toString());
        }
      }
    }
    return message;
  }
}

class Videos {
  static String? getVideoLink(String content) {
    if (_getYoutubeLink(content) != null) {
      return _getYoutubeLink(content);
    } else if (_getFacebookLink(content) != null) {
      return _getFacebookLink(content);
    } else {
      return _getVimeoLink(content);
    }
  }

  static String? _getYoutubeLink(String? content) {
    final regExp = RegExp(
        'https://www.youtube.com/((v|embed))\/?[a-zA-Z0-9_-]+',
        multiLine: true,
        caseSensitive: false);

    String? youtubeUrl;

    try {
      if (content?.isNotEmpty ?? false) {
        var matches = regExp.allMatches(content ?? '');
        if (matches.isNotEmpty) {
          youtubeUrl = matches.first.group(0) ?? '';
        }
      }
    } catch (error) {
//      printLog('[_getYoutubeLink] ${error.toString()}');
    }
    return youtubeUrl;
  }

  static String? _getFacebookLink(String? content) {
    final regExp = RegExp(
        'https://www.facebook.com\/[a-zA-Z0-9\.]+\/videos\/(?:[a-zA-Z0-9\.]+\/)?([0-9]+)',
        multiLine: true,
        caseSensitive: false);

    String? facebookVideoId;
    String? facebookUrl;
    try {
      if (content?.isNotEmpty ?? false) {
        var matches = regExp.allMatches(content ?? '');
        if (matches.isNotEmpty) {
          facebookVideoId = matches.first.group(1);
          if (facebookVideoId != null) {
            facebookUrl =
                'https://www.facebook.com/video/embed?video_id=$facebookVideoId';
          }
        }
      }
    } catch (error) {
      printLog(error);
    }
    return facebookUrl;
  }

  static String? _getVimeoLink(String? content) {
    final regExp = RegExp('https://player.vimeo.com/((v|video))\/?[0-9]+',
        multiLine: true, caseSensitive: false);

    String? vimeoUrl;

    try {
      if (content?.isNotEmpty ?? false) {
        var matches = regExp.allMatches(content ?? '');
        if (matches.isNotEmpty) {
          vimeoUrl = matches.first.group(0);
        }
      }
    } catch (error) {
      printLog(error);
    }
    return vimeoUrl;
  }
}

class Utils {
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  static Future<dynamic> parseJsonFromAssets(String assetsPath) async {
    return rootBundle.loadString(assetsPath).then(convert.jsonDecode);
  }

  static Function getLanguagesList = ([context]) {
    return [
      {
        'name': context != null ? S.of(context).english : 'English',
        'icon': ImageCountry.GB,
        'code': 'en',
        'text': 'English',
        'storeViewCode': 'en'
      },
      {
        'name': context != null ? S.of(context).vietnamese : 'Vietnam',
        'icon': ImageCountry.VN,
        'code': 'vi',
        'text': 'Tiếng Việt',
        'storeViewCode': 'vi'
      },
    ];
  };
}
