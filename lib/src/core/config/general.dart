import '../../domain/entities/advanced_config.dart';

/// Default app config, it's possible to set as URL
const kAppConfig =
    'https://firebasestorage.googleapis.com/v0/b/medita-fef9a.appspot.com/o/configs%2Fconfig_vi.json?alt=media';

const kAdvanceConfigRaw = {
  'DefaultLanguage': 'vi',
  'EnablePointReward': true,
  'hideOutOfStock': false,
  'EnableRating': true,
  'hideEmptyProductListRating': false,

  /// Show stock Status on product List
  'showStockStatus': true,

  'isCaching': false,

  /// set kIsResizeImage to true if you have finish running Re-generate image plugin
  'kIsResizeImage': false,

  'GridCount': 3,
  'DefaultCurrency': {
    'symbol': 'đ',
    'decimalDigits': 2,
    'symbolBeforeTheNumber': false,
    'currency': 'VND'
  },
  'Currencies': [
    {
      'symbol': '\$',
      'decimalDigits': 2,
      'symbolBeforeTheNumber': true,
      'currency': 'USD'
    },
    {
      'symbol': 'đ',
      'decimalDigits': 2,
      'symbolBeforeTheNumber': false,
      'currency': 'VND'
    },
    {
      'symbol': '€',
      'decimalDigits': 2,
      'symbolBeforeTheNumber': true,
      'currency': 'Euro'
    },
    {
      'symbol': '£',
      'decimalDigits': 2,
      'symbolBeforeTheNumber': true,
      'currency': 'Pound sterling'
    },
  ],

  /// Below config is used for Magento store
  'DefaultStoreViewCode': '',
  'EnableAttributesConfigurableProduct': ['color', 'size'],
  'EnableAttributesLabelConfigurableProduct': ['color', 'size'],

  //if the woo commerce website supports multi languages. set false if the website only have one language
  'isMultiLanguages': true,
  'versionCheck': {
    'iosId': 'com.inapps.medita',
    'enable': true,
    'androidId': 'com.inapps.medita',
    'iOSAppStoreCountry': 'VN',
    'androidPlayStoreCountry': 'VN'
  },
  'inAppUpdateForAndroid': {'enable': false, 'typeUpdate': 'immediate'},
};

final kAdvanceConfig = AdvanceConfig.fromJson(kAdvanceConfigRaw);


