{"HorizonLayout": [{"layout": "userCard", "config": {"margin": {"left": 16, "right": 16}}}, {"layout": "space", "config": {"percentHeight": 20}}, {"layout": "text", "config": {"text": "The problem you care \nWhat is today?", "center": false, "pinned": false, "margin": {"bottom": 20}}}, {"layout": "banner", "items": [{"urlBackground": "https://share.cleanshot.com/x2BtH3HY+", "title": "Meditation", "type": "Meditation", "shortDescription": "Meditation to reduce stress", "description": "Let us know how you are feeling?", "highlightKeywords": ["<PERSON><PERSON><PERSON><PERSON>"], "urlImageCard": "https://share.cleanshot.com/x2BtH3HY+", "courses": [{"primaryColor": "A50E31", "secondaryColor": "FF7192", "title": "An Lac in just a few minutes", "imageCard": "https://share.cleanshot.com/vlRyDWbW+", "backgroundImage": "https://share.cleanshot.com/L9G7013t+", "description": "Here are the exercises we have for you slowly listening to follow", "primaryColorItem": "731313", "secondaryColorItem": "CE4848", "lessons": [{"categoryId": "yAVZff7WpnnImdy52Z61", "id": "QrkURg_oK", "title": "An Lac in just a few minutes"}]}, {"primaryColor": "AA5709", "secondaryColor": "F6B87E", "title": "7 days of meditation from 0", "imageCard": "https://share.cleanshot.com/bC5kWScq+", "description": "Here are the exercises we have for you slowly listening to follow", "backgroundImage": "https://share.cleanshot.com/85pMghBy+", "primaryColorItem": "CA7302", "secondaryColorItem": "F9A639", "categoryId": "BJOP4O3YY7IYiq2sZwsI"}, {"primaryColor": "10A35E", "secondaryColor": "12B76A", "description": "Here are the exercises we have for you slowly listening to follow", "title": "30 days of creating meditation habit", "imageCard": "https://share.cleanshot.com/d4QSYwjy+", "backgroundImage": "https://share.cleanshot.com/7FzQSCJS+", "primaryColorItem": "125230", "secondaryColorItem": "248551", "categoryId": "1ViPI4SxPI3AfcbHhu1f", "isPaid": false}]}, {"urlBackground": "https://share.cleanshot.com/L9G7013t+", "title": "Relax", "description": "Let us know how you are feeling?", "type": "Relax", "shortDescription": "Try the method to relax the mind", "highlightKeywords": ["thả lỏng tâm trí"], "urlImageCard": "https://share.cleanshot.com/L9G7013t+", "courses": [{"primaryColor": "AA5709", "secondaryColor": "F6B87E", "title": "Deep relaxation meditation", "imageCard": "https://share.cleanshot.com/bC5kWScq+", "backgroundImage": "https://share.cleanshot.com/L9G7013t+", "description": "Here are the exercises we have for you slowly listening to follow", "primaryColorItem": "CA7302", "secondaryColorItem": "F9A639", "lessons": [{"id": "UeR-T6k8x", "categoryId": "s1WdhmJYez8kq0dQXo4M", "title": "Relax"}, {"categoryId": "yAVZff7WpnnImdy52Z61", "id": "rOdlHiGC3", "title": "Deep relaxation meditation"}, {"categoryId": "1ViPI4SxPI3AfcbHhu1f", "id": "8RpCC7ymb", "title": "Relax the whole body"}], "isPaid": false}]}, {"urlBackground": "https://share.cleanshot.com/DFQ7zqdf+", "description": "Let us know how you are feeling?", "title": "Deep sleep", "type": "Sleep", "shortDescription": "Meditation method to sleep deeply", "highlightKeywords": ["<PERSON><PERSON> thật sâu"], "urlImageCard": "https://share.cleanshot.com/DFQ7zqdf+", "courses": [{"primaryColor": "0877AB", "secondaryColor": "0877AB", "title": "4 good night's sleep", "imageCard": "https://share.cleanshot.com/Tv1xdL8v+", "backgroundImage": "https://share.cleanshot.com/DFQ7zqdf+", "description": "Here are the exercises we have for you slowly listening to follow", "primaryColorItem": "1C3D7C", "secondaryColorItem": "6093F5", "lessons": [{"categoryId": "yAVZff7WpnnImdy52Z61", "id": "sGekEIuqY"}, {"categoryId": "yAVZff7WpnnImdy52Z61", "id": "fCEUx_oJk"}, {"categoryId": "yAVZff7WpnnImdy52Z61", "id": "var5o8Yj8"}, {"categoryId": "yAVZff7WpnnImdy52Z61", "id": "JpyDKE7Di"}]}]}, {"urlBackground": "https://share.cleanshot.com/x2BtH3HY+", "title": "Meditation", "type": "Meditation", "shortDescription": "Meditation awakening intellectuals inside", "description": "Let us know how you are feeling?", "highlightKeywords": ["<PERSON><PERSON><PERSON><PERSON>"], "urlImageCard": "https://share.cleanshot.com/x2BtH3HY+", "courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "title": "Meditation French awakening", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "description": "Here are the exercises we have for you slowly listening to follow", "categoryId": "6hzQ3c9xRjuDj3flgFEN"}]}]}, {"layout": "drawCard", "config": {"margin": {"top": 50, "bottom": 30, "left": 16, "right": 16}}}, {"layout": "breathingExcercises", "config": {"margin": {"bottom": 30, "left": 16, "right": 16}}}, {"layout": "bannerRegisterPremium", "config": {"margin": {"bottom": 30, "left": 16, "right": 16}}}, {"layout": "sharingForYou", "config": {"margin": {"left": 16, "right": 16}}, "items": [{"title": "SOULCOACH 2025", "icon": "assets/icons/ic_heart.svg", "courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "QIULumxJUBYDnwaohExE", "isPaid": false}]}, {"title": "Share", "icon": "assets/icons/ic_heart.svg", "courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "GtqxHuiziOUzUkIkCAEO", "isPaid": false}]}, {"title": "Dialogue", "icon": "assets/icons/ic_spirituality.svg", "courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "OQ5q78pBUdDCfBNTBX7j", "isPaid": false}]}, {"title": "Meditation music", "icon": "assets/icons/ic_music.svg", "courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "DpT163QEB2WCGiob4pO3", "isPaid": false}]}, {"title": "Meditation awakening", "icon": "assets/icons/ic_bling.svg", "courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "qSxB26u9uHIX5otSkVe2", "isPaid": false}]}]}], "Setting": {"MainColor": "#22521F", "ProductListLayout": "list", "StickyHeader": false}, "SplashScreen": {"Data1": "assets/images/splashscreen.flr", "Type1": "flare", "Data2": "assets/images/splashscreen.png", "Data": "assets/img/round_logo.png", "Type": "animated"}, "IsRequiredLogin": true, "OnBoardOnlyShowFirstTime": true, "TabBar": [{"layout": "home", "default_selected": true, "icon": "assets/icons/ic_home.png"}, {"layout": "alarm", "icon": "assets/icons/ic_alarm.svg"}, {"layout": "advancedMeditation", "icon": "assets/icons/ic_yoga.svg", "data": {"courses": [{"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "kY0i9BiqDXl3VLgdqQeB", "isPaid": true}, {"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "Ozwo6TZN3BtGXNSkSUYB", "isPaid": true}, {"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "zAk0TxMkUoaBeiTXCIDJ", "isPaid": true}, {"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "VwaZwxm861mN1h0IkHol", "isPaid": true}, {"primaryColor": "5F1E95", "secondaryColor": "C481FC", "imageCard": "https://share.cleanshot.com/SsN3JFG9+", "backgroundImage": "https://share.cleanshot.com/91Yp9rtf+", "primaryColorItem": "6D29A5", "secondaryColorItem": "8742C1", "categoryId": "s1WdhmJYez8kq0dQXo4M", "isPaid": true}]}}, {"layout": "settings", "icon": "assets/icons/ic_user.svg"}], "OnBoarding": {"data": [{"title": "Welcome to FluxStore", "image": "assets/images/fogg-delivery-1.png", "desc": "Fluxstore is on the way to serve you. ", "background": "#FFF3F2"}, {"title": "Connect Surrounding World", "image": "assets/images/fogg-uploading-1.png", "desc": "See all things happening around you just by a click in your phone. Fast, convenient and clean.", "background": "#F2FFFC"}, {"title": "Let's Get Started", "image": "fogg-order-completed.png", "desc": "Waiting no more, let's see what we get!", "background": "#F9F2FF"}]}}