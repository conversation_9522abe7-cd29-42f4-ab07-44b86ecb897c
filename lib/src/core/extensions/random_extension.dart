import 'dart:math';

extension RadomExt on Random {
  (int, int) nextRange(int maxValue, {int? maxRange, int minRange = 1}) {
    final random = Random();
    final maxRangeValue = maxRange ?? maxValue;
    var startRandom = random.nextInt(maxValue);
    var endRandom = random.nextInt(maxValue);
    var temp = startRandom;

    startRandom = min(startRandom, endRandom);
    endRandom = max(temp, endRandom);

    if (startRandom == endRandom) {
      if (endRandom + 1 >= maxValue) {
        endRandom -= 1;
        startRandom -= 1;
      } else {
        endRandom += 1;
        startRandom += 1;
      }
    }

    if (endRandom - startRandom > maxRangeValue) {
      endRandom = startRandom + maxRangeValue;
    }

    while (endRandom - startRandom < minRange) {
      if (endRandom + 1 >= maxRangeValue && startRandom > 0) {
        startRandom -= 1;
      } else {
        endRandom += 1;
      }
    }

    return (startRandom, endRandom);
  }
}
