import 'package:intl/intl.dart';

extension DateTimeExt on DateTime {
  int get dayOfYear => int.parse(DateFormat('D').format(this));

  // String todayBannerImage() {
  //   // Get day of year
  //   final index = dayOfYear % bannerImageCount + 1;
  //   return '$bannerImagePath$index.png';
  // }

  // String todayFullBannerImage() {
  //   // Get day of year
  //   final index = dayOfYear % bannerImageCount + 1;
  //   return '$fullBannerImagePath$index.png';
  // }
}
