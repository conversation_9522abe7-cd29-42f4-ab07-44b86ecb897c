// extension StringExtensions on String {
//   bool get hasOnlyWhitespaces => trim().isEmpty && isNotEmpty;

//   String toSpaceSeparated() {
//     final value =
//         replaceAllMapped(RegExp(r'.{4}'), (match) => '${match.group(0)} ');
//     return value;
//   }

//   String formatCopy() {
//     return replaceAll('},', '\n},\n')
//         .replaceAll('[{', '[\n{\n')
//         .replaceAll(',\"', ',\n\"')
//         .replaceAll('{\"', '{\n\"')
//         .replaceAll('}]', '\n}\n]');
//   }

//   bool get isNoInternetError => contains('SocketException: Failed host lookup');

//   bool get isURLImage =>
//       (isNotEmpty) && (contains('http') || contains('https'));
// }

import '../../presentation/shared/account/account_bloc.dart';
import '../lib_common.dart';

extension StringExt on String {
  bool get isAudio {
    for (var element in kExtensionAudio) {
      if (contains(element)) {
        return true;
      }
    }
    return false;
  }

  String get mediaLinkFirebase =>
      'https://firebasestorage.googleapis.com/v0/b/${AccountBloc.firebaseProjectID}.appspot.com/o/flamelink%2Fmedia%2F${Uri.encodeComponent(this)}?alt=media';
  // String get mediaDevLinkFirebase =>
  //     'https://firebasestorage.googleapis.com/v0/b/dev-meditaapp.appspot.com/o/flamelink%2Fmedia%2F$this?alt=media';

  String get clearLogError {
    var log = this;
    final isFromFirebase = log.contains('[firebase');
    if (isFromFirebase) {
      final indexStart = indexOf(' ');
      log = log.substring(indexStart + 1);
    }

    return log;
  }
}
