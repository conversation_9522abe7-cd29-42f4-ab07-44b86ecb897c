final Map<String, dynamic> environmentDev = {
  'name': 'MeditaDev',
  'environment': 'dev',
  'isPremium': false,
  'timeDrawCard': 86400,
  'devConfig': {'username': '<EMAIL>', 'password': '123123123'},
  'emailSupport': [
    '<EMAIL>',
    '<EMAIL>',
  ],
  'firebaseConfig': {
    'name': 'MeditaDev',
    'apiKey': 'AIzaSyBWHN971l2-xm1EH9kuWTrlsPLBIeXs0o8',
    'appIosId': '1:166533544969:ios:eb826f041d69093eb38cd8',
    'appAndroidId': '1:166533544969:android:0b059af3c897a142b38cd8',
    'messagingSenderId': '166533544969',
    'projectId': 'dev-meditaapp',
    'storageBucket': 'dev-meditaapp.appspot.com',
    'androidClientId':
        '166533544969-41ect7tnc00daoiqbkr1jsdngra70f2n.apps.googleusercontent.com',
    'iosClientId':
        '166533544969-9h7qg2nslksmklk7j3b7klg0hb1ltag9.apps.googleusercontent.com',
    'iosBundleId': 'com.inapps.medita.dev',
    'androidBundleId': 'com.inapps.medita.dev',
  },
};
