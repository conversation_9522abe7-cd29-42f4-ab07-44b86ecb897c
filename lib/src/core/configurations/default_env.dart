import 'package:meditaguru/src/core/configurations/entities/firebase_config.dart';

class DefaultConfig {
  static const String name = 'studio';
  static const String environment = 'dev';
  static const bool isStudio = false;
  static const int timeDrawCard = 86400;
  static const List<String> emailSupport = ['<EMAIL>'];
  static FirebaseConfig firebaseConfig = FirebaseConfig.fromJson({
    'name': 'MeditaDev',
    'environment': 'dev',
    'firebaseConfig': {
      'apiKey': 'AIzaSyBWHN971l2-xm1EH9kuWTrlsPLBIeXs0o8',
      'appId': '1:166533544969:android:0b059af3c897a142b38cd8',
      'messagingSenderId': '166533544969',
      'projectId': 'dev-meditaapp',
      'storageBucket': 'dev-meditaapp.appspot.com',
      'androidClientId': '',
      'iosClientId': '',
      'iosBundleId': 'com.inapps.medita.dev',
      'androidBundleId': 'com.inapps.medita.dev',
    },
  });
}
