class DevConfig {
  final String? username;
  final String? password;

  DevConfig({
    this.username,
    this.password,
  });

  Map<String, dynamic> to<PERSON>son() {
    final result = <String, dynamic>{};

    if (username != null) {
      result.addAll({'username': username});
    }
    if (password != null) {
      result.addAll({'password': password});
    }

    return result;
  }

  factory DevConfig.fromJson(Map<String, dynamic> map) {
    return DevConfig(
      username: map['username'],
      password: map['password'],
    );
  }
}
