class FirebaseConfig {
  final String apiKey;
  final String appIosId;
  final String appAndroidId;
  final String messagingSenderId;
  final String projectId;
  final String storageBucket;
  final String androidClientId;
  final String iosClientId;
  final String iosBundleId;
  final String androidBundleId;
  final String? name;

  FirebaseConfig({
    required this.apiKey,
    required this.appIosId,
    required this.appAndroidId,
    required this.messagingSenderId,
    required this.projectId,
    required this.storageBucket,
    required this.androidClientId,
    required this.iosClientId,
    required this.iosBundleId,
    required this.androidBundleId,
    this.name,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'apiKey': apiKey})
      ..addAll({'appIosId': appIosId})
      ..addAll({'appAndroidId': appAndroidId})
      ..addAll({'messagingSenderId': messagingSenderId})
      ..addAll({'projectId': projectId})
      ..addAll({'storageBucket': storageBucket})
      ..addAll({'androidClientId': androidClientId})
      ..addAll({'iosClientId': iosClientId})
      ..addAll({'androidBundleId': androidBundleId})
      ..addAll({'name': name})
      ..addAll({'iosBundleId': iosBundleId});

    return result;
  }

  factory FirebaseConfig.fromJson(Map<String, dynamic> map) {
    return FirebaseConfig(
      apiKey: map['apiKey'] ?? '',
      appIosId: map['appIosId'] ?? '',
      appAndroidId: map['appAndroidId'] ?? '',
      messagingSenderId: map['messagingSenderId'] ?? '',
      projectId: map['projectId'] ?? '',
      storageBucket: map['storageBucket'] ?? '',
      androidClientId: map['androidClientId'] ?? '',
      iosClientId: map['iosClientId'] ?? '',
      iosBundleId: map['iosBundleId'] ?? '',
      androidBundleId: map['androidBundleId'] ?? '',
      name: map['name'],
    );
  }
}
