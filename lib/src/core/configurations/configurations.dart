import 'default_env.dart';
import 'entities/dev_config.dart';
import 'entities/firebase_config.dart';

class Configurations {
  static String _name = DefaultConfig.name;
  static String _environment = DefaultConfig.environment;
  static bool _isStudio = DefaultConfig.isStudio;
  static int _timeDrawCard = DefaultConfig.timeDrawCard;
  static bool _isPremium = false;
  static DevConfig? _devConfig;
  static List<String> _emailSupport = DefaultConfig.emailSupport;
  static FirebaseConfig _firebaseConfig = DefaultConfig.firebaseConfig;

  void setConfigurationValues(Map<String, dynamic> value) {
    _name = value['name'] ?? DefaultConfig.name;
    _environment = value['environment'] ?? DefaultConfig.environment;
    _isStudio = value['isStudio'] ?? DefaultConfig.isStudio;
    _isPremium = value['isPremium'] ?? false;
    _timeDrawCard = value['timeDrawCard'] ?? DefaultConfig.timeDrawCard;
    _emailSupport = value['emailSupport'] ?? DefaultConfig.emailSupport;
    _devConfig = value['devConfig'] != null
        ? DevConfig.fromJson(value['devConfig'])
        : null;
    _firebaseConfig = value['firebaseConfig'] != null
        ? FirebaseConfig.fromJson(value['firebaseConfig'])
        : DefaultConfig.firebaseConfig;
  }

  static String get name => _name;
  static String get environment => _environment;
  static bool get isStudio => _isStudio;
  static bool get isDev => environment == 'dev';
  static bool get isPremium => isDev && _isPremium;
  static int get timeDrawCard => _timeDrawCard;
  static DevConfig? get devConfig => isDev ? _devConfig : null;
  static FirebaseConfig get firebaseConfig => _firebaseConfig;
  static List<String> get emailSupports => _emailSupport;
}
