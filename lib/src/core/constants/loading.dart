import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:meditaguru/meditaguru.dart';
// import 'package:pull_to_refresh/pull_to_refresh.dart';

/// For Loading Widget
Widget kLoadingWidget(context,
        {Color color = Colors.white, double size = 30.0}) =>
    Center(
      child: SpinKitFadingCube(
        color: color,
        size: size,
      ),
    );

Widget kLoadingCircleWidget(context, {double size = 48.0}) => Container(
      alignment: Alignment.center,
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          width: 1.0,
          color: AppColors.secondary2Color,
        ),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFF6FC9C).withOpacity(0.5),
            offset: const Offset(-1.0, 1.0),
            blurRadius: 30.0,
          ),
        ],
      ),
      child: kLoadingWidget(
        context,
        color: Colors.black54,
        size: size * 0.4,
      ),
    );

// Widget kLoadingWhiteWidget(context) => const Center(
//       child: SpinKitFadingCube(
//         color: AppColors.whiteColor,
//         size: 30.0,
//       ),
//     );

// Widget kCustomFooter(context) => CustomFooter(
//       builder: (BuildContext context, LoadStatus mode) {
//         Widget body;
//         if (mode == LoadStatus.idle) {
//           body = Text(S.of(context).pullToLoadMore);
//         } else if (mode == LoadStatus.loading) {
//           body = kLoadingWidget(context);
//         } else if (mode == LoadStatus.failed) {
//           body = Text(S.of(context).loadFail);
//         } else if (mode == LoadStatus.canLoading) {
//           body = Text(S.of(context).releaseToLoadMore);
//         } else {
//           body = Text(S.of(context).noData);
//         }
//         return Container(
//           height: 55.0,
//           child: Center(child: body),
//         );
//       },
//     );
