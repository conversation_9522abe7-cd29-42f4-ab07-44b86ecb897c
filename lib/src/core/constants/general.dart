import 'package:universal_platform/universal_platform.dart';

/// Logging config
const kLogTag = '[AppName]';
const kLogEnable = true;

void printLog(dynamic data) {
  if (kLogEnable) {
    // ignore: avoid_print
    print('[${DateTime.now().toUtc()}]$kLogTag${data.toString()}');
  }
}

/// check if the environment is web
final bool kIsWeb = UniversalPlatform.isWeb;
final bool isIos = UniversalPlatform.isIOS;
final bool isAndroid = UniversalPlatform.isAndroid;
final bool isMacOS = UniversalPlatform.isMacOS;
final bool isWindow = UniversalPlatform.isWindows;
final bool isFuchsia = UniversalPlatform.isFuchsia;

const kLocalStorageName = 'TrungSonStorage';

const kExtensionAudio = [
  '.amr',
  '.ogg',
  '.m4a',
  '.3gp',
  '.aac',
  '.mp3',
  '.wav',
  '.flac'
];

const kBreathingCircle = 11;
final double kBreathingOneStep = kBreathingCircle / 2;
const kBreathingCount = 10;
