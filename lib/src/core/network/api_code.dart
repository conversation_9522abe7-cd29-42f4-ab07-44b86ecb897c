// ignore_for_file: constant_identifier_names

const int INTERNAL_SERVER_ERROR = 500;
const int BAD_REQUEST = 400;
const int UNAUTHORIZE = 401;

const int NOT_FOUND = 404;
const int OK = 200;
const int CREATED = 201;

// custom error code
const int NET_WORK = -200;
const int kTypeError = -100;
const int FAILED = -1;
const int IS_EXISTED = -2;
const int PASSWORD_OR_USERNAME_INCORRECT = -3;
const int REFERRAL_CODE_NOTE_EXIST = -4;
const int EMAIL_INCORRECT = -5;
const int ACCOUNT_IS_LOCKED = -6;
const int OTP_EXPIRED_OR_INVALID = -7;
const int OTP_BY_DAY_LIMIT = -8;
const int REFERRAL_CODE_PARENT_BLOCKED = -9;
const int PASSWORD_INCORRECT = -10;
