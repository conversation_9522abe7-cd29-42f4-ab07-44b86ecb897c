// To parse this JSON data, do
//
//     final responseError = responseErrorFromJson(jsonString);

import 'dart:convert';

ResponseError responseErrorFromJson(String str) =>
    ResponseError.fromJson(json.decode(str));

String responseErrorToJson(ResponseError data) => json.encode(data.toJson());

class ResponseError {
  ResponseError({
    this.message,
    this.statusCode,
    this.errorCode,
  });

  String? message;
  int? statusCode;
  int? errorCode;

  factory ResponseError.fromJson(Map<String, dynamic> json) => ResponseError(
        message: json['message'],
        statusCode: json['status_code'],
        errorCode: json['error_code'],
      );

  Map<String, dynamic> toJson() => {
        'message': message,
        'status_code': statusCode,
        'error_code': errorCode,
      };
}
