import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:meditaguru/src/core/utils/logs.dart';

class EmailSenderService {
  static Future<void> sendMail(
      {String subject = '',
      List<String> recipients = const <String>[],
      String body = ''}) async {
    try {
      var email = Email(
        body: body,
        subject: subject,
        recipients: recipients,
        cc: [''],
        bcc: [''],
        isHTML: false,
      );

      await FlutterEmailSender.send(email);
    } catch (e) {
      printError('[EmailSenderService] Error: $e');
    }
  }
}
