import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:csv/csv.dart';

class CsvService {
  Future<bool> createCsv(List<Map> data,
      {required String saveTo, bool addTitle = true}) async {
    final contentCSV = <List>[];
    final title = [];
    if (data.isEmpty) {
      return false;
    }
    try {
      ///
      for (final item in data) {
        final dataLine = [];
        final contentTitle = [];

        ///
        for (final itemOfLine in item.entries) {
          if (title.isEmpty && addTitle) {
            contentTitle.add(itemOfLine.key);
          }

          ///
          dataLine.add(itemOfLine.value);
        }

        ///
        if (title.isEmpty && addTitle) {
          title.addAll(contentTitle);
          contentCSV.add(title);
        }

        ///
        contentCSV.add(dataLine);
      }
      contentCSV.add([]);

      final csv = const ListToCsvConverter().convert(contentCSV);
      final f = File(saveTo);
      final isExistFile = await f.exists();
      if (isExistFile == false) {
        await f.create(recursive: true);
      }
      var excelCsvBytes = <int>[];
      if (addTitle) {
        excelCsvBytes = [0xEF, 0xBB, 0xBF, ...utf8.encode(csv)];
      } else {
        excelCsvBytes.addAll(utf8.encode(csv));
      }

      await f.writeAsBytes(excelCsvBytes, mode: FileMode.append);

      return true;
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> createTitleCsv(List<Map> data, {required String saveTo}) async {
    final contentCSV = <List>[];
    if (data.isEmpty) {
      return false;
    }
    try {
      ///
      final contentTitle = [];
      final contentDescription = [];
      for (final item in data) {
        contentTitle.add(item.entries.first.key);
        contentDescription.add(item.entries.first.value);
      }
      contentCSV
        ..add(contentTitle)
        ..add(contentDescription);

      final csv = const ListToCsvConverter().convert(contentCSV);
      final f = File(saveTo);
      final isExistFile = await f.exists();
      if (isExistFile == false) {
        await f.create(recursive: true);
      }
      var excelCsvBytes = <int>[];
      excelCsvBytes = [
        0xEF,
        0xBB,
        0xBF,
        ...utf8.encode(csv),
        ...utf8.encode('\n')
      ];
      await f.writeAsBytes(excelCsvBytes, mode: FileMode.append);
      return true;
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> readCsv(String pathFile,
      {bool readTitle = true}) async {
    final result = <Map<String, dynamic>>[];
    try {
      final input = File(pathFile).openRead();
      final fields = await input
          .transform(utf8.decoder)
          .transform(const CsvToListConverter())
          .toList();

      ///
      final titleContent = fields.first;

      ///
      for (var indexLine = 0; indexLine < fields.length; indexLine++) {
        final item = <String, dynamic>{};
        final lineItem = fields[indexLine];

        ///
        for (var i = 0; i < lineItem.length; i++) {
          final element = _convertData(lineItem[i]);
          var title = 'index-$i';

          if (readTitle) {
            title = titleContent[i].toString();
          }
          item.addAll({title: element});
        }

        ///
        if (item.isNotEmpty) {
          result.add(item);
        }
      }
    } catch (e) {
      log('Error: $e');
    }

    return result;
  }

  Future<List<String>> readListDataInCsv(String pathFile) async {
    final result = <String>[];
    try {
      final input = File(pathFile).openRead();
      final fields = await input
          .transform(utf8.decoder)
          .transform(const CsvToListConverter())
          .toList();

      ///

      ///
      for (var indexLine = 0; indexLine < fields.length; indexLine++) {
        final item = <String>[];
        final lineItem = fields[indexLine];

        ///
        for (var i = 0; i < lineItem.length; i++) {
          if (lineItem[i] is String) {
            final listData = (lineItem[i] as String).split('\n');
            item.addAll(listData);
          }
        }

        ///
        if (item.isNotEmpty) {
          result.addAll(item);
        }
      }
    } catch (e) {
      log('Error: $e');
    }

    return result;
  }

  dynamic _convertData(dynamic data) {
    if (data is String) {
      return jsonEncode(data).replaceAll('"', '');
    }
    return data;
  }
}
