import 'package:package_info_plus/package_info_plus.dart';

class AppInfoService {
  static Future<AppInfoModel> getAppInfo() async {
    var packageInfo = await PackageInfo.fromPlatform();
    return AppInfoModel(
        version: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
        packageName: packageInfo.packageName,
        appName: packageInfo.appName);
  }
}

class AppInfoModel {
  String? version;
  String? buildNumber;
  String? packageName;
  String? appName;

  bool get hasVersion => version != null && buildNumber != null;

  AppInfoModel(
      {this.version, this.buildNumber, this.packageName, this.appName});

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{};

    if (version != null) {
      result.addAll({'version': version});
    }
    if (buildNumber != null) {
      result.addAll({'buildNumber': buildNumber});
    }
    if (packageName != null) {
      result.addAll({'packageName': packageName});
    }
    if (appName != null) {
      result.addAll({'appName': appName});
    }

    return result;
  }

  factory AppInfoModel.fromJson(Map<String, dynamic> map) {
    return AppInfoModel(
      version: map['version'],
      buildNumber: map['buildNumber'],
      packageName: map['packageName'],
      appName: map['appName'],
    );
  }
}
