import 'package:firebase_remote_config/firebase_remote_config.dart';

class FirebaseRemoteServices {
  String getString(String key) => '';

  Future<bool> loadRemoteConfig() async => false;

  Future<List<String>> getKeys() async => [];
}

/// Implemennt Firebase Remote Config
///
class FirebaseRemoteServicesImpl extends FirebaseRemoteServices {
  @override
  String getString(String key) {
    return FirebaseRemoteConfig.instance.getString(key);
  }

  @override
  Future<bool> loadRemoteConfig() async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;

      await remoteConfig.fetch();
      await remoteConfig.activate();
      final values = remoteConfig.getAll();
      print('values: $values');
      return true;
    } catch (e) {
      print('Unable to fetch remote config. Default value will be used. $e');
    }

    return false;
  }

  @override
  Future<List<String>> getKeys() async {
    return FirebaseRemoteConfig.instance.getAll().keys.toList();
  }
}
