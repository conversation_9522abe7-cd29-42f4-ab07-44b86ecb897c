import 'package:firebase_core/firebase_core.dart';
import 'package:meditaguru/src/core/configurations/configurations.dart';
import 'package:meditaguru/src/core/configurations/entities/firebase_config.dart';
import 'package:universal_platform/universal_platform.dart';

class FirebaseCoreService {
  static Future<void> init() async {
    await Firebase.initializeApp(
      options: Configurations.firebaseConfig.toOptions(),
      name: Configurations.firebaseConfig.nameApp,
    );
  }
}

extension FirebaseConfigConverter on FirebaseConfig {
  bool get _isAndroid => UniversalPlatform.isAndroid;

  String? get nameApp => (Configurations.isDev || _isAndroid) ? name : null;

  FirebaseOptions toOptions() => FirebaseOptions(
        apiKey: apiKey,
        appId: _isAndroid ? appAndroidId : appIosId,
        messagingSenderId: messagingSenderId,
        projectId: projectId,
        storageBucket: storageBucket,
        androidClientId: androidClientId,
        iosClientId: iosClientId,
        iosBundleId: iosBundleId,
      );
}
