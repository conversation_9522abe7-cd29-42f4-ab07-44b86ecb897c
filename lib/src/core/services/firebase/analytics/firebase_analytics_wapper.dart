import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

abstract class FirebaseAnalyticsAbs {
  Future<void> init() async {}
  List<NavigatorObserver> getMNavigatorObservers() {
    return const <NavigatorObserver>[];
  }
}

class FirebaseAnalyticsWapper extends FirebaseAnalyticsAbs {
  static final FirebaseAnalyticsWapper _singleton =
      FirebaseAnalyticsWapper._internal();

  factory FirebaseAnalyticsWapper() {
    return _singleton;
  }

  FirebaseAnalyticsWapper._internal();

  // https://pub.dev/packages/firebase_analytics/example
  late FirebaseAnalytics analytics;

  @override
  Future<void> init() {
    analytics = FirebaseAnalytics.instance;
    return super.init();
  }

  @override
  List<FirebaseAnalyticsObserver> getMNavigatorObservers() {
    return [
      FirebaseAnalyticsObserver(analytics: analytics),
    ];
  }
}
