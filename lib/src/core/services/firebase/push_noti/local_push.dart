import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_native_timezone_latest/flutter_native_timezone_latest.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';

// import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

// first add this code to application tag for chat chanel:
// <meta-data
//   android:name="com.google.firebase.messaging.default_notification_channel_id"
//   android:value="high_importance_channel" />
const AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel', // id
  'High Importance Notifications', // title
  description: 'This channel is used for important notifications.',
  // description
  importance: Importance.max,
);

abstract class LocalPushService {
  LocalPushServicegDelegate? delegate;

  Future<void> init();

  Future<void> showLocalNotificationForegroundAndroid(RemoteMessage message);

  Future<void> requestPermissions();

  Future<void> cancelAllNotifications();

  Future<void> showNotification(
      {String title = '', String body = '', String payload = ''});

  Future<void> cancel(int id);

  Future<void> scheduledalarm(int id, TimeOfDay timeofalarm, String showtime);

  Future<void> showNotificationCustomSound();
}

abstract class LocalPushServicegDelegate {
  void onSelectNotification(String payload);

  void onDidReceiveLocalNotification(
      int id, String? title, String? body, String? payload);
}

class LocalPushServiceImpl extends LocalPushService {
  // Create the channel on the device (if a channel with an id already exists, it will be updated):
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  @override
  Future<void> init() async {
    try {
      await _configureLocalTimeZone();
    } catch (e) {
      print(e);
    }
    // initialise the plugin. app_icon needs to be a added as a drawable resource to the Android head project
    const initializationSettingsAndroid =
        AndroidInitializationSettings('app_icon');

    final initializationSettingsIOS = const DarwinInitializationSettings(
      requestSoundPermission: false,
      requestBadgePermission: false,
      requestAlertPermission: false,
      defaultPresentAlert: true,
      defaultPresentBadge: true,
      // defaultPresentSound: true,
      // onDidReceiveLocalNotification:
      //     (int id, String? title, String? body, String? payload) async {
      //   delegate?.onDidReceiveLocalNotification(
      //       id, title ?? '', body ?? '', payload ?? '');
      // },
    );

    final initializationSettingsMacos = const DarwinInitializationSettings(
      requestSoundPermission: false,
      requestBadgePermission: false,
      requestAlertPermission: false,
      defaultPresentAlert: true,
      defaultPresentBadge: true,
      // defaultPresentSound: true,
      // onDidReceiveLocalNotification:
      //     (int id, String? title, String? body, String? payload) async {
      //   delegate?.onDidReceiveLocalNotification(
      //       id, title ?? '', body ?? '', payload ?? '');
      // },
    );
    final initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
      macOS: initializationSettingsMacos,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: (payload) async {
      delegate?.onSelectNotification(payload.payload ?? '');
    });

    // create notification channel
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<void> _configureLocalTimeZone() async {
    tz.initializeTimeZones();
    final timeZoneName = await FlutterNativeTimezoneLatest.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }

  @override
  Future<bool?> requestPermissions() async {
    return await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  @override
  Future<void> showLocalNotificationForegroundAndroid(
      RemoteMessage message) async {
    if (isAndroid) {
      var notification = message.notification;
      var android = message.notification?.android;

      // If `onMessage` is triggered with a notification, construct our own
      // local notification to show to users using the created channel.
      if (notification != null && android != null) {
        await flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            NotificationDetails(
              android: AndroidNotificationDetails(
                channel.id,
                channel.name,
                channelDescription: channel.description,
                icon: 'mipmap/ic_launcher',
                priority: Priority.max,
                // other properties...
              ),
            ),
            payload: json.encode(message.data));
      }
    }
  }

  @override
  Future<void> cancelAllNotifications() async {
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  @override
  Future<void> cancel(int id) async {
    await flutterLocalNotificationsPlugin.cancel(id);
  }

  @override
  Future<void> showNotification(
      {String title = '', String body = '', String payload = ''}) async {
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      channel.id,
      channel.name,
      channelDescription: channel.description,
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
    );
    var iOSPlatformChannelSpecifics =
        const DarwinNotificationDetails(presentAlert: true, presentBadge: true);
    var platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics);
    await flutterLocalNotificationsPlugin
        .show(0, title, body, platformChannelSpecifics, payload: payload);
  }

  @override
  Future<void> showNotificationCustomSound() async {
    const androidPlatformChannelSpecifics = AndroidNotificationDetails(
      'your other channel id',
      'your other channel name',
      channelDescription: 'your other channel description',
      sound: RawResourceAndroidNotificationSound('sd'),
    );
    const iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(sound: 'sd.aiff');
    const macOSPlatformChannelSpecifics =
        DarwinNotificationDetails(sound: 'sd.aiff');
    const platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
        macOS: macOSPlatformChannelSpecifics);
    await flutterLocalNotificationsPlugin.show(
        0,
        'custom sound notification title',
        'custom sound notification body',
        platformChannelSpecifics);
  }

  @override
  Future<void> scheduledalarm(
      int id, TimeOfDay timeofalarm, String showtime) async {
    // var androidPlatformChannelSpecifics = AndroidNotificationDetails(
    //     channelSound.id, channelSound.name, channelSound.description,
    //     sound: RawResourceAndroidNotificationSound('sd'),
    //     autoCancel: true,
    //     playSound: true,
    //     color: AppColors.primaryColor,
    //     importance: Importance.max,
    //     priority: Priority.high);
    //
    // var iOSPlatformChannelSpecifics = IOSNotificationDetails(
    //     sound: "sd.aiff",
    //     presentSound: true
    // );
    const androidPlatformChannelSpecifics = AndroidNotificationDetails(
      'medita_alarm_channel',
      'Medita alarm channel',
      channelDescription: 'Medita alarm channel',
      sound: RawResourceAndroidNotificationSound('sd'),
    );
    const iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(sound: 'sd.aiff');

    await flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        S.current.timeToMeditate,
        '$showtime',
        _nextInstanceOfHour(timeofalarm.hour, timeofalarm.minute),
        const NotificationDetails(
            android: androidPlatformChannelSpecifics,
            iOS: iOSPlatformChannelSpecifics),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time,
        payload: 'alarm');

    print('alarm set');
  }

  tz.TZDateTime _nextInstanceOfHour(int hour, int minutes) {
    final now = tz.TZDateTime.now(tz.local);
    var scheduledDate =
        tz.TZDateTime(tz.local, now.year, now.month, now.day, hour, minutes, 0);
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
    return scheduledDate;
  }
}
