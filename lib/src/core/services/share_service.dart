import 'dart:ui';

import 'package:share_plus/share_plus.dart';

import '../utils.dart';

class ShareService {
  static Future<bool> share({
    String? text,
    String? subject = '',
    Rect? sharePositionOrigin,
  }) async {
    var isSuccess = false;
    try {
      await SharePlus.instance.share(ShareParams(
        text: text ?? '',
        subject: subject,
        sharePositionOrigin: sharePositionOrigin,
      ));
      isSuccess = true;
    } catch (e) {
      Log.printSimpleLog(e.toString());
    }
    return isSuccess;
  }
}
