import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:meditaguru/src/core/constants/general.dart';

import 'app_info_service.dart';

class DeviceInfoService {
  static DeviceInfoModel _deviceInfo = DeviceInfoModel();
  static AppInfoModel _appInfo = AppInfoModel();

  static DeviceInfoModel get deviceInfo => _deviceInfo;
  static AppInfoModel get appInfo => _appInfo;

  static Future<DeviceInfoModel> getDeviceInfo() async {
    String? deviceName;
    String? deviceVersion;
    String? identifier;
    int? osType;
    var deviceInfoPlugin = DeviceInfoPlugin();
    _appInfo = await AppInfoService.getAppInfo();

    try {
      if (Platform.isAndroid) {
        osType = 0;
        var build = await deviceInfoPlugin.androidInfo;
        deviceName = build.model;
        deviceVersion = build.version.sdkInt.toString();
        identifier = ''; //UUID for Android
      } else if (Platform.isIOS) {
        osType = 1;
        var data = await deviceInfoPlugin.iosInfo;
        deviceName = data.name;
        deviceVersion = data.systemVersion;
        identifier = data.identifierForVendor; //UUID for iOS
      }
    } catch (error) {
      printLog(error);
    }

    _deviceInfo = DeviceInfoModel(
      deviceName: deviceName,
      deviceVersion: deviceVersion,
      deviceId: identifier,
      osType: osType,
    );

    return _deviceInfo;
  }
}

class DeviceInfoModel {
  DeviceInfoModel({
    this.deviceName,
    this.deviceVersion,
    this.deviceId,
    this.osType,
  });

  String? deviceName;
  String? deviceVersion;
  String? deviceId;
  int? osType;

  Map<String, dynamic> toJson() => {
        'deviceName': deviceName,
        'deviceVersion': deviceVersion,
        'deviceId': deviceId,
        'osType': osType,
      };
}
