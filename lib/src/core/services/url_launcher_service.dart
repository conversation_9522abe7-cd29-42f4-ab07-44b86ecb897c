import 'package:meditaguru/src/core/constants/general.dart';
import 'package:url_launcher/url_launcher.dart';

class UrlLauncherService {
  static Future<bool> launchURL(
    String url, {
    LaunchMode mode = LaunchMode.externalApplication,
  }) async {
    try {
      await launchUrl(Uri.parse(url), mode: mode);
    } catch (_) {
      printLog('Could not launch $url');
    }
    return true;
  }

  static Future<bool> launchEmail(String email, String subject) async {
    return await launchEmail(email, subject);
  }

  static Future<bool> launchPhone(String phone) async {
    return await launchPhone('tel:$phone');
  }

  static Future<bool> launchSMS(String phone) async {
    return await launchSMS('sms:$phone');
  }

  static Future<bool> launchMap(double latitude, double longitude) async {
    var googleUrl =
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
    if (await canLaunchUrl(Uri(path: googleUrl))) {
      await launchMap(latitude, longitude);
      return true;
    } else {
      // throw 'Could not open the map.';
      return false;
    }
  }

  static Future<bool> canLaunch(Uri uri) async {
    return await canLaunchUrl(uri);
  }

  static Future<bool> launch(Uri uri) async {
    return await launchUrl(uri);
  }
}
