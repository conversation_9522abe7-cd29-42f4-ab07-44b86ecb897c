import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:crypto/crypto.dart';
import 'package:log_webhook/log_webhook.dart';

import '../../di/injection/injection.dart';
import '../../presentation/shared/account/account_bloc.dart';
import 'device_info_service.dart';

class TrackingLogService {
  final String _url;

  TrackingLogService(this._url) {
    LogWebhook.setUrl(_url);
  }

  Future<void> sendLog(
      String key, String project, Map<String, dynamic> logs) async {
    await LogWebhook.sendLog(key: key, project: project, logs: logs);
  }

  void sendCustomLog(Map<String, dynamic> logs,
      {bool isCheckConnection = false}) async {
    final user = injector.get<AccountBloc>().userFull;
    var result =
        isCheckConnection ? await Connectivity().checkConnectivity() : null;

    final token = _createToken();
    LogWebhook.setHeaders({
      's-key': token,
    });

    final hashUser =
        (user.name?.isNotEmpty ?? false) && (user.phone?.isNotEmpty ?? false);

    LogWebhook.sendLogCustom({
      'version': '1.0.0+3',
      'logs': {
        'project': 'MeditaionApp',
        if (DeviceInfoService.appInfo.hasVersion)
          'appVersion':
              'v${DeviceInfoService.appInfo.version}+${DeviceInfoService.appInfo.buildNumber}',
        'time': DateTime.now().toUtc().toString(),
        'info': {
          if (result != null) 'statusConnect': result.first.name,
          'device': DeviceInfoService.deviceInfo.toJson(),
          if (hashUser)
            'user': {
              'email': user.email,
              'phone': user.phone,
            },
        },
        'logs': logs,
      },
    });
  }

  String _createToken() {
    final timecurrent = DateTime.now().toUtc();
    var timeText = timecurrent.minute.toString();
    if (timecurrent.minute < 10) {
      timeText = '0$timeText';
    }
    if (timecurrent.hour < 10) {
      timeText = '${timeText}0${timecurrent.hour}';
    } else {
      timeText = '$timeText${timecurrent.hour}';
    }

    final timeHash = 'hashTim3:$timeText';
    var bytes = utf8.encode(timeHash);
    var digest = sha1.convert(bytes);
    String rePassword = digest.toString();
    String passwordReverse = rePassword.split('').reversed.join('');
    List<String> passwordArray = [
      passwordReverse.substring(32, 40),
      passwordReverse.substring(19, 32),
      passwordReverse.substring(10, 19),
      passwordReverse.substring(0, 10),
    ];

    return passwordArray.join('');
  }
}
