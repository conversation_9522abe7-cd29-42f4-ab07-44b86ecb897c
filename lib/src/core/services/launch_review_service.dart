import 'dart:io';

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/wrappers/toast_message/toast_message.dart';
import 'package:rate_my_app/rate_my_app.dart';

import '../../../generated/l10n.dart';

const _googlePlayIdentifier = 'com.inapps.medita';
const _appStoreIdentifier = '1547936055';


final _rateMyApp = RateMyApp(
  // rate app on store
  // minDays: _kAppRatingConfig.minDays,
  // minLaunches: _kAppRatingConfig.minLaunches,
  // remindDays: _kAppRatingConfig.remindDays,
  // remindLaunches: _kAppRatingConfig.remindLaunches,
  googlePlayIdentifier: _googlePlayIdentifier,
  appStoreIdentifier: _appStoreIdentifier,
);

class LaunchReviewService {
  static Future<void> launchReviewApp(BuildContext context) async {
    context.showLoading();
    await _rateMyApp.init();
    context.hideLoading();
    await _rateMyApp.showRateDialog(
      context,
      title: S.of(context).rateTheApp,
      // The dialog title.
      message: S.of(context).rateThisAppDescription,
      // The dialog message.
      rateButton: S.of(context).rate.toUpperCase(),
      // The dialog 'rate' button text.
      noButton: S.of(context).noThanks.toUpperCase(),
      // The dialog 'no' button text.
      laterButton: S.of(context).maybeLater.toUpperCase(),
      // The dialog 'later' button text.
      listener: (button) {
        // The button click listener (useful if you want to cancel the click event).
        switch (button) {
          case RateMyAppDialogButton.rate:
            break;
          case RateMyAppDialogButton.later:
            break;
          case RateMyAppDialogButton.no:
            break;
        }

        return true; // Return false if you want to cancel the click event.
      },
      ignoreNativeDialog: Platform.isAndroid,
      // Set to false if you want to show the native Apple app rating dialog on iOS.
      dialogStyle: const DialogStyle(),
      // Custom dialog styles.
      // Called when the user dismissed the dialog (either by taping outside or by pressing the 'back' button).
      // actionsBuilder: (_) => [], // This one allows you to use your own buttons.
    );
  }
}
