import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

class FacebookService {
  // https://facebook.meedu.app/#/
  static Future<String?> loginFacebook() async {
    String? token;

    final result = await FacebookAuth.instance.login();

    if (result.status == LoginStatus.success) {
      // you are logged
      final accessToken = result.accessToken!;
      token = accessToken.tokenString;
    }
    return token;
  }

  static Future<void> logout({consumerKey, consumerSecret}) async {
    try {
      await FacebookAuth.instance.logOut();
    } catch (e) {
      print(e);
    }
  }
}
