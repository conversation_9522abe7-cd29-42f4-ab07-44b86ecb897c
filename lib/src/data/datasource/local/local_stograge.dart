import 'dart:convert';

import 'package:localstorage/localstorage.dart';

import '../../../domain/entities/design_settings.dart';
import '../../../presentation/shared/navigation/navigation_model.dart';

const _kConfigKey = 'config_medita_v2';
const _kDesignSettingsKey = 'design_settings_v2';

class LocalStorageDataSource {
  final storage = LocalStorage('builder.json');

  Future<void> saveDesignConfig(ConfigModel config) async {
    await storage.setItem(_kConfigKey, jsonEncode(config.toJson()));
  }

  Future<ConfigModel?> getDesignConfig() async {
    final config = await storage.getItem(_kConfigKey);
    if (config == null) return null;

    return configModelFromJson(config);
  }

  Future<void> saveDesignSettings(DesignSettings settings) async {
    await storage.setItem(_kDesignSettingsKey, jsonEncode(settings.toJson()));
  }

  Future<DesignSettings?> getDesignSettings() async {
    final settings = await storage.getItem(_kDesignSettingsKey);
    if (settings == null) return null;

    return DesignSettings.fromJson(jsonDecode(settings));
  }
}
