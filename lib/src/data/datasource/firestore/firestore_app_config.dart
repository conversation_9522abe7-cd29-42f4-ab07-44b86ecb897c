import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../domain/entities/design_settings.dart';
import '../../../presentation/shared/navigation/navigation_model.dart';

class FirestoreAppConfigsDataSource {
  final _firestore = FirebaseFirestore.instance;

  Future<ConfigModel?> getConfigDesign(String languageCode) async {
    final designKey = 'config_${languageCode.toLowerCase()}';

    final data =
        await _firestore.collection('app_configs').doc('design_config').get();
    final datas = data.data();
    final viConfig = datas?[designKey];

    if (viConfig != null) {
      final config = ConfigModel.fromJson(jsonDecode(viConfig));
      return config;
    }

    return null;
  }

  Future<DesignSettings?> getDesignSettings() async {
    final data =
        await _firestore.collection('app_configs').doc('settings').get();
    final datas = data.data();

    if (datas != null) {
      final config = DesignSettings.fromJson(datas);
      return config;
    }

    return null;
  }
}
