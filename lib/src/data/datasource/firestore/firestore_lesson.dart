import 'package:cloud_firestore/cloud_firestore.dart';

class FirestoreLessonDataSource {
  final _firestore = FirebaseFirestore.instance;

  Future<List> getLessonById(String? id) async {
    Query messagesQuery =
        _firestore.collection('fl_content').where('id', isEqualTo: id);
    // .limit(10);
    // DocumentSnapshot docSnapshot = audioDetailsList.length > 0 ? audioDetailsList[audioDetailsList.length - 1]['docSnapshot'] ?? null : null;
    // if (docSnapshot != null)
    //   messagesQuery = messagesQuery.endBeforeDocument(docSnapshot);
    List audioDetailsList = [];
    var data = await messagesQuery.get();
    for (var items in data.docs[0]['audios']) {
      var tempObj = {};

      tempObj['audioFile'] = items['audioFile'];
      // tempObj['audioFile'] = await items['audioFile'][0].get().then((documentSnapshot) {
      //   return documentSnapshot.data()['file'];
      // });
      tempObj['audioCoverImage'] = items['audioCoverImage'];
      // tempObj['audioCoverImageUrl'] = (await items['audioCoverImage'][0].get()).data()['file'];

      tempObj['audioDescription'] = items['audioDescription'];
      tempObj['audioName'] = items['audioName'];
      tempObj['isPaid'] = items['isPaid'];
      // tempObj['docSnapshot'] = items;

      audioDetailsList.add(tempObj);
    }
    return audioDetailsList;
  }
}
