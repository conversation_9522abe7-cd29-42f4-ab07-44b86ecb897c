import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:just_audio/just_audio.dart';
import 'package:meditaguru/src/core/lib_common.dart';

import '../../../domain/entities/category_data.dart';
import '../../models/lesson_rawdata.dart';

class FirestoreCategoryDataSource {
  final _firestore = FirebaseFirestore.instance;

  Future<List> getCategories() async {
    final categoryList = [];

    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'categories')
        .get();

    final futureList = <Future>[];
    for (var doc in data.docs) {
      final fetchData = doc['coverImage'][0].get();
      futureList.add(fetchData);
    }

    final results = await Future.wait(futureList);

    final docs = data.docs;
    for (var i = 0; i < results.length; i++) {
      final doc = docs[i];
      final coverImage = await results[i].data()['file'];

      Map fetchedObj = doc.data();
      fetchedObj['coverImage'] = coverImage;
      fetchedObj['data'] = doc;
      categoryList.add(fetchedObj);
    }

    categoryList.sort((a, b) {
      int x = a['order'] ?? 0;
      int y = b['order'] ?? 0;
      if (x > y) {
        return 1;
      } else if (x == y) {
        return 0;
      }
      return -1;
    });

    return categoryList;
  }

  Future<List<CategoryData>> getCategoriesData(
      {bool loadCoverImage = true}) async {
    final categoryList = [];

    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'categories')
        .get();

    if (loadCoverImage) {
      final futureList = <Future>[];
      for (var doc in data.docs) {
        final fetchData = doc['coverImage'][0].get();
        futureList.add(fetchData);
      }

      final results = await Future.wait(futureList);

      final docs = data.docs;
      for (var i = 0; i < results.length; i++) {
        final doc = docs[i];
        final coverImage = await results[i].data()['file'];

        Map fetchedObj = doc.data();
        fetchedObj['coverImage'] = coverImage;
        fetchedObj['data'] = doc;
        categoryList.add(fetchedObj);
      }
    } else {
      final docs = data.docs;
      for (var i = 0; i < docs.length; i++) {
        final doc = docs[i];

        Map fetchedObj = doc.data();
        fetchedObj['data'] = doc;
        fetchedObj['coverImage'] = null;
        categoryList.add(fetchedObj);
      }
    }

    categoryList.sort((a, b) {
      int x = a['order'] ?? 0;
      int y = b['order'] ?? 0;
      if (x > y) {
        return 1;
      } else if (x == y) {
        return 0;
      }
      return -1;
    });

    final categs = categoryList
        .map((e) => CategoryData.fromJson(Map<String, dynamic>.from(e)))
        .toList();

    return categs;
  }

  Future<LessonRawDataModel?> getLessonFromCategory({
    String? categoryName,
    String? lessonName,
    required bool isUnLock,
  }) async {
    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'categories')
        .where('name', isEqualTo: categoryName)
        .get();

    if (data.docs.isNotEmpty) {
      for (var items in data.docs[0]['audios']) {
        var tempObj = {};

        tempObj['audioFile'] = items['audioFile'];
        // tempObj['audioFile'] = await items['audioFile'][0].get().then((documentSnapshot) {
        //   return documentSnapshot.data()['file'];
        // });
        tempObj['audioCoverImage'] = items['audioCoverImage'];
        // tempObj['audioCoverImageUrl'] = (await items['audioCoverImage'][0].get()).data()['file'];

        tempObj['audioDescription'] = items['audioDescription'];
        tempObj['audioName'] = items['audioName'];
        tempObj['audioTime'] = items['audioTime'];
        tempObj['isPaid'] = items['isPaid'];
        // tempObj['docSnapshot'] = items;

        if (lessonName == tempObj['audioName']) {
          var isLocked = true;
          print(data);
          if (!tempObj['isPaid']) {
            //for free content
            isLocked = false;
          } else {
            //fist check if user has buy something
            if (isUnLock) {
              isLocked = false;
            }
          }

          if (isLocked) {
            return LessonRawDataModel();
          } else {
            final documentSnapshotaudioCoverImage =
                await tempObj['audioCoverImage'][0].get();
            var audioCoverImageId =
                documentSnapshotaudioCoverImage.data()['file'];

            final documentSnapshot = await tempObj['audioFile'][0].get();
            String fileName = documentSnapshot.data()['file'];

            return LessonRawDataModel(
              isLocked: false,
              data: LessonDataModel(
                tempObj: tempObj,
                imageId: audioCoverImageId,
                fileName: fileName,
              ),
            );
          }
        }
      }
    }
    return null;
  }

  Future<CategoryData?> getCategoryByName(String categoryName) async {
    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'categories')
        .where('name', isEqualTo: categoryName)
        .get();

    if (data.docs.isNotEmpty) {
      Map fetchedObj = data.docs[0].data();
      String coverImage =
          await data.docs[0]['coverImage'][0].get().then((documentSnapshot) {
        return documentSnapshot.data()['file'];
      });
      fetchedObj['coverImage'] = coverImage;
      fetchedObj['data'] = data.docs[0].data();

      // await context.startLessons(
      //   id: fetchedObj['data']['id'],
      //   coverImage: fetchedObj['coverImage'],
      //   desc: (data.docs[0].data())['categoryDescription'],
      //   name: fetchedObj['data']['name'],
      //   useRoot: true,
      // );

      return CategoryData(
        id: fetchedObj['data']['id'],
        description: (data.docs[0].data())['categoryDescription'],
        coverImage: fetchedObj['coverImage'],
        name: fetchedObj['data']['name'],
      );
    }

    return null;
  }

  Future<CategoryData?> getCategoryById(String id) async {
    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'categories')
        .where('id', isEqualTo: id)
        .get();

    if (data.docs.isNotEmpty) {
      Map fetchedObj = data.docs[0].data();
      String coverImage =
          await data.docs[0]['coverImage'][0].get().then((documentSnapshot) {
        return documentSnapshot.data()['file'];
      });
      fetchedObj['coverImage'] = coverImage;
      fetchedObj['data'] = data.docs[0].data();

      // await context.startLessons(
      //   id: fetchedObj['data']['id'],
      //   coverImage: fetchedObj['coverImage'],
      //   desc: (data.docs[0].data())['categoryDescription'],
      //   name: fetchedObj['data']['name'],
      //   useRoot: true,
      // );

      return CategoryData(
        id: fetchedObj['data']['id'],
        description: (data.docs[0].data())['categoryDescription'],
        coverImage: fetchedObj['coverImage'],
        name: fetchedObj['data']['name'],
      );
    }

    return null;
  }

  Future<void> handleAudio() async {
    final categoryList = [];

    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'categories')
        .get();

    final docs = data.docs;
    for (var i = 0; i < docs.length; i++) {
      final doc = docs[i];
      Map fetchedObj = doc.data();
      categoryList.add(fetchedObj);

      print('handleAudio: fetchedObj: ${fetchedObj['name']}');

      // Update audioTime for the specific audio item
      final audios = fetchedObj['audios'];
      if (audios != null && audios is List && audios.isNotEmpty) {
        for (var audio in audios) {
          if (audio['audioTime'] != null) {
            print('Đã xử lý: ${audio['audioName']} - ${audio['audioTime']}');
            continue;
          }

          print('Xử lý: ${audio['audioName']}');
          final audioFile = audio['audioFile'];
          if (audioFile != null && audioFile is List && audioFile.isNotEmpty) {
            final audioFile = await audio['audioFile'][0].get();
            final fileName = audioFile.data()['file'];
            final link = fileName?.toString().mediaLinkFirebase;
            if (link != null) {
              // get duration from link
              final duration = await getDurationFromLink(link);
              if (duration != null) {
                audio['audioTime'] = duration;
                print(
                    'Đã xử lý hoàn tất: ${audio['audioName']} - ${audio['audioTime']}');
              }
            }
          }
        }

        print('handleAudio: fetchedObj: ${fetchedObj['name']}');
        // // Update the Firestore document with the new audioTime
        await _firestore
            .collection('fl_content')
            .doc(doc.id) // Use the document ID to update the correct document
            .update({
          'audios': audios, // Update the audios field
        });
      }
    }

    print(categoryList);
  }

  Future<int?> getDurationFromLink(String link) async {
    try {
      final audioPlayer = AudioPlayer();
      await audioPlayer.setAudioSource(AudioSource.uri(Uri.parse(link)));
      final duration = audioPlayer.duration;
      return duration?.inSeconds;
    } catch (e) {
      print('[Error] getDurationFromLink: $link ');
      print('[Error] getDurationFromLink: $e ');
      return null;
    }
  }
}
