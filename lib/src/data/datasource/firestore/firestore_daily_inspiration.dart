import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:meditaguru/src/core/extensions/random_extension.dart';

import '../../../domain/entities/daily_inspiration_data.dart';

class FirestoreDailyInspirationDataSource {
  final _firestore = FirebaseFirestore.instance;
  Future<List<DailyInspirationData>> getListDailyInspiration() async {
    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'dailyInspiration')
        .get();

    final listDailyInspiration = <DailyInspirationData>[];

    final random = Random();
    var randomValue =
        random.nextRange(data.docs.length, maxRange: 8, minRange: 6);

    for (var i = randomValue.$1; i < randomValue.$2; i++) {
      final doc = data.docs[i];

      final dataDaily = await _getData(doc);
      if (dataDaily != null) {
        listDailyInspiration.add(dataDaily);
      }
    }
    int indexCheck = 0;
    while (listDailyInspiration.length < 6 && indexCheck < data.docs.length) {
      if (indexCheck < randomValue.$1 || indexCheck > randomValue.$2) {
        final doc = data.docs[indexCheck];

        final dataDaily = await _getData(doc);
        if (dataDaily != null) {
          listDailyInspiration.add(dataDaily);
        }
      }
      indexCheck++;
    }

    return listDailyInspiration;
  }

  Future<DailyInspirationData?> _getData(
      QueryDocumentSnapshot<Map<String, dynamic>> doc) async {
    final dailyInspirationData = doc.data();
    final rawDataImage = dailyInspirationData['image'];
    if (rawDataImage is List && rawDataImage.isNotEmpty) {
      final imageData = await dailyInspirationData['image'][0].get();
      final image = imageData['file']?.toString();

      if (image?.isNotEmpty ?? false) {
        return DailyInspirationData.fromJson(<String, dynamic>{
          ...dailyInspirationData,
          'image': image,
        });
      }
    }
    return null;
  }
}
