import 'package:cloud_firestore/cloud_firestore.dart';

class FirestoreFeaturedStoriesDataSource {
  final _firestore = FirebaseFirestore.instance;

  Future<List> getFeaturedStories() async {
    final data = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.schema', isEqualTo: 'featuredStories')
        .get();

    final featuredStoryList = [];

    final futureList = <Future>[];
    for (var doc in data.docs) {
      final fetchData = doc['coverImage'][0].get();
      futureList.add(fetchData);
    }
    final results = await Future.wait(futureList);
    final docs = data.docs;
    for (var i = 0; i < results.length; i++) {
      final coverImage = results[i].data()['file'];
      Map fetchedObj = docs[i].data();
      fetchedObj['coverImage'] = coverImage;
      featuredStoryList.add(fetchedObj);
    }
    featuredStoryList.sort((a, b) {
      int x = a['order'] ?? 0;
      int y = b['order'] ?? 0;
      if (x > y) {
        return 1;
      } else if (x == y) {
        return 0;
      }
      return -1;
    });

    return featuredStoryList;
  }
}
