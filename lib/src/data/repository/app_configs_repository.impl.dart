import 'package:meditaguru/src/presentation/shared/navigation/navigation_model.dart';

import '../../domain/entities/design_settings.dart';
import '../../domain/repository/app_configs_repository.dart';
import '../datasource/firestore/firestore_app_config.dart';

class AppConfigsRepositoryImpl extends AppConfigsRepository {
  final FirestoreAppConfigsDataSource _firestoreAppConfigsDataSource;

  AppConfigsRepositoryImpl(this._firestoreAppConfigsDataSource);

  @override
  Future<DesignSettings?> getDesignSettings() {
    return _firestoreAppConfigsDataSource.getDesignSettings();
  }

  @override
  Future<ConfigModel?> getConfigDesign(String languageCode) {
    return _firestoreAppConfigsDataSource.getConfigDesign(languageCode);
  }
}
