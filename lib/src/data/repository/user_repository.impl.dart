import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';
import 'package:meditaguru/src/data/datasource/firestore/firestore_user.dart';
import 'package:meditaguru/src/di/injection/injection.dart';

import '../../domain/repository/user_repository.dart';

class UserRepoImpl implements UserRepo {
  late SharedPreferencesManager _shared;
  final FirestoreUserDataSource _firestoreUserDataSource;

  UserRepoImpl(this._firestoreUserDataSource) {
    _shared = injector<SharedPreferencesManager>();
  }

  String getAccessToken() {
    return _shared.getString(keyAccessToken) ?? '';
  }

  @override
  Future getUserSystem() {
    return _firestoreUserDataSource.getUserSystem();
  }

  @override
  Future<QuerySnapshot<Map<String, dynamic>>> getUser(String uid) {
    return _firestoreUserDataSource.getUser(uid);
  }

  @override
  Future<DocumentSnapshot<Map<String, dynamic>>> getUserByDocId(String uid) {
    return _firestoreUserDataSource.getUserByDocId(uid);
  }

  @override
  Future updateUserInfo({
    required String uid,
    required String phone,
    required String email,
    String? dob,
    String? photoUrl,
  }) {
    return _firestoreUserDataSource.updateUserInfo(
      dob: dob,
      phone: phone,
      email: email,
      uid: uid,
      photoUrl: photoUrl,
    );
  }

  // @override
  // Future getAllUsers() {
  //   return _firestoreUserDataSource.getAllUser();
  // }
}
