import '../../domain/entities/daily_inspiration_data.dart';
import '../../domain/repository/daily_inspiration_repository.dart';
import '../datasource/firestore/firestore_daily_inspiration.dart';

class DailyInspirationRepositoryImpl extends DailyInspirationRepository {
  final FirestoreDailyInspirationDataSource _firestoreDailyInspiration;

  DailyInspirationRepositoryImpl(this._firestoreDailyInspiration);

  @override
  Future<List<DailyInspirationData>> getListDailyInspiration() {
    return _firestoreDailyInspiration.getListDailyInspiration();
  }
}
