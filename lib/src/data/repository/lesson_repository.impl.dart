import 'package:meditaguru/src/data/datasource/firestore/firestore_lesson.dart';

import '../../domain/repository/lesson_repository.dart';

class LessonRepositoryImpl extends LessonRepository {
  final FirestoreLessonDataSource _firestoreLessonDataSource;

  LessonRepositoryImpl(this._firestoreLessonDataSource);

  @override
  Future<List> getLessonById(String? id) {
    return _firestoreLessonDataSource.getLessonById(id);
  }
}
