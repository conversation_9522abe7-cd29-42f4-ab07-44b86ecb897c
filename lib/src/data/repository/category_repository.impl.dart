import 'package:meditaguru/src/data/models/lesson_rawdata.dart';
import 'package:meditaguru/src/domain/entities/category_data.dart';

import '../../domain/repository/category_repository.dart';
import '../datasource/firestore/firestore_category.dart';

class CategoryRepositoryImpl extends CategoryRepository {
  final FirestoreCategoryDataSource _firestoreCategoryDataSource;

  CategoryRepositoryImpl(this._firestoreCategoryDataSource);

  @override
  Future<List> getCategories() {
    return _firestoreCategoryDataSource.getCategories();
  }

  @override
  Future<List<CategoryData>> getCategoriesData() {
    return _firestoreCategoryDataSource.getCategoriesData();
  }

  @override
  Future<LessonRawDataModel?> getLessonFromCategory(
      {String? categoryName, String? lessonName, required bool isUnLock}) {
    return _firestoreCategoryDataSource.getLessonFromCategory(
      isUnLock: isUnLock,
      categoryName: categoryName,
      lessonName: lessonName,
    );
  }

  @override
  Future<CategoryData?> getCategoryByName(String categoryName) {
    return _firestoreCategoryDataSource.getCategoryByName(categoryName);
  }

  @override
  Future<void> handleAudio() async {
    await _firestoreCategoryDataSource.handleAudio();
  }
}
