import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/constants.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/widgets/take_a_breath_widget.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_model.dart';
import 'package:provider/provider.dart';

import 'core/utils.dart';
import 'presentation/shared/categories/categories_model.dart';

class AppInit extends StatefulWidget {
  const AppInit({Key? key}) : super(key: key);

  @override
  _AppInitState createState() => _AppInitState();
}

class _AppInitState extends BaseStateScreen<AppInit> with AfterLayoutMixin {
  final StreamController<bool> _streamInit = StreamController<bool>();
  late AppBloc appBloc;
  late ConfigModel appConfig;
  bool isFirstSeen = false;
  bool isLoggedIn = false;

  Future<void> loadInitData() async {
    try {
      Log.printSimpleLog('[AppState] Inital Data');
      isLoggedIn = injector<AccountBloc>().isLogedIn();
      await injector<AccountBloc>().getGlobalConfig();
      await context.read<CategoriesModel>().getCategories();
      isFirstSeen = appBloc.appModel.appConfig.onBoardOnlyShowFirstTime ?? false
          ? isLoggedIn
          : false;
      printLog('[AppState] Init Data Finish');
    } catch (e, trace) {
      printLog(e.toString());
      await FirebaseAnalyticsWapper().analytics.logEvent(
        name: 'Bugs',
        parameters: {
          'name': trace.toString(),
        },
      );
    }
  }

  @override
  void initState() {
    super.initState();
    Log.printSimpleLog('[AppState] Inital State');
    appBloc = context.read<AppBloc>();
    appConfig = appBloc.appModel.appConfig;
    final isRequiredLogin = appConfig.isRequiredLogin ?? false;

    if (mounted) {
      loadInitData();
    }

    _streamInit.stream.listen((event) {
      Log.printSimpleLog('[AppState] Inital Stream');
      if ((isRequiredLogin && !isLoggedIn)) {
        context.startWelcome();
      } else {
        context.startDashboardAndRemoveUntil(useRoot: true);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _streamInit.close();
  }

  @override
  Widget build(BuildContext context) {
    return TakeADeepBreathWidget(
      loadAnimationDone: () {
        if (!_streamInit.isClosed) {
          _streamInit.add(isFirstSeen);
        }
      },
    );
  }

  @override
  void afterFirstLayout(BuildContext context) {
    ScreenUtil.init(context, allowFontScaling: true);
  }
}
