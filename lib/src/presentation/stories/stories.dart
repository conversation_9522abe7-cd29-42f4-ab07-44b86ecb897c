import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/story_view/controller/story_controller.dart';
import 'package:meditaguru/src/core/widgets/story_view/widgets/story_view.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/utils.dart';
import '../shared/account/account_bloc.dart';

class Stories extends StatefulWidget {
  static const String routeName = '/stories-screen';

  final dynamic fsl;
  const Stories(this.fsl);

  @override
  _StoriesState createState() => _StoriesState();
}

class _StoriesState extends State<Stories> {
  List<dynamic> storyItemDetails = [];
  void _getStoryItems() async {
    for (var items in widget.fsl) {
      var tempObj = {};
      if (items['storyImage'].length > 0) {
        tempObj['storyImage'] =
            await items['storyImage'][0].get().then((documentSnapshot) {
          return documentSnapshot.data()['file'];
        });
      }
      if (items.containsKey('caption') && items['caption'] != '') {
        tempObj['caption'] = items['caption'];
      }
      if (items.containsKey('captionHtml') && items['captionHtml'] != '') {
        tempObj['captionHtml'] = items['captionHtml'];
      }
      storyItemDetails.add(tempObj);
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    _getStoryItems();
  }

  final storyController = StoryController();

  /// dispose stories Controller
  @override
  void dispose() {
    storyController.dispose();
    super.dispose();
  }

  ///Displaying Static story
  @override
  Widget build(BuildContext context) {
    var storiesDuration = injector<AccountBloc>().globalConfig?.storiesDuration;
    List<StoryItem?>? items = [];
    var n = 0;
    while (storyItemDetails.length > n) {
      // When there is image or gif.
      String caption =
          StringUtils.isNotEmpty(storyItemDetails[n]['captionHtml'])
              ? storyItemDetails[n]['captionHtml']
              : storyItemDetails[n]['caption'];
      if (storyItemDetails[n].containsKey('storyImage')) {
        items.add(StoryItem.pageImage(
          url: storyItemDetails[n]['storyImage'].toString().mediaLinkFirebase,
          imageFit: BoxFit.cover,
          // shown: true,
          controller: storyController,
          caption: caption,
          duration: Duration(seconds: storiesDuration ?? 6),
        ));
      }
      // When there is only caption and no image.
      if (!storyItemDetails[n].containsKey('storyImage') &&
          storyItemDetails[n].containsKey('caption')) {
        items.add(StoryItem.text(
          title: caption,
          backgroundColor: AppColors.primaryColor,
          duration: Duration(seconds: storiesDuration ?? 6),
        ));
      }
      n++;
    }
    return Scaffold(
      body: storyItemDetails.isNotEmpty
          ? StoryView(
              storyItems: items,
              onStoryShow: (s) {},
              onComplete: () {
                context.pop();
              },
              progressPosition: ProgressPosition.top,
              repeat: false,
              controller: storyController,
            )
          : Container(
              color: Colors.black87,
              child: Shimmer.fromColors(
                baseColor: Colors.white,
                highlightColor: AppColors.darkPrimaryColor,
                child: Center(
                  child: Image.asset(
                    'assets/img/round_logo.png',
                    height: 100.0,
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ),
    );
  }
}
