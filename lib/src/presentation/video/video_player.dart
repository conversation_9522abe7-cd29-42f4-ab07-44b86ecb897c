import 'dart:async';

import 'package:chewie/chewie.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/back_floating_action.dart';
import 'package:meditaguru/src/core/wrappers/toast_message/toast_message.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/video/video_full_screen.dart';
import 'package:video_player/video_player.dart';

class BumbleBeeRemoteVideo extends StatefulWidget {
  static const String routeName = '/video-screen';

  final String url;
  final String videoName;
  final String? description;

  BumbleBeeRemoteVideo(
      {required this.url, required this.videoName, this.description});

  @override
  _BumbleBeeRemoteVideoState createState() => _BumbleBeeRemoteVideoState();
}

class _BumbleBeeRemoteVideoState extends BaseState<BumbleBeeRemoteVideo> {
  late VideoPlayerController _controller;
  ChewieController? chewieController;

  // Future<ClosedCaptionFile> _loadCaptions() async {
  //   final fileContents = await DefaultAssetBundle.of(context)
  //       .loadString('assets/bumble_bee_captions.srt');
  //   return SubRipCaptionFile(fileContents);
  // }
  late StreamSubscription<List<ConnectivityResult>> connectivity;

  String _url = '';
  Duration _currentPosition = Duration.zero;

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    _url = widget.url;
    // _url = 'https://flutter.github.io/assets-for-api-docs/assets/videos/bee.mp4';
    // _url = 'https://www.sample-videos.com/video123/mp4/720/big_buck_bunny_720p_20mb.mp4';
    initializePlayer().then((_) {
      connectivity =
          Connectivity().onConnectivityChanged.listen(checkConnectionResult);
    });
  }

  @override
  void dispose() {
    connectivity.cancel();
    _controller.dispose();
    chewieController?.dispose();
    super.dispose();
  }

  Future<void> initializePlayer() async {
    if (_url.startsWith('http')) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(_url),
        // closedCaptionFile: _loadCaptions(),
        // videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      );
    } else {
      _controller = VideoPlayerController.asset(
        _url,
        // closedCaptionFile: _loadCaptions(),
        // videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      );
    }

    await _controller.initialize();
    _createChewieController();
    if (mounted) {
      setState(() {});
    }
    _controller.addListener(updateCurrentPosition);
  }

  void updateCurrentPosition() {
    setState(() {
      if (_controller.value.isInitialized) {
        _currentPosition = _controller.value.position;
      }
    });
  }

  void _createChewieController() {
    chewieController = ChewieController(
      videoPlayerController: _controller,
      autoPlay: true,
      looping: false,
      deviceOrientationsAfterFullScreen: [DeviceOrientation.portraitUp],
      cupertinoProgressColors: ChewieProgressColors(
          playedColor: AppColors.secondary2Color,
          handleColor: AppColors.secondary2Color,
          backgroundColor: AppColors.whiteColor),
      materialProgressColors: ChewieProgressColors(
        playedColor: AppColors.secondary2Color,
        handleColor: AppColors.secondary2Color,
        backgroundColor: AppColors.whiteColor,
      ),
      bufferingBuilder: (context) {
        return Center(
          child: kLoadingCircleWidget(context),
        );
      },
    );
  }

  checkConnectionResult(List<ConnectivityResult> result) {
    if (result.contains(ConnectivityResult.none)) {
      showToastMessage(
        S.of(context).oopsInternetLost,
        ToastMessageType.error,
      );
    } else {
      // Try to continue playing the video after connection is restored
      if (!_controller.value.isInitialized) {
        initializePlayer().then((_) {
          _controller
            ..pause()
            ..seekTo(_currentPosition)
            ..play();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var appWidget = SingleChildScrollView(
      child: Column(
        children: <Widget>[
          const SizedBox(
            height: 50,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            width: MediaQuery.sizeOf(context).width,
            alignment: Alignment.center,
            child: Text(
              widget.videoName,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 23,
                  color: Colors.white,
                  fontFamily: 'MyFont',
                  fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            width: MediaQuery.sizeOf(context).width,
            alignment: Alignment.center,
            child: Text(
              widget.description ?? '',
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontFamily: 'MyFont',
                  fontWeight: FontWeight.normal),
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Center(
            child: chewieController != null &&
                    chewieController!.videoPlayerController.value.isInitialized
                ? Container(
                    width: double.infinity,
                    height: 300,
                    child: Chewie(
                      controller: chewieController!,
                    ),
                  )
                : Container(
                    width: double.infinity,
                    height: 300,
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: AppColors.secondary2Color,
                        ),
                      ],
                    ),
                  ),
          ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );

    return MScaffold(
        safeAreaTop: false,
        floatingActionButton: BackFloatingAction(
          onPressed: () {
            context.pop();
          },
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
        body: SafeArea(child: appWidget));
  }
}

class ControlsOverlay extends StatelessWidget {
  final bool showFullScreen;
  const ControlsOverlay(
      {Key? key, required this.controller, this.showFullScreen = true})
      : super(key: key);

  // static const _examplePlaybackRates = [
  //   0.25,
  //   0.5,
  //   1.0,
  //   1.5,
  //   2.0,
  //   3.0,
  //   5.0,
  //   10.0,
  // ];

  final VideoPlayerController controller;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 50),
          reverseDuration: const Duration(milliseconds: 200),
          child: controller.value.isPlaying
              ? const SizedBox.shrink()
              : Container(
                  color: Colors.black26,
                  child: const Center(
                    child: Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 100.0,
                    ),
                  ),
                ),
        ),
        GestureDetector(
          onTap: () {
            if (controller.value.isPlaying) {
              controller.pause();
            } else {
              if (controller.value.position == controller.value.duration) {
                controller.seekTo(const Duration());
              }
              controller.play();
            }
          },
        ),
        Align(
          alignment: Alignment.bottomRight,
          child: controller.value.isPlaying
              ? const SizedBox.shrink()
              : Container(
                  color: Colors.black26,
                  child: InkWell(
                    child: IconButton(
                      icon: Icon(
                        showFullScreen
                            ? Icons.fullscreen
                            : Icons.fullscreen_exit_sharp,
                        color: Colors.white,
                      ),
                      onPressed: () async {
                        if (showFullScreen) {
                          var res = await VideoPlayerFullScreen.navigate(
                              context,
                              controller: controller);
                          if (res != null && res is Duration) {
                            await controller.seekTo(res);
                          }
                        } else {
                          return context.pop(controller.value.position);
                        }
                      },
                    ),
                  ),
                ),
        )
        // Align(
        //   alignment: Alignment.topRight,
        //   child: PopupMenuButton<double>(
        //     initialValue: controller.value.playbackSpeed,
        //     tooltip: 'Playback speed',
        //     onSelected: (speed) {
        //       controller.setPlaybackSpeed(speed);
        //     },
        //     itemBuilder: (context) {
        //       return [
        //         for (final speed in _examplePlaybackRates)
        //           PopupMenuItem(
        //             value: speed,
        //             child: Text('${speed}x'),
        //           )
        //       ];
        //     },
        //     child: Padding(
        //       padding: const EdgeInsets.symmetric(
        //         // Using less vertical padding as the text is also longer
        //         // horizontally, so it feels like it would need more spacing
        //         // horizontally (matching the aspect ratio of the video).
        //         vertical: 12,
        //         horizontal: 16,
        //       ),
        //       child: Text('${controller.value.playbackSpeed}x'),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
