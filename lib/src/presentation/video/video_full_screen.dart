import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/video/video_player.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerFullScreen extends StatefulWidget {
  const VideoPlayerFullScreen({Key? key, required this.controller})
      : super(key: key);

  @override
  _VideoPlayerFullScreenState createState() => _VideoPlayerFullScreenState();

  final VideoPlayerController controller;

  static Future navigate(BuildContext context,
          {required VideoPlayerController controller}) =>
      context.startScreen(VideoPlayerFullScreen(
        controller: controller,
      ));
}

class _VideoPlayerFullScreenState extends State<VideoPlayerFullScreen> {
  late VideoPlayerController _controller;
  bool isFirstInit = true;

  @override
  void initState() {
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    super.initState();
    // _controller = widget.controller;
    _controller = VideoPlayerController.networkUrl(
      Uri.parse(widget.controller.dataSource),
      // closedCaptionFile: _loadCaptions(),
      videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
    );
    _controller
      ..addListener(() {
        setState(() {
          if (_controller.value.isInitialized && isFirstInit) {
            isFirstInit = false;
            seek();
          }
        });
      })
      ..setLooping(false)
      ..initialize();
  }

  Future<void> seek() async {
    await _controller.seekTo(widget.controller.value.position);
    await _controller.play();
  }

  @override
  void dispose() {
    _controller.dispose();
    Style.styleDefault();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MediaQuery.sizeOf(context).width;
    MediaQuery.sizeOf(context).height;
    return Scaffold(
      backgroundColor: Colors.black,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: MediaQuery.sizeOf(context).width,
            ),
            Container(
              width: MediaQuery.sizeOf(context).height *
                  (_controller.value.aspectRatio),
              height: MediaQuery.sizeOf(context).height,
              child: AspectRatio(
                aspectRatio: (_controller.value.aspectRatio),
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: <Widget>[
                    VideoPlayer(_controller),
                    // ClosedCaption(text: _controller.value.caption.text),
                    ControlsOverlay(
                      controller: _controller,
                      showFullScreen: false,
                    ),
                    VideoProgressIndicator(_controller, allowScrubbing: true),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
