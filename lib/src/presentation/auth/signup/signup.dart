import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/dialog/date_time_dialog.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/medita_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/auth/login/login.dart';
import 'package:meditaguru/src/presentation/auth/login/login_bloc.dart';
import 'package:provider/provider.dart';

import '../../../core/services/url_launcher_service.dart';
import '../../../core/utils.dart';

class SignupScreen extends StatefulWidget {
  static const String routeName = '/register-screen';

  @override
  _SignupState createState() => _SignupState();

  static Provider<LoginBloc> newInstance() => Provider<LoginBloc>(
        create: (_) => LoginBloc(),
        dispose: (_, _bloc) => _bloc.dispose(),
        child: SignupScreen(),
      );
}

class LoginData {
  String displayName = '';
  String email = '';
  String password = '';
  String phoneNumber = '';
  DateTime? dob;
}

class _SignupState extends State<SignupScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();
  final LoginData _data = LoginData();
  late LoginBloc _signUpBloc;
  late TextEditingController dateTimeTextController;
  bool isHideFeatureIOS = false;

  @override
  void initState() {
    super.initState();
    dateTimeTextController = TextEditingController(text: '');
    _signUpBloc = Provider.of<LoginBloc>(context, listen: false);
    _signUpBloc.handleUIBloc.uiHandleController.listen((value) {
      if (value.isError) {
        ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
            .showSnackBar(SnackBar(
          content: Text(value.message ?? ''),
          duration: const Duration(seconds: 4),
        ));
      }
    });
    injector.get<MeditaUsecase>().getGlobalConfig().then((value) {
      setState(() {
        isHideFeatureIOS = (value.hideFeaturesIos ?? false);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return MScaffold(
      scaffoldKey: _scaffoldKey,
      safeAreaTop: false,
      body: SafeArea(
        child: Container(
          width: MediaQuery.sizeOf(context).width,
          height: MediaQuery.sizeOf(context).height,
          child: SingleChildScrollView(
            child: Column(
              children: <Widget>[
                popBack(),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      signupFormWidget(),
                      const SizedBox(
                        height: 20,
                      ),
                      LoginWithSocialAcc(
                        loginBloc: _signUpBloc,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Row popBack() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColors.whiteColor,
          ),
          onPressed: () {
            context.pop(false);
          },
        )
      ],
    );
  }

  Widget signupFormWidget() {
    return Container(
      child: Container(
        child: Container(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                Row(
                  children: [
                    Container(
                      child: CText.title5(
                        S.of(context).register,
                        fontSize: AppFontSize.titleSubFontSize2.sp,
                        color: AppColors.secondaryColor,
                      ),
                    )
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                MTextFieldInputRight(
                  title: '${S.of(context).fullName} *',
                  onSaved: (String? value) {
                    _data.displayName = value ?? '';
                  },
                  validator: (value) {
                    if (value!.isEmpty) {
                      return S.of(context).inputFullName;
                    }
                    return null;
                  },
                  textInputType: TextInputType.name,
                ),
                const SizedBox(
                  height: 15,
                ),
                MTextFieldInputRight(
                  title: 'Email *',
                  onSaved: (String? value) {
                    _data.email = value ?? '';
                  },
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return S.of(context).emailIsRequire;
                    }
                    if (!ValidateData.validEmail().hasMatch(value ?? '')) {
                      return S.of(context).emailInvalid;
                    }
                    return null;
                  },
                  textInputType: TextInputType.emailAddress,
                ),
                const SizedBox(
                  height: 15,
                ),
                MTextFieldInputRight(
                  obscureText: true,
                  title: '${S.of(context).password} *',
                  onSaved: (String? value) {
                    _data.password = value ?? '';
                  },
                  validator: (value) {
                    if (value!.isEmpty) {
                      return S.of(context).inputPassword;
                    }
                    return null;
                  },
                  textInputType: TextInputType.text,
                ),
                const SizedBox(
                  height: 15,
                ),
                MTextFieldInputRight(
                  title:
                      '${S.of(context).phoneNumber} ${isHideFeatureIOS ? '' : '*'}',
                  onSaved: (String? value) {
                    _data.phoneNumber = value ?? '';
                  },
                  validator: (value) {
                    if (isHideFeatureIOS) {
                      return null;
                    }
                    if (value!.isEmpty) {
                      return S.of(context).phoneNumberIsRequire;
                    }
                    if (!ValidateData.phoneNumber().hasMatch(value)) {
                      return S.of(context).phoneNumberFormatInvalid;
                    }
                    return null;
                  },
                  textInputType: TextInputType.phone,
                ),
                const SizedBox(
                  height: 15,
                ),
                InkWell(
                  onTap: () async {
                    var date = await DateTimeDialog.selectDate(
                        context, DateTime.now());
                    if (date != null) {
                      _data.dob = date;
                      dateTimeTextController.text =
                          MDateUtils().getDDMMYY(date);
                    }
                  },
                  child: MTextFieldInputRight(
                    enabled: false,
                    title: S.of(context).dob,
                    textInputType: TextInputType.datetime,
                    textEditingController: dateTimeTextController,
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
                Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      CText.title4(
                        S.of(context).agreeTermsDes,
                        textAlign: TextAlign.center,
                      ),
                      MBlock(4),
                      InkWell(
                        onTap: () {
                          UrlLauncherService.launchURL(
                            'https://docs.google.com/document/d/1dTisY879tjFoCJpx0bWsoPuO8_lDX_LJ9JAAlXYHTCg/edit?usp=sharing',
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2.0),
                          child: CText.title4(
                            S.of(context).termsCondition,
                            color: AppColors.secondaryColor,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                CButton(
                  S.of(context).registerNow,
                  onTap: () async {
                    if (_formKey.currentState!.validate()) {
                      _formKey.currentState?.save();

                      var res = await _signUpBloc.register(_data);
                      if (res != null) {
                        context.startDashboardAndRemoveUntil();
                      }
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
