import 'package:firebase_auth/firebase_auth.dart';
import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/medita_usecase.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/handle_ui_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';

class ForgetBloc extends BaseBloc {
  late LoadingBloc loadingBloc;
  late HandleUIBloc handleUIBloc;
  late MeditaUsecase meditaUsecase;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  ForgetBloc() {
    loadingBloc = injector<LoadingBloc>();
    meditaUsecase = injector<MeditaUsecase>();
    handleUIBloc = HandleUIBloc();
  }

  @override
  void init() {}

  @override
  void dispose() {}

  Future<bool> resetPass(_data) async {
    try {
      loadingBloc.loading();
      await _auth.sendPasswordResetEmail(email: _data.email);
    } catch (err) {
      handleUIBloc.handleError(err.toString().clearLogError);
      return false;
    } finally {
      loadingBloc.loaded();
    }
    return true;
  }
}
