import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/auth/forget/forget_bloc.dart';
import 'package:provider/provider.dart';

import '../../../core/utils.dart';

class ForgetPassword extends StatefulWidget {
  static const String routeName = '/forget-screen';

  @override
  _ForgetPasswordState createState() => _ForgetPasswordState();

  static Provider<ForgetBloc> newInstance() => Provider<ForgetBloc>(
        create: (_) => ForgetBloc(),
        dispose: (_, _bloc) => _bloc.dispose(),
        child: ForgetPassword(),
      );
}

class _ResetData {
  String email = '';
}

class _ForgetPasswordState extends State<ForgetPassword> {
  final _formKey = GlobalKey<FormState>();
  final _ResetData _data = _ResetData();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  late ForgetBloc _forgetBloc;

  @override
  void initState() {
    super.initState();
    _forgetBloc = Provider.of<ForgetBloc>(context, listen: false);
    _forgetBloc.handleUIBloc.uiHandleController.listen((value) {
      if (value.isError) {
        ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
            .showSnackBar(SnackBar(
          content: Text(value.message ?? ''),
          duration: const Duration(seconds: 5),
        ));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MScaffold(
      scaffoldKey: _scaffoldKey,
      safeAreaTop: false,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              popBack(),
              MBlock(20),
              Center(
                  child: Image.asset('assets/img/logoWhite.png', height: 200)),
              MBlock(20),
              CText.title5('Thiền Thức Tỉnh'),
              MBlock(40),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    signupFormWidget(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Row popBack() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColors.whiteColor,
          ),
          onPressed: () {
            context.pop(false);
          },
        )
      ],
    );
  }

  /// Registration form field
  Widget signupFormWidget() {
    return Container(
      child: Container(
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              Row(
                children: [
                  Container(
                    child: CText.title5(
                      S.of(context).forgotPass,
                      fontSize: AppFontSize.titleSubFontSize2.sp,
                      color: AppColors.secondaryColor,
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              Row(
                children: [
                  Container(
                    child: CText.body2(
                      S.of(context).inputEmail,
                    ),
                  )
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      child: CText.body2(
                        S.of(context).forgotPassSendEmailGuide,
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                height: 55.0,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  border:
                      Border(bottom: BorderSide(color: AppColors.whiteColor)),
                ),
                child: TextFormField(
                  style: CText.body('').style,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: '',
                    hintStyle: CText.body('').style,
                    icon: const Icon(
                      Icons.email,
                      color: AppColors.whiteColor,
                    ),
                    border: InputBorder.none,
                  ),
                  onSaved: (String? value) {
                    _data.email = value ?? '';
                  },
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return S.of(context).emailIsRequire;
                    }
                    if (!ValidateData.validEmail().hasMatch(value ?? '')) {
                      return S.of(context).emailInvalid;
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(height: 30),
              CButton(
                S.of(context).sendEmailTitle,
                onTap: () async {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState?.save();

                    if (await _forgetBloc.resetPass(_data)) {
                      context.pop(true);
                    }
                  }
                },
              ),
              const SizedBox(height: 15),
            ],
          ),
        ),
      ),
    );
  }
}
