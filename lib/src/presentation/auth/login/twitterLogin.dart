import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:oauth1/oauth1.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../core/lib_common.dart';

const _twitterDomain = 'https://twitter.com';

/// Twitter Login Screen.
/// See [Log in with Twitter](https://developer.twitter.com/en/docs/basics/authentication/guides/log-in-with-twitter).
class TwitterLoginScreen extends StatefulWidget {
  static const String routeName = '/twitter-login-screen';

  final twitterPlatform = Platform(
    'https://api.twitter.com/oauth/request_token', // temporary credentials request
    'https://api.twitter.com/oauth/authorize', // resource owner authorization
    'https://api.twitter.com/oauth/access_token', // token credentials request
    SignatureMethods.hmacSha1, // signature method
  );

  final ClientCredentials clientCredentials;
  final String oauthCallbackHandler;

  TwitterLoginScreen({
    required final String consumerKey,
    required final String consumerSecret,
    required this.oauthCallbackHandler,
  }) : clientCredentials = ClientCredentials(consumerKey, consumerSecret);

  @override
  _TwitterLoginScreenState createState() => _TwitterLoginScreenState();
}

class _TwitterLoginScreenState extends State<TwitterLoginScreen> {
  bool isLoading = true;
  late Authorization _oauth;
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    // Initialize Twitter OAuth
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      // ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) async {
            if (request.url.startsWith(widget.oauthCallbackHandler)) {
              final queryParameters = Uri.parse(request.url).queryParameters;
              final oauthToken = queryParameters['oauth_token'];
              final oauthVerifier = queryParameters['oauth_verifier'];
              if (null != oauthToken && null != oauthVerifier) {
                await _twitterLogInFinish(oauthToken, oauthVerifier);
              } else {
                context.pop(null);
              }
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(_twitterDomain));
    _oauth = Authorization(widget.clientCredentials, widget.twitterPlatform);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _twitterLogInFinish(
      String oauthToken, String oauthVerifier) async {
    // Step 3 - Request Access Token
    final tokenCredentialsResponse = await _oauth.requestTokenCredentials(
        Credentials(oauthToken, ''), oauthVerifier);

    final result = TwitterAuthProvider.credential(
      accessToken: tokenCredentialsResponse.credentials.token,
      secret: tokenCredentialsResponse.credentials.tokenSecret,
    );
    context.pop(result);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(title: const Text('Twitter Login')),
      body: Stack(
        children: <Widget>[
          WebViewWidget(
            controller: _controller,
            // onWebViewCreated: (WebViewController controller) async {
            //   await controller.loadRequest(await _twitterLogInStart());
            // },
          ),
          if (isLoading) kLoadingWidget(context),
        ],
      ),
    );
  }
}
