import 'dart:io';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/configurations/configurations.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/auth/login/login_bloc.dart';
import 'package:provider/provider.dart';

import '../../../core/utils.dart';
import '../../../domain/entities/global_config.dart';
import '../../shared/account/account_bloc.dart';

class LoginScreen extends StatefulWidget {
  static const String routeName = '/login-screen';

  static Provider<LoginBloc> newInstace() => Provider<LoginBloc>(
        create: (_) => LoginBloc(),
        dispose: (_, _bloc) => _bloc.dispose(),
        child: LoginScreen(),
      );

  @override
  _LoginState createState() => _LoginState();
}

class _LoginData {
  String email = '';
  String password = '';
}

class _LoginState extends State<LoginScreen> {
  late LoginBloc _loginBloc;

  @override
  void initState() {
    super.initState();
    _loginBloc = Provider.of<LoginBloc>(context, listen: false);
    _loginBloc.handleUIBloc.uiHandleController.listen((state) {
      if (state.isError) {
        ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
            .showSnackBar(SnackBar(
          content: Text(state.message ?? ''),
          duration: const Duration(seconds: 5),
        ));
      }
    });
  }

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();
  final _LoginData _data = _LoginData();

  @override
  Widget build(BuildContext context) {
    return MScaffold(
      scaffoldKey: _scaffoldKey,
      backgroundColor: Colors.white,
      onlyUseBgColor: false,
      safeAreaTop: false,
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            MBlock(60),
            Center(child: Image.asset('assets/img/logoWhite.png', height: 200)),
            MBlock(20),
            CText.title5('Thiền Thức Tỉnh'),
            MBlock(40),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: <Widget>[
                  loginFormWidget(),
                  const SizedBox(
                    height: 20,
                  ),
                  LoginWithSocialAcc(
                    loginBloc: _loginBloc,
                  ),
                  signupButtonWidget(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// login form
  Widget loginFormWidget() {
    return Container(
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              Row(
                children: [
                  Container(
                    child: CText.title5(
                      S.of(context).login,
                      fontSize: AppFontSize.titleSubFontSize2.sp,
                      color: AppColors.secondaryColor,
                    ),
                  )
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                height: 55.0,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  border:
                      Border(bottom: BorderSide(color: AppColors.whiteColor)),
                ),
                child: TextFormField(
                  initialValue: Configurations.devConfig?.username,
                  style: CText.body('').style,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: '',
                    hintStyle: CText.body('').style,
                    icon: const Icon(
                      Icons.email,
                      color: AppColors.whiteColor,
                    ),
                    border: InputBorder.none,
                  ),
                  onSaved: (String? value) {
                    _data.email = value ?? '';
                  },
                  validator: (value) {
                    if (value != null && value.isEmpty) {
                      return S.of(context).emailIsRequire;
                    }
                    if (!ValidateData.validEmail().hasMatch(value ?? '')) {
                      return S.of(context).emailInvalid;
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(height: 15),
              Container(
                height: 55.0,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  border:
                      Border(bottom: BorderSide(color: AppColors.whiteColor)),
                ),
                child: TextFormField(
                  obscureText: true,
                  initialValue: Configurations.devConfig?.password,
                  style: CText.body('').style,
                  decoration: InputDecoration(
                      hintText: '',
                      hintStyle: CText.body('').style,
                      icon: const Icon(
                        Icons.lock,
                        color: AppColors.whiteColor,
                      ),
                      border: InputBorder.none),
                  onSaved: (String? value) {
                    _data.password = value ?? '';
                  },
                  validator: (value) {
                    if (value!.isEmpty) {
                      return S.of(context).passwordRequire;
                    }
                    return null;
                  },
                ),
              ),
              MBlock(10),
              InkWell(
                onTap: () async {
                  final showSnackBar = await context.startForgetPassword();

                  if (true == showSnackBar) {
                    ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
                        .showSnackBar(const SnackBar(
                      content: Text(
                          'Follow the link sent to your email address to reset the password.'),
                      duration: Duration(seconds: 8),
                    ));
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Center(
                      child: CText.body2(
                    S.of(context).forgotPassQuestion,
                    color: AppColors.secondaryColor,
                  )),
                ),
              ),
              loginButtonWidget(),
            ],
          ),
        ),
      ),
    );
  }

  ///Login Button Navigate to Home screen
  Widget loginButtonWidget() {
    return CButton(
      S.of(context).login,
      onTap: () async {
        if (_formKey.currentState!.validate()) {
          _formKey.currentState?.save();
          if (await _loginBloc.handleSignIn(_data) != null) {
            context.startDashboardAndRemoveUntil();
          }
        }
      },
    );
  }

  Widget signupButtonWidget() {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          CText.body2(S.of(context).haveAnAccount),
          InkWell(
            onTap: () {
              context.startRegister();
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: CText.body2(
                ' ${S.of(context).registerNow}!',
                color: AppColors.secondaryColor,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class LoginWithSocialAcc extends StatelessWidget {
  final LoginBloc? loginBloc;

  const LoginWithSocialAcc({Key? key, this.loginBloc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CText.body2(
          S.of(context).orConnectWith,
          fontWeight: FontWeight.bold,
        ),
        const SizedBox(
          height: 10,
        ),
        socialLoginWidget(context),
        const SizedBox(
          height: 15,
        ),
      ],
    );
  }

  /// Social login button like Facebook, Twitter, Google
  Widget socialLoginWidget(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (Platform.isIOS) ...[
            InkWell(
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(28)),
                    border: Border.all(color: AppColors.whiteColor)),
                child: const Center(
                  child: Icon(
                    FontAwesomeIcons.apple,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
              onTap: () async {
                await loginBloc?.handleAppleLogin(context);
              },
            ),
            const SizedBox(width: 15),
          ],
          StreamBuilder<GlobalConfig?>(
            stream: injector<AccountBloc>().globalConfigController,
            builder: (context, snap) {
              if (snap.data?.hideFacebookLogin ?? false) {
                return const SizedBox.shrink();
              } else {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      child: Container(
                        width: 56,
                        height: 56,
                        decoration: BoxDecoration(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(28)),
                            border: Border.all(color: AppColors.whiteColor)),
                        child: const Center(
                          child: Icon(
                            FontAwesomeIcons.facebookF,
                            color: Colors.white,
                            size: 18,
                          ),
                        ),
                      ),
                      onTap: () async {
                        await loginBloc?.handleFacebookLogin(context);
                      },
                    ),
                    const SizedBox(width: 15),
                  ],
                );
              }
            },
          ),
          InkWell(
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(28)),
                  border: Border.all(color: AppColors.whiteColor)),
              child: const Center(
                child: Icon(
                  FontAwesomeIcons.twitter,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
            onTap: () async {
              await loginBloc?.handleTwitterLogin(context);
            },
          ),
          const SizedBox(width: 15),
          InkWell(
            child: Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(28)),
                  border: Border.all(color: AppColors.whiteColor)),
              child: const Center(
                child: Icon(
                  FontAwesomeIcons.googlePlusG,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
            onTap: () async {
              await loginBloc?.handleGoogleSignIn(context);
            },
          ),
        ],
      ),
    );
  }
}
