import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/facebook_login_service.dart';
import 'package:meditaguru/src/core/wrappers/toast_message/toast_message.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/medita_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/auth/signup/signup.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/handle_ui_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart' as i;

import '../../../core/utils.dart';
import '../../shared/account/account_bloc.dart';

class LoginBloc extends BaseBloc {
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  late MeditaUsecase _meditaUsecase;

  late LoadingBloc loadingBloc;
  HandleUIBloc handleUIBloc = HandleUIBloc();
  User? userSocial;
  bool isHideFeatureIOS = true;

  LoginBloc({this.userSocial}) {
    loadingBloc = injector<LoadingBloc>();
    _meditaUsecase = injector<MeditaUsecase>();
    isHideFeatureIOS =
        injector<AccountBloc>().globalConfig?.hideFeaturesIos ?? true;
    injector<AccountBloc>().getGlobalConfig().then((value) {
      isHideFeatureIOS =
          injector<AccountBloc>().globalConfig?.hideFeaturesIos ?? true;
    });
  }

  @override
  void init() {
    handleUIBloc.init();
    loadingBloc.init();
  }

  @override
  void dispose() {
    handleUIBloc.dispose();
  }

  Future<auth.User?> handleSignIn(_data) async {
    UserCredential firebaseAuth;
    loadingBloc.loading();
    try {
      firebaseAuth = await _auth.signInWithEmailAndPassword(
          email: _data.email, password: _data.password);
    } catch (err) {
      print(err.toString());
      handleUIBloc.handleError(err.toString().clearLogError);
      return null;
    } finally {
      loadingBloc.loaded();
    }
    await _meditaUsecase.setDataUser(firebaseAuth.user);
    return firebaseAuth.user;
  }

  Future<int> handleTwitterLogin(BuildContext context) async {
    User? user;
    var resultInt = 0;
    try {
      loadingBloc.loading();
      var result = await context.starttWitterLogin();
      // var result = await TwitterService.login(
      //     consumerKey: GlobalConfiguration().getValue("twitterConsumerKey"),
      //     consumerSecret: GlobalConfiguration().getValue("twitterConsumerSecret"));
      if (result != null && result is AuthCredential) {
        // final twitterAuthCredential = TwitterAuthProvider.credential(
        //   accessToken: result.token,
        //   secret: result.secret,
        // );
        user = (await FirebaseAuth.instance.signInWithCredential(result)).user;
        // user = (await FirebaseAuth.instance.signInWithCredential(twitterAuthCredential)).user;
        print('user $user');
      }
      if (user != null) {
        if (await _meditaUsecase.checkExistSocial(user)) {
          context.startDashboardAndRemoveUntil();
          resultInt = 1;
        } else {
          if (isHideFeatureIOS) {
            await _meditaUsecase.setDataUser(user);
            context.startDashboardAndRemoveUntil();
          } else {
            await context.startRegisterSocial(user: user);
          }
          resultInt = 2;
        }
      } else {
        resultInt = 0;
      }
    } catch (e) {
      handleUIBloc.handleError(S.current.anErrorOccurredAndTryAgain);
    } finally {
      loadingBloc.loaded();
    }
    return resultInt;
  }

  Future<int> handleFacebookLogin(BuildContext context) async {
    User? user;
    var result = await FacebookService.loginFacebook();
    if (result != null) {
      loadingBloc.loading();
      try {
        final facebookAuthCred = FacebookAuthProvider.credential(result);
        user =
            (await FirebaseAuth.instance.signInWithCredential(facebookAuthCred))
                .user;

        print('user $user');
      } catch (e) {
        print('Error $e');
      } finally {
        loadingBloc.loaded();
      }
    }
    if (user != null) {
      if (await _meditaUsecase.checkExistSocial(user)) {
        context.startDashboardAndRemoveUntil();
        return 1;
      } else {
        if (isHideFeatureIOS) {
          await _meditaUsecase.setDataUser(user);
          context.startDashboardAndRemoveUntil();
        } else {
          await context.startRegisterSocial(user: user);
        }
        return 2;
      }
    } else {
      return 0;
    }
  }

  ///apple login
  Future<int> handleAppleLogin(BuildContext context) async {
    User? user;
    String displayName = '';
    if (await i.SignInWithApple.isAvailable()) {
      try {
        loadingBloc.loading();

        final appleIdCredential = await i.SignInWithApple.getAppleIDCredential(
          scopes: [
            i.AppleIDAuthorizationScopes.email,
            i.AppleIDAuthorizationScopes.fullName,
          ],
        );
        displayName = [
          appleIdCredential.familyName,
          appleIdCredential.givenName,
        ].whereType<String>().join(' ');

        var result = await i.SignInWithApple.getCredentialState(
            appleIdCredential.userIdentifier ?? '');

        switch (result) {
          case i.CredentialState.authorized:
            try {
              var oAuthProvider = OAuthProvider('apple.com');
              final credential = oAuthProvider.credential(
                idToken: String.fromCharCodes(
                    appleIdCredential.identityToken!.codeUnits),
                accessToken: String.fromCharCodes(
                    appleIdCredential.authorizationCode.codeUnits),
              );
              user = (await _auth.signInWithCredential(credential)).user;
            } catch (e) {
              print('error');
            }
            break;
          case i.CredentialState.notFound:
            // do something
            handleUIBloc.handleError(S.current.anErrorOccurredAndTryAgain);
            break;

          case i.CredentialState.revoked:
            break;
        }
      } on i.SignInWithAppleAuthorizationException catch (e) {
        if (e.code != i.AuthorizationErrorCode.canceled &&
            e.code != i.AuthorizationErrorCode.unknown) {
          handleUIBloc.handleError(e.toString().clearLogError);
        }
      } catch (error) {
        handleUIBloc.handleError(error.toString().clearLogError);
      } finally {
        loadingBloc.loaded();
      }
    } else {
      handleUIBloc.handleError(S.current.appleConnectionNotAvailable);
    }
    if (user != null) {
      if (await _meditaUsecase.checkExistSocial(user)) {
        context.startDashboardAndRemoveUntil();
        return 1;
      } else {
        if (isHideFeatureIOS) {
          await _meditaUsecase.setDataUser(user);
          context.startDashboardAndRemoveUntil();
        } else {
          // await user.updateDisplayName(displayName);
          await context.startRegisterSocial(user: user, displayName: displayName);
        }
        return 2;
      }
    } else {
      return 0;
    }
  }

  Future<int> handleGoogleSignIn(BuildContext context) async {
    var result = 0;
    try {
      loadingBloc.loading();
      final googleUser = await _googleSignIn.signIn();
      final googleAuth = await googleUser!.authentication;

      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final user = (await _auth.signInWithCredential(credential)).user;
      if (user != null) {
        if (await _meditaUsecase.checkExistSocial(user)) {
          context.startDashboardAndRemoveUntil();
          result = 1;
        } else {
          if (isHideFeatureIOS) {
            await _meditaUsecase.setDataUser(user);
            context.startDashboardAndRemoveUntil();
          } else {
            loadingBloc.loaded();
            await context.startRegisterSocial(user: user);
          }
          result = 2;
        }
      } else {
        handleUIBloc.handleError(S.current.anErrorOccurredAndTryAgain);
        result = 0;
      }
    } finally {
      loadingBloc.loaded();
    }
    return result;
  }

  Future<User?> register(LoginData _data) async {
    try {
      loadingBloc.loading();
      var isExist =
          await _meditaUsecase.checkExistPhoneNumber(_data.phoneNumber);
      if (!isExist || StringUtils.isEmpty(_data.phoneNumber)) {
        final user = (await _auth.createUserWithEmailAndPassword(
          email: _data.email,
          password: _data.password,
        ))
            .user;
        if (user != null) {
          await _meditaUsecase.setDataUserSignUpWithAccountPass(
            user,
            _data.displayName,
            _data.phoneNumber,
            _data.dob,
          );
        }
        return user;
      } else {
        handleUIBloc.handleError(S.current.phoneExists);
        return null;
      }
    } catch (err) {
      handleUIBloc.handleError(err.toString().clearLogError);
      return null;
    } finally {
      loadingBloc.loaded();
    }
  }

  Future<User?> registerSocial(LoginData _data) async {
    try {
      loadingBloc.loading();
      final phoneNumber = _data.phoneNumber;
      if (phoneNumber.isNotEmpty) {
        final isExist =
            await _meditaUsecase.checkExistPhoneNumber(_data.phoneNumber);
        if (isExist) {
          handleUIBloc.handleError(S.current.phoneExists);
          return null;
        }
      }

      await _meditaUsecase.setDataUser(userSocial,
          email: _data.email,
          name: _data.displayName,
          phone: _data.phoneNumber,
          dob: _data.dob);
      return userSocial;
    } catch (err) {
      handleUIBloc.handleError(err.toString().clearLogError);
      debugPrint(err.toString());
      return null;
    } finally {
      loadingBloc.loaded();
    }
  }
}
