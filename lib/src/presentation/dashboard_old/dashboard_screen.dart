import 'dart:async';

import 'package:app_developer/app_developer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/bottom_bar_item.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/dashboard_old/dashboard_bloc.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_widgets.dart';
import 'package:provider/provider.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

import '../../core/widgets/common/will_pop_scope.dart';
import '../../domain/usecase/medita_usecase.dart';

class OldDashboardScreen extends StatefulWidget {
  static const String routeName = '/old-dashboard';
  static MultiProvider newInstance() => MultiProvider(
        providers: [
          Provider<DashboardBloc>(
            create: (_) => DashboardBloc(),
            dispose: (_, _bloc) => _bloc.dispose(),
          ),
        ],
        child: OldDashboardScreen(),
      );

  @override
  _OldDashboardScreenState createState() => _OldDashboardScreenState();
}

class _OldDashboardScreenState extends BaseStateScreen<OldDashboardScreen> {
  late List<Widget> widgets;
  late DashboardBloc _dashboardBloc;
  late AccountBloc accountBloc;

  @override
  void initState() {
    super.initState();
    accountBloc = Provider.of<AccountBloc>(context, listen: false);
    accountBloc.setView(handleUIBloc);
    accountBloc.handleUIPaymentBloc.uiHandleController.listen((value) async {
      var context = AppBloc.navigatorKey.currentState!.context;
      if (value.isError) {
        await Alert(
          context: context,
          type: AlertType.error,
          title: S.of(context).failed,
          desc: value.message ?? '',
          buttons: [
            DialogButton(
              onPressed: () => context.pop(),
              width: 120,
              child: Text(
                S.of(context).tryAgain,
                style: const TextStyle(color: Colors.white, fontSize: 20),
              ),
            )
          ],
        ).show();
      }

      if (value.isSucess) {
        await context.startPaymentSuccess(message: value.message);
        // popup charge tiền thành công và bay qua home
      }
    });

    _dashboardBloc = Provider.of<DashboardBloc>(context, listen: false);
    _dashboardBloc
      ..init()
      ..setView(handleUIBloc);

    var navigationBloc = Provider.of<NavigationBloc>(context, listen: false)
      ..onUserChangeTab(BottomBarItemType.home);
    widgets = navigationBloc.navigationSate.navigationItems
        .map((item) =>
            MainPage(keepAlive: true, child: item.layout.buildOld(context)))
        .toList();
    if (mounted) {
      fetchDatas();
    }
  }

  Future<void> fetchDatas() async {
    await accountBloc.getGlobalConfig();
    await accountBloc.initializePayment();
    await injector<AccountBloc>().getUser();
    await injector<AccountBloc>().getFullInfoUser();
    injector<AccountBloc>().regisListener();
    unawaited(injector<MeditaUsecase>()
        .pushLastLoginInfo(injector<AccountBloc>().auth.currentUser));
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScopeWidget(
      onWillPop: _onWillPop,
      child: MScaffold(
        safeAreaTop: false,
        body: NavigationScaffold(
          child: NavigationPageChooser(
            widgets: widgets,
          ),
        ),
        floatingActionButton:
            kDebugMode ? const DeveloperButton(hasBottomBar: true) : null,
      ),
    );
  }

  Future<bool> _onWillPop() async {
    var res = await showDialog<bool?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(S.of(context).exit),
          content: Text(S.of(context).exitConfirm),
          actions: <Widget>[
            TextButton(
              onPressed: () => context.pop(false),
              child: Text(S.of(context).no),
            ),
            TextButton(
              onPressed: () {
                context.pop(false);
                SystemNavigator.pop(animated: true);
              },
              child: Text(S.of(context).yes),
            ),
          ],
        );
      },
    );

    return res ?? false;
  }
}
