import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/core/network/api_exception.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/handle_ui_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';

class DashboardBloc extends BaseBloc {
  late LoadingBloc loadingBloc;
  late HandleUIBloc handleUIBloc;

  DashboardBloc() {
    loadingBloc = injector<LoadingBloc>();
  }

  @override
  void init() {
    loadingBloc.init();
  }

  Future<bool> refreshData() async {
    var isSuccess = false;
    loadingBloc.loading();
    try {} on CustomException catch (e) {
      handleUIBloc.handleError(e.errorMessage, customException: e);
    } finally {
      loadingBloc.loaded();
    }
    return isSuccess;
  }

  @override
  void dispose() {}

  void setView(HandleUIBloc handleBloc) {
    handleUIBloc = handleBloc;
  }
}
