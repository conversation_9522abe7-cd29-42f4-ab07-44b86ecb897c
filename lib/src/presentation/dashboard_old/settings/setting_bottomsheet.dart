import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/app_setting_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';

import '../../../core/utils.dart';

class SettingLanguageBottomSheet extends StatelessWidget {
  static void show(BuildContext context) {
    ModalManager.showBottomSheet(
        context, (context) => SettingLanguageBottomSheet());
  }

  @override
  Widget build(BuildContext context) {
    List languages = Utils.getLanguagesList();
    return MBackdropFilterFull(
      child: Container(
        height: 76.h * languages.length + 80.h,
        decoration: const BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 40, vertical: 18),
                    child: CText.body1(
                      S.of(context).changeLanguage,
                      color: Colors.black,
                    ),
                  ),
                  const MDivider(
                    color: AppColors.backgroundDisableButton,
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: languages.length,
                      itemBuilder: (context, index) {
                        var isChecked =
                            injector<AppSettingUseCase>().getLanguage() ==
                                languages[index]['code'];
                        return InkWell(
                          onTap: () {
                            context.pop();
                            injector<AppBloc>()
                                .changeLanguage(languages[index]['code']);
                          },
                          child: Column(
                            children: [
                              Container(
                                height: 76.h,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.w, vertical: 16.h),
                                color: isChecked
                                    ? AppColors.backgroundButton
                                    : AppColors.whiteColor,
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 0,
                                      child: MIconContainer(
                                        size: 44,
                                        child: Image.asset(
                                            languages[index]['icon']),
                                      ),
                                    ),
                                    MBlock(12),
                                    Expanded(
                                      flex: 1,
                                      child: CText.title1(
                                        languages[index]['text'],
                                        color: Colors.black,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 0,
                                      child: isChecked
                                          ? const Icon(Icons.check)
                                          : Container(),
                                    ),
                                  ],
                                ),
                              ),
                              MDivider(
                                indent: 20.w,
                                color: AppColors.backgroundDisableButton,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  )
                ],
              ),
            ),
            Align(
              alignment: Alignment.topLeft,
              child: MButtonImage(
                onTap: () {
                  context.pop();
                },
                child: const Icon(Icons.clear_rounded, color: Colors.black),
              ),
            )
          ],
        ),
      ),
    );
  }
}
