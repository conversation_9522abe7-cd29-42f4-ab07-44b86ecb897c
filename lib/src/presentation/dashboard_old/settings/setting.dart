import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/app_info_service.dart';
import 'package:meditaguru/src/core/services/deep_link_service.dart';
import 'package:meditaguru/src/core/services/launch_review_service.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/global_config.dart';
import 'package:meditaguru/src/domain/entities/user/user_full_info.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/dashboard_old/settings/setting_bottomsheet.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';

import '../../../core/services/share_service.dart';
import '../../../core/services/url_launcher_service.dart';
import '../../../core/utils.dart';

class Setting extends StatefulWidget {
  @override
  _SettingState createState() => _SettingState();
}

class _SettingState extends State<Setting> {
  void _onTapLogout() {
    context.startDialogConfirmLogout().then(
      (value) async {
        if (true == value) {
          await injector<AccountBloc>().logout().whenComplete(() {
            context.startLogin(isReplacement: true);
          });
        }
      },
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            MBlock(16),
            CText.title1(
              S.of(context).profile,
              fontWeight: FontWeight.bold,
            ),
            MBlock(16),
            _buildProfileInfo(),
            MBlock(16),
            _buildProfileSettings(),
            MBlock(16),
            _buildAppSettings(),
            MBlock(140),
          ],
        ),
      ),
    );
  }

  GestureDetector _buildProfileInfo() {
    return GestureDetector(
      onTap: () {
        context.startProfile();
      },
      child: BackgroundRound1(
        paddingVertical: 0,
        child: StreamBuilder<UserFullInfo?>(
          stream: injector<AccountBloc>().userFullInfoController,
          builder: (context, snapshot) {
            var user = snapshot.data;
            var expiredDate = injector<AccountBloc>().expiryDatePremium == null
                ? ''
                : DateFormat.yMMMEd()
                    .format(injector<AccountBloc>().expiryDatePremium ??
                        DateTime.now())
                    .toString();
            if (injector<AccountBloc>().isPuchasedPremiumManual) {
              expiredDate = injector<AccountBloc>().expiryDatePremiumManual ==
                      null
                  ? ''
                  : DateFormat.yMMMEd()
                      .format(injector<AccountBloc>().expiryDatePremiumManual ??
                          DateTime.now())
                      .toString();
            }
            return Column(
              children: [
                MBlock(16),
                BubleBackground(
                  imageUrl: user?.photoUrl != null
                      ? Tools.getHigherResProviderPhotoUrl(user?.photoUrl ?? '')
                      : 'assets/img/ic_launcher.png',
                ),
                if (injector<AccountBloc>().isBothPuchasedPremium) ...[
                  Container(
                    width: 59.0,
                    height: 18.0,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.0),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Color(0xFFEAF818), Color(0xFFF6FC9C)],
                      ),
                      boxShadow: [
                        const BoxShadow(
                          color: Color(0xFF535966),
                          offset: Offset(0, 0),
                          blurRadius: 2.0,
                        ),
                      ],
                    ),
                    child: Center(
                      child: CText.caption(
                        'Premium',
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                MBlock(10),
                CText.title2(
                  user?.name ?? '',
                ),
                if (injector<AccountBloc>().isBothPuchasedPremium) ...[
                  MBlock(4),
                  CText.body2(
                    // 'Thời hạn gói Premium: ${expiredDate}',
                    '${S.of(context).premiumPlanUntil} $expiredDate',
                  ),
                ],
                MBlock(32),
              ],
            );
          },
        ),
      ),
    );
  }

  BackgroundRound2 _buildProfileSettings() {
    return BackgroundRound2(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                CText.body(S.of(context).general),
                MBlock(20),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              context.startProfile();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const SizedBox(
                    width: double.infinity,
                  ),
                  CText.body(
                    S.of(context).userInfo,
                  ),
                  MBlock(4),
                  CText.body2(
                    S.of(context).editProfileGuide,
                    color: const Color(0xFFACB5C5),
                  ),
                  MBlock(15),
                ],
              ),
            ),
          ),
          const MDivider(),
          InkWell(
            onTap: () async {
              final box = context.findRenderObject() as RenderBox?;
              var link = await DeeplinkService.createDynamicLink(
                  userId: injector<AccountBloc>().auth.currentUser?.uid ?? '');

              var shareContent = S.of(context).inviteMessage(link.toString());
              await ShareService.share(
                text: shareContent,
                subject: 'Thiền Thức Tỉnh',
                sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  MBlock(15),
                  Container(
                    width: double.infinity,
                  ),
                  CText.body(
                    S.of(context).inviteGuide,
                  ),
                  MBlock(4),
                  CText.body2(
                    S.of(context).inviteYourFriendInThisApp,
                    color: const Color(0xFFACB5C5),
                  ),
                ],
              ),
            ),
          ),
          StreamBuilder<UserFullInfo?>(
            stream: injector<AccountBloc>().userFullInfoController,
            builder: (context, snapshot) {
              if (!injector<AccountBloc>().isBothPuchasedPremium) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MBlock(15),
                    const MDivider(),
                    MBlock(15),
                    Container(
                      width: double.infinity,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: CText.body(
                        S.of(context).subscription,
                      ),
                    ),
                    MBlock(4),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: CText.body2(
                        S.of(context).subcribeGuide,
                        color: const Color(0xFFACB5C5),
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: CButton(
                        S.of(context).subscribeNow.toUpperCase(),
                        onTap: () {
                          context.startPayment();
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                  ],
                );
              } else {
                return const SizedBox();
              }
            },
          )
        ],
      ),
    );
  }

  BackgroundRound3 _buildAppSettings() {
    return BackgroundRound3(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              UrlLauncherService.launchURL(Urls.awakeMap);
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  CText.body(
                    S.of(context).awakeMap,
                  ),
                  MBlock(20),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () {
              SettingLanguageBottomSheet.show(context);
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  CText.body(
                    S.of(context).language,
                  ),
                  MBlock(20),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () async {
              var appId = (await AppInfoService.getAppInfo()).packageName;
              if (isIos) {
                appId = '1547936055';
              }
              unawaited(LaunchReviewService.launchReviewApp(context));
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  CText.body(
                    S.of(context).reviewApp,
                  ),
                  MBlock(20),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: context.sendFeedbackThienThucTinh,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  CText.body(
                    S.of(context).feedback,
                  ),
                  MBlock(20),
                ],
              ),
            ),
          ),
          StreamBuilder<GlobalConfig?>(
            stream: injector<AccountBloc>().globalConfigController,
            builder: (context, snap) {
              if (snap.hasData) {
                return InkWell(
                  onTap: () {
                    UrlLauncherService.launchURL(
                      snap.data?.privacyPolicy ?? '',
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        CText.body(
                          S.of(context).privacyPolicy,
                        ),
                        MBlock(20),
                      ],
                    ),
                  ),
                );
              } else {
                return Container();
              }
            },
          ),
          StreamBuilder<GlobalConfig?>(
            stream: injector<AccountBloc>().globalConfigController,
            builder: (context, snap) {
              if (snap.hasData) {
                return InkWell(
                  onTap: () {
                    UrlLauncherService.launchURL(
                      snap.data?.termsAndConditions ?? '',
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        CText.body(
                          S.of(context).termsCondition,
                        ),
                        MBlock(20),
                      ],
                    ),
                  ),
                );
              } else {
                return Container();
              }
            },
          ),
          FutureBuilder<AppInfoModel>(
            future: AppInfoService.getAppInfo(),
            initialData: AppInfoModel(version: '1.0.0'),
            builder: (context, snap) {
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    CText.body(
                      'V ${snap.data?.version ?? '1.0.0'}',
                    ),
                    MBlock(20),
                  ],
                ),
              );
            },
          ),
          const MDivider(),
          InkWell(
            onTap: _onTapLogout,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  MBlock(20),
                  CText.body(
                    S.of(context).logout,
                    color: const Color(0xFFE77A96),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BubleBackground extends StatelessWidget {
  final String imageUrl;

  const BubleBackground({Key? key, required this.imageUrl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 40),
      child: Row(
        children: <Widget>[
          Align(
            alignment: const Alignment(0.0, -0.07),
            child: Container(
              width: 18.0,
              height: 18.0,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment(0.71, -0.5),
                  end: Alignment(-0.6, 0.78),
                  colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                ),
              ),
            ),
          ),
          const Spacer(flex: 58),
          Align(
            alignment: const Alignment(0.0, 0.54),
            child: Container(
              width: 8.0,
              height: 8.0,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment(0.71, -0.5),
                  end: Alignment(-0.6, 0.78),
                  colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                ),
              ),
            ),
          ),
          const Spacer(flex: 5),
          SizedBox(
            width: 106.0,
            height: 78.0,
            child: Stack(
              children: <Widget>[
                Positioned(
                  right: 0,
                  bottom: 2.0,
                  child: Container(
                    width: 26.0,
                    height: 26.0,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment(0.71, -0.5),
                        end: Alignment(-0.6, 0.78),
                        colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xFF1A1C1F),
                          offset: Offset(0, 4.0),
                          blurRadius: 16.0,
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: 34.0,
                  height: 34.0,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment(0.71, -0.5),
                      end: Alignment(-0.6, 0.78),
                      colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                    ),
                  ),
                ),
                Positioned(
                  right: 14.0,
                  bottom: 0,
                  child: Container(
                    width: 74.0,
                    height: 74.0,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(7.0),
                      boxShadow: [
                        const BoxShadow(
                          color: Color(0xFF23262B),
                          offset: Offset(0, 5.0),
                          blurRadius: 12.0,
                        ),
                      ],
                    ),
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(7),
                        child: Tools.image(url: imageUrl, fit: BoxFit.cover)),
                  ),
                ),
              ],
            ),
          ),
          const Spacer(flex: 30),
          Align(
            alignment: const Alignment(0.0, -0.37),
            child: Container(
              width: 14.0,
              height: 14.0,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment(0.71, -0.5),
                  end: Alignment(-0.6, 0.78),
                  colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFF1A1C1F),
                    offset: Offset(0, 4.0),
                    blurRadius: 16.0,
                  ),
                ],
              ),
            ),
          ),
          const Spacer(flex: 42),
          Align(
            alignment: const Alignment(0.0, 0.31),
            child: Container(
              width: 8.0,
              height: 8.0,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment(0.71, -0.5),
                  end: Alignment(-0.6, 0.78),
                  colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFF1A1C1F),
                    offset: Offset(0, 4.0),
                    blurRadius: 16.0,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BackgroundRound1 extends StatelessWidget {
  final Widget child;
  final double? paddingVertical;

  const BackgroundRound1({Key? key, required this.child, this.paddingVertical})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.symmetric(vertical: paddingVertical ?? 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.0),
        gradient: Style.linearGradientTTT1(),
        // LinearGradient(
        //   begin: Alignment.topRight,
        //   end: Alignment(-0.96, 1.0),
        //   colors: [const Color(0xFF414141), Colors.black],
        // ),
        border: Border.all(
          width: 1.0,
          color: const Color(0xFF3E434E),
        ),
        boxShadow: [
          const BoxShadow(
            color: Color(0xFF1A1C1F),
            offset: Offset(0, 4.0),
            blurRadius: 12.0,
          ),
        ],
      ),
      child: child,
    );
  }
}

class BackgroundRound2 extends StatelessWidget {
  final Widget child;
  final double? margin;

  const BackgroundRound2({Key? key, required this.child, this.margin})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: margin ?? 16),
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.0),
        gradient: Style.linearGradientTTT2(),
        // LinearGradient(
        //   begin: Alignment.topRight,
        //   end: Alignment(-0.96, 1.0),
        //   colors: [Colors.black, const Color(0xFF414141)],
        // ),
        border: Border.all(
          width: 1.0,
          color: const Color(0xFF3E434E),
        ),
        boxShadow: [
          const BoxShadow(
            color: Color(0xFF1A1C1F),
            offset: Offset(0, 4.0),
            blurRadius: 12.0,
          ),
        ],
      ),
      child: child,
    );
  }
}

class BackgroundRound3 extends StatelessWidget {
  final Widget child;
  final double? margin;
  final double? radius;

  const BackgroundRound3(
      {Key? key, required this.child, this.margin, this.radius})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: margin ?? 16),
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius ?? 24.0),
        gradient: Style.linearGradientTTT1(),
        // LinearGradient(
        //   begin: Alignment.topRight,
        //   end: Alignment(-0.96, 1.0),
        //   colors: [const Color(0xFF414141), Colors.black],
        // ),
        border: Border.all(
          width: 1.0,
          color: const Color(0xFF3E434E),
        ),
        boxShadow: [
          const BoxShadow(
            color: Color(0xFF1A1C1F),
            offset: Offset(0, 4.0),
            blurRadius: 12.0,
          ),
        ],
      ),
      child: child,
    );
  }
}

class BackgroundTextImage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: const Alignment(-0.4, 0.95),
          end: const Alignment(-0.12, -0.02),
          colors: [
            Colors.black.withOpacity(0.8),
            const Color(0xFF040404).withOpacity(0.75),
            const Color(0xFF414141).withOpacity(0.0)
          ],
          stops: [0.0, 0.343, 1.0],
        ),
      ),
    );
  }
}
