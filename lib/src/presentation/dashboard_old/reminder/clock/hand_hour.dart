import 'dart:math';

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class HourHandPainter extends CustomPainter {
  final Paint hourHandPaint;
  int hours;
  int minutes;

  HourHandPainter({required this.hours, required this.minutes})
      : hourHandPaint = Paint() {
    hourHandPaint
      ..color = AppColors.secondary2Color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final radius = size.width / 2;
    // To draw hour hand
    // And check if hour is greater than 12 before calculating rotation
    canvas
      ..save()
      ..translate(radius, radius)
      ..rotate(hours >= 12
          ? 2 * pi * ((hours - 12) / 12 + (minutes / 720))
          : 2 * pi * ((hours / 12) + (minutes / 720)));

    //hour hand stem
    var path = Path()
      ..moveTo(0.0, -radius * 0.5)
      ..lineTo(0.0, radius * 0);

    canvas
      ..drawPath(path, hourHandPaint)
      ..restore();
  }

  @override
  bool shouldRepaint(HourHandPainter oldDelegate) {
    return true;
  }
}
