import 'dart:math';

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class MinuteHandPainter extends CustomPainter {
  final Paint minuteHandPaint;
  int minutes;
  int seconds;

  MinuteHandPainter({required this.minutes, required this.seconds})
      : minuteHandPaint = Paint() {
    minuteHandPaint
      ..color = AppColors.whiteColor
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 2;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final radius = size.width / 2;
    canvas
      ..save()
      ..translate(radius, radius)
      ..rotate(2 * pi * ((minutes + (seconds / 60)) / 60));

    var path = Path()
      ..moveTo(0.0, -radius * 0.6)
      ..lineTo(0.0, radius * 0)
      ..close();

    canvas
      ..drawPath(path, minuteHandPaint)
      ..drawShadow(path, Colors.black, 4.0, false)
      ..restore();
  }

  @override
  bool shouldRepaint(MinuteHandPainter oldDelegate) {
    return true;
  }
}
