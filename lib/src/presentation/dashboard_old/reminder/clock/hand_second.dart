import 'dart:math';

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class SecondHandPainter extends CustomPainter {
  final Paint secondHandPaint;
  final Paint secondHandPointsPaint;

  int seconds;

  SecondHandPainter({required this.seconds})
      : secondHandPaint = Paint(),
        secondHandPointsPaint = Paint() {
    secondHandPaint
      ..color = AppColors.splashIndicatorColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;

    secondHandPointsPaint
      ..color = AppColors.secondary2Color
      ..style = PaintingStyle.fill;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final radius = size.width / 2;
    canvas
      ..save()
      ..translate(radius, radius)
      ..rotate(2 * pi * seconds / 60);

    var path1 = Path();
    var path2 = Path();
    path1
      ..moveTo(0.0, -radius * 0.93)
      ..lineTo(0.0, radius * 0);

    path2.addOval(Rect.fromCircle(radius: 5.0, center: const Offset(0.0, 0.0)));

    canvas
      ..drawPath(path1, secondHandPaint)
      ..drawPath(path2, secondHandPointsPaint)
      ..restore();
  }

  @override
  bool shouldRepaint(SecondHandPainter oldDelegate) {
    return seconds != oldDelegate.seconds;
  }
}
