import 'package:flutter/material.dart';

import 'clock_text.dart';

class ClockFace extends StatelessWidget {
  final DateTime? dateTime;
  final ClockText clockText;
  ClockFace({this.clockText = ClockText.arabic, this.dateTime});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: AspectRatio(
          aspectRatio: 1,
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              // color: Color(0xfff4f9fd),
              color: Colors.black,
              boxShadow: [
                // BoxShadow(
                //     offset: Offset(8.0, 0),
                //     blurRadius: 13,
                //     spreadRadius: 1,
                //     // color: Color(0xffd3e0f0)
                //     color: const Color(0xFF838383).withOpacity(0.5)
                // ),
                BoxShadow(
                    offset: const Offset(0, 8),
                    blurRadius: 13,
                    spreadRadius: 1,
                    // color: Color(0xffd3e0f0)
                    color: const Color(0xFF838383).withOpacity(0.5)),
                BoxShadow(
                    offset: const Offset(0, 0),
                    blurRadius: 13,
                    spreadRadius: 1,
                    // color: Color(0xffd3e0f0)
                    color: const Color(0xFF838383).withOpacity(0.5))
              ],

              gradient: const LinearGradient(
                begin: Alignment(0.38, -1.31),
                end: Alignment.bottomCenter,
                colors: [Color(0xFF414141), Colors.black],
              ),
              border: Border.all(
                width: 1.0,
                color: const Color(0xFF838383).withOpacity(0.5),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
