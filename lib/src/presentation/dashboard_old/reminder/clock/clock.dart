import 'dart:async';

import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';

import 'clock_face.dart';
import 'clock_hands.dart';
import 'clock_text.dart';

typedef TimeProducer = DateTime Function();

class Clock extends StatefulWidget {
  final Color circleColor;
  final Color shadowColor;

  final ClockText clockText;

  final TimeProducer getCurrentTime;
  final Duration updateDuration;

  Clock(
      {
      //   this.circleColor = const Color(0xfffe1ecf7),
      // this.shadowColor = const Color(0xffd9e2ed),
      this.circleColor = const Color(0xFF00838F),
      this.shadowColor = const Color(0xFF414141),
      // this.shadowColor = const Color(0x3DFFFFFF),
      this.clockText = ClockText.arabic,
      this.getCurrentTime = getSystemTime,
      this.updateDuration = const Duration(seconds: 1)});

  static DateTime getSystemTime() {
    return DateTime.now();
  }

  @override
  State<StatefulWidget> createState() {
    return _Clock();
  }
}

class _Clock extends State<Clock> {
  late Timer _timer;
  late DateTime dateTime;

  @override
  void initState() {
    super.initState();
    dateTime = DateTime.now();
    _timer = Timer.periodic(widget.updateDuration, setTime);
  }

  void setTime(Timer timer) {
    setState(() {
      dateTime = DateTime.now();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: buildClockCircle(context),
    );
  }

  Container buildClockCircle(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        // color: Colors.transparent,
        // gradient: Style.linearGradient(),
        boxShadow: [
          // BoxShadow(
          //   offset: new Offset(0.0, 5.0),
          //   blurRadius: 0.0,
          //   color: widget.shadowColor,
          // ),
          // BoxShadow(
          //     offset: Offset(0.0, 5.0),
          //     color: widget.circleColor,
          //     blurRadius: 10,
          //     spreadRadius: -8)
        ],
      ),
      child: Stack(
        children: <Widget>[
          ClockFace(
            clockText: widget.clockText,
            dateTime: dateTime,
          ),
          // Container(
          //   padding: EdgeInsets.all(25),
          //   width: double.infinity,
          //   child: new CustomPaint(
          //     painter: new ClockDialPainter(clockText: widget.clockText),
          //   ),
          // ),
          _buildClockBackground(),
          ClockHands(dateTime: dateTime),
        ],
      ),
    );
  }

  Padding _buildClockBackground() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.bottomCenter,
            child: CText.header2(
              '6',
              color: const Color(0xFF6B6B6B),
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: CText.header2(
              '12',
              color: const Color(0xFF6B6B6B),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: CText.header2(
              '3',
              color: const Color(0xFF6B6B6B),
            ),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: CText.header2(
              '9',
              color: const Color(0xFF6B6B6B),
            ),
          ),
        ],
      ),
    );
  }
}
