import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/firebase/push_noti/local_push.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/dashboard_old/reminder/reminder.dart';
import 'package:shared_preferences/shared_preferences.dart';

TimeOfDay? timeofalarm;
String showtime = '';
List<String>? multipleAlarm = [];
Color mainColor = const Color(0xFFf45905);

class Alarm extends StatefulWidget {
  @override
  _AlarmState createState() => _AlarmState();
}

class _AlarmState extends State<Alarm> {
  @override
  void initState() {
    super.initState();
    getAlarmTime();
  }

  void getAlarmTime() async {
    var myPrefs = await SharedPreferences.getInstance();

    setState(() {
      if (myPrefs.getStringList('multipleAlarm') != null) {
        multipleAlarm = myPrefs.getStringList('multipleAlarm');
      }
    });
  }

  void selectTime(BuildContext context) async {
    var selectedTimeRTL = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.dark().copyWith(
            // accentColor: AppColors.secondary3Color,
            colorScheme: const ColorScheme.dark(
              // change the border color
              primary: AppColors.secondaryColor,
              // secondary: AppColors.errorRedColor,
              // change the text color
              onSurface: Color(0xFF6B6B6B),
            ),
          ),
          child: child ?? Container(),
        );
      },
    );
    if (selectedTimeRTL != null) {
      var hour = selectedTimeRTL.hour;
      var min = selectedTimeRTL.minute;
      print(selectedTimeRTL.hashCode);
      print('============>$hour');
      print('============>$min ');
      timeofalarm = TimeOfDay(hour: hour, minute: min);
      setState(() {
        showtime = TimeOfDay(hour: hour, minute: min).format(context);
      });
      var myPrefs = await SharedPreferences.getInstance();

      if (!multipleAlarm!
          .any((alarm) => alarm.contains('${selectedTimeRTL.hashCode}'))) {
        var data = jsonEncode({
          'id': selectedTimeRTL.hashCode,
          'time': showtime,
          'enabled': true,
          'hour': hour,
          'min': min
        });

        multipleAlarm?.add(data);
        await myPrefs.setStringList('multipleAlarm', multipleAlarm ?? []);
        // myPrefs.setString('alarm_time', '$showtime');

        if (timeofalarm != null) {
          // injector<LocalPushService>().showNotificationCustomSound();
          await injector<LocalPushService>()
              .scheduledalarm(selectedTimeRTL.hashCode, timeofalarm!, showtime);
          snackbar(S.of(context).dailyReminderFor(showtime));
        }
      } else {
        snackbar(S.of(context).reminderTimeExist(showtime));
      }
    }
  }

  void snackbar(String text) {
    final snackBar = SnackBar(
      content: Text('$text '),
      duration: const Duration(seconds: 2),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: <Widget>[
            Container(
              padding: const EdgeInsets.all(16),
              width: double.infinity,
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(children: [
                  TextSpan(
                    text: S.of(context).setReminder,
                    style: CText.title1(
                      '',
                      fontWeight: FontWeight.bold,
                    ).style,
                  ),
                  const TextSpan(text: '\n'),
                  const TextSpan(text: '\n'),
                  TextSpan(
                    text: S.of(context).setReminderGuide,
                    style: CText.body('').style,
                  ),
                  const TextSpan(text: '\n'),
                  TextSpan(
                    text: S.of(context).reachYourGoal,
                    style: CText.body('').style,
                  ),
                ]),
              ),
            ),
            const SizedBox(
              height: 25,
            ),
            Reminder(),
            const SizedBox(
              height: 25,
            ),
            Container(
              width: 180,
              child: CButton(
                S.of(context).setReminder,
                onTap: () => selectTime(context),
              ),
            ),
            // SizedBox(
            //   height: MediaQuery.sizeOf(context).height * .07,
            // ),
            const SizedBox(
              height: 20,
            ),
            multipleAlarm != null
                ? ListView.builder(
                    shrinkWrap: true,
                    physics: const ScrollPhysics(),
                    itemCount: multipleAlarm?.length,
                    itemBuilder: (context, index) {
                      var alarm = jsonDecode(multipleAlarm![index]);

                      return Dismissible(
                        secondaryBackground: Container(
                            padding: const EdgeInsets.only(right: 20),
                            color: AppColors.secondaryColor,
                            alignment: Alignment.centerRight,
                            child: const Icon(
                              Icons.delete,
                              color: Colors.red,
                            )),
                        key: UniqueKey(),
                        direction: DismissDirection.endToStart,
                        onDismissed: (direction) async {
                          var myPrefs = await SharedPreferences.getInstance();

                          await injector<LocalPushService>()
                              .cancel(jsonDecode(multipleAlarm![index])['id']);
                          multipleAlarm?.removeAt(index);
                          await myPrefs.setStringList(
                              'multipleAlarm', multipleAlarm ?? []);

                          setState(() {
                            multipleAlarm =
                                myPrefs.getStringList('multipleAlarm');
                          });

                          snackbar(
                              S.of(context).reminderTimeRemoved(alarm['time']));
                        },
                        background: Container(color: AppColors.primaryColor),
                        child: _buildItemAlarm("${alarm['time']}",
                            alarm['enabled'] ?? true, index),
                      );
                    },
                  )
                : const SizedBox(),
            // Container(
            //   padding: EdgeInsets.only(right: 20),
            //   alignment: Alignment.bottomRight,
            //   child: FloatingActionButton.extended(
            //     heroTag: UniqueKey,
            //     label: Icon(
            //       Icons.add,
            //       color: Color(0xff2d386b),
            //       size: 20,
            //     ),
            //     backgroundColor: Colors.white,
            //     onPressed: () => selectTime(context),
            //   ),
            // ),
            const SizedBox(
              height: 40,
            )
          ],
        ),
      ),
    );
  }

  // Card _buildItemAlarm1(String time) {
  //   return Card(
  //     child: ListTile(
  //       title: Text(
  //         time,
  //         textAlign: TextAlign.center,
  //         style: const TextStyle(
  //             color: Color(0xff2d386b),
  //             fontSize: 30,
  //             fontWeight: FontWeight.w700),
  //       ),
  //       trailing: const Icon(
  //         Icons.chevron_left,
  //         color: AppColors.primaryColor,
  //       ),
  //     ),
  //   );
  // }

  Container _buildItemAlarm(String time, bool enabled, int index) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 22),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.0),
        gradient: const LinearGradient(
          begin: Alignment(0.0, 0.61),
          end: Alignment(0.14, -1.83),
          colors: [Colors.black, Color(0xFF414141)],
        ),
        border: Border.all(
          width: 1.0,
          color: const Color(0xFF3E434E),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: CText.header1(
              time,
              fontWeight: FontWeight.normal,
            ),
          ),
          Expanded(
            flex: 0,
            child: CupertinoSwitch(
                value: enabled,
                onChanged: (value) async {
                  var myPrefs = await SharedPreferences.getInstance();
                  //
                  var item = jsonDecode(multipleAlarm?[index] ?? '{}');
                  item['enabled'] = value;

                  if (value == false) {
                    // tắt value = false;
                    // xóa alarm
                    await injector<LocalPushService>()
                        .cancel(jsonDecode(multipleAlarm![index])['id']);
                  } else {
                    // bật value = true;
                    // add alarm
                    int hour = item['hour'] ?? 6;
                    int min = item['min'] ?? 0;
                    timeofalarm = TimeOfDay(hour: hour, minute: min);
                    showtime =
                        TimeOfDay(hour: hour, minute: min).format(context);
                    item['showtime'] = showtime;

                    if (timeofalarm != null) {
                      await injector<LocalPushService>()
                          .scheduledalarm(item['id'], timeofalarm!, showtime);
                      snackbar(S.of(context).dailyReminderFor(showtime));
                    }
                  }

                  // update lại DB
                  // setState
                  multipleAlarm?[index] = jsonEncode(item);
                  await myPrefs.setStringList(
                      'multipleAlarm', multipleAlarm ?? []);
                  setState(() {
                    multipleAlarm = myPrefs.getStringList('multipleAlarm');
                  });
                },
                activeColor: AppColors.secondary2Color,
                trackColor: const Color(0xFF2E3138)),
          ),
        ],
      ),
    );
  }
}
