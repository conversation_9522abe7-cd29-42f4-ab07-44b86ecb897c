part of '../edit_profile.dart';

extension EditProfileExt on State<EditProfileScreen> {
  Row _buildEditActionButtons(
    BuildContext context, {
    required Function() onSubmit,
    required Function() onCancel,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: <Widget>[
        Builder(builder: (BuildContext context) {
          return TextButton.icon(
            // splashColor: Colors.blue,
            icon: const Icon(
              Icons.done,
              color: Colors.green,
            ),
            label: CText.body(S.of(context).submit),
            onPressed: onSubmit,
          );
        }),
        TextButton.icon(
          // splashColor: Colors.blue,
          icon: const Icon(
            Icons.cancel,
            color: Colors.red,
          ),
          label: CText.body(S.of(context).cancel),
          onPressed: onCancel,
        ),
      ],
    );
  }

  BackgroundRound2 _buildPurchase(BuildContext context) {
    return BackgroundRound2(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                CText.body(S.of(context).subscription),
                MBlock(20),
              ],
            ),
          ),
          const MDivider(),
          StreamBuilder<List<PurchaseDetails?>?>(
            stream: injector<AccountBloc>().purchaseDetailsController,
            builder: (context, snap) {
              if (snap.hasData &&
                  snap.data != null &&
                  (snap.data?.length ?? 0) > 0) {
                return Column(
                  children: <Widget>[
                    ListView(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      children: snap.data!.map(
                        (item) {
                          return _buildPurchaseItem(context, item);
                        },
                      ).toList(),
                    ),
                  ],
                );
              } else {
                return _buildSubscribeWidget(context);
              }
            },
          )
        ],
      ),
    );
  }

  Padding _buildPurchaseItem(BuildContext context, PurchaseDetails? item) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Wrap(
        runSpacing: 10,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              CText.body(
                S.of(context).idTransaction,
                fontWeight: FontWeight.w500,
              ),
              CText.body(item?.purchaseID ?? ''
                  // item[
                  //   'transactionId']
                  ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              CText.body(S.of(context).plan, fontWeight: FontWeight.w500),
              CText.body(
                  '${injector<AccountBloc>().getDuration(item?.productID ?? '')}'),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              CText.body(S.of(context).subcribedOn,
                  fontWeight: FontWeight.w500),
              CText.body(DateFormat.yMMMEd()
                  .format(DateTime.fromMillisecondsSinceEpoch(int.parse(
                          item?.transactionDate ?? DateTime.now().toString()))
                      .toLocal())
                  .toString()),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              CText.body(S.of(context).expiresOn, fontWeight: FontWeight.w500),
              CText.body(DateFormat.yMMMEd()
                  .format(DateTime.fromMillisecondsSinceEpoch(
                          int.parse(item!.transactionDate ?? '0'))
                      .add(extendDate(item.productID))
                      .toLocal())
                  .toString()),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              CText.body(S.of(context).status, fontWeight: FontWeight.w500),
              CText.body(
                !Platform.isIOS
                    ? item.status == PurchaseStatus.purchased
                        ? S.of(context).active
                        : ''
                    : S.of(context).active,
                color: !Platform.isIOS
                    ? item.status == PurchaseStatus.purchased
                        ? Colors.green
                        : Colors.red
                    : Colors.green,
              ),
            ],
          ),
          const MDivider()
        ],
      ),
    );
  }

  Column _buildSubscribeWidget(BuildContext context) {
    return Column(
      children: <Widget>[
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              MBlock(16),
              CText.body(
                S.of(context).youDontHaveSubscribeYet,
                textAlign: TextAlign.left,
              ),
              MBlock(6),
              CText.body2(
                S.of(context).subcribeGuide,
                textAlign: TextAlign.left,
                color: const Color(0xFFACB5C5),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: CButton(
            S.of(context).subscribeNow.toUpperCase(),
            onTap: () {
              context.startPayment();
            },
          ),
        ),
        const SizedBox(
          height: 4,
        ),
      ],
    );
  }

  BackgroundRound3 _buildDeleteAccount(BuildContext context) {
    return BackgroundRound3(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              context.startDeleteAccount();
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  CText.body(
                    S.of(context).deleteAccount,
                    color: const Color(0xFFE77A96),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Duration extendDate(id) {
  if (id == 'month_1') {
    return const Duration(days: 30);
  } else if (id == 'month_6') {
    return const Duration(days: 180);
  } else if (id == 'year_1') {
    return const Duration(days: 365);
  }
  return const Duration();
}
