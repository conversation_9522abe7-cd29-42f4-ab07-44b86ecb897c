import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:intl/intl.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/back_floating_action.dart';
import 'package:meditaguru/src/core/widgets/dialog/date_time_dialog.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/user/user_full_info.dart';
import 'package:meditaguru/src/domain/usecase/user_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/dashboard_old/settings/setting.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';

import '../../core/utils.dart';
import '../../domain/usecase/medita_usecase.dart';

part 'extension/edit_profile_ext.dart';

class EditProfileScreen extends StatefulWidget {
  static const String routeName = '/profile-screen';

  @override
  _EditProfileState createState() => _EditProfileState();
}

final TextEditingController phnctlr = TextEditingController();
final TextEditingController emailctlr = TextEditingController();
final TextEditingController dobctlr = TextEditingController();

class _EditProfileState extends State<EditProfileScreen> {
  String? username;
  bool edit = false;
  String? joindate;
  DateTime? dobTime;
  String? mail;
  String? photourl;

  // var transaction = [];
  String? uploadedFileURL;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  File? _image;

  @override
  void initState() {
    super.initState();
    currentuser();
    // injector<AccountBloc>().getpastPurchases();
  }

  void showImageAction() {
    showDialog(
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            title: Text(S.of(context).selectOne),
            children: <Widget>[
              SimpleDialogOption(
                child: Text(
                  S.of(context).gallery,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                onPressed: () {
                  getImage();
                  context.pop();
                },
              ),
              SimpleDialogOption(
                child: Text(
                  S.of(context).cancel,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                onPressed: () {
                  context.pop();
                },
              )
            ],
          );
        });
  }

  Future getImage() async {
    var image = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (image != null) {
      var croppedFile = await ImageCropper.platform.cropImage(
        sourcePath: image.path,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Crop',
            toolbarColor: AppColors.primaryColor,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            cropStyle: CropStyle.circle,
            aspectRatioPresets: [
              CropAspectRatioPreset.square,
            ],
            lockAspectRatio: true,
          ),
          IOSUiSettings(
            minimumAspectRatio: 1.0,
          )
        ],
      );

      setState(() {
        _image = File(croppedFile?.path ?? '');
      });
    }
  }

  Future uploadFile(BuildContext context) async {
    if (await checkExistPhoneNumber()) {
      ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
          .showSnackBar(SnackBar(
        content: Text(S.of(context).phoneExists),
        duration: const Duration(seconds: 5),
      ));
      return;
    }
    var _firebaseAuth = FirebaseAuth.instance;
    var user = _firebaseAuth.currentUser;
    var storageReference =
        FirebaseStorage.instance.ref().child('users/${user?.uid}/myimage.jpg');
    var uploadTask = storageReference.putFile(_image!);
    await uploadTask.whenComplete(() async {});
    if (uploadTask.snapshot.state == TaskState.running) {
      ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
          .showSnackBar(SnackBar(
        content: Text(S.of(context).uploading),
      ));
    }

    if (uploadTask.snapshot.state == TaskState.success) {
      ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
          .showSnackBar(SnackBar(
        duration: const Duration(seconds: 2),
        content: Text(S.of(context).uploadedSuccessfully),
      ));
      await storageReference.getDownloadURL().then((fileURL) async {
        uploadedFileURL = fileURL;
        try {
          await injector.get<UserUsecase>().updateUserInfo(
                uid: '${user?.uid}',
                dob: dobTime?.toString(),
                phone: phnctlr.text,
                email: emailctlr.text,
                photoUrl: uploadedFileURL,
              );
        } catch (err) {
          print('Error: $err');
          ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
              .showSnackBar(SnackBar(
            content: Text(err.toString()),
            duration: const Duration(seconds: 5),
          ));
        }
        await currentuser();
        _image = null;
      });
    }
  }

  Future<void> updatedata() async {
    if (await checkExistPhoneNumber()) {
      ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
          .showSnackBar(SnackBar(
        content: Text(S.of(context).phoneExists),
        duration: const Duration(seconds: 5),
      ));
      return;
    }
    var _firebaseAuth = FirebaseAuth.instance;
    var user = _firebaseAuth.currentUser;
    try {
      await injector.get<UserUsecase>().updateUserInfo(
            uid: '${user?.uid}',
            dob: dobTime?.toString(),
            phone: phnctlr.text,
            email: emailctlr.text,
          );

      ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
          .showSnackBar(SnackBar(
        content: Text(S.of(context).profileUpdated),
        duration: const Duration(seconds: 5),
      ));
      await currentuser();
    } catch (err) {
      print('Error: $err');
      ScaffoldMessenger.of(_scaffoldKey.currentState!.context)
          .showSnackBar(SnackBar(
        content: Text(err.toString()),
        duration: const Duration(seconds: 5),
      ));
    }
  }

  Future<bool> checkExistPhoneNumber() async {
    var userFullInfo = await injector<AccountBloc>().getFullInfoUser();
    if (phnctlr.text != userFullInfo.phone) {
      var isPhoneNumberExist =
          await injector<MeditaUsecase>().checkExistPhoneNumber(
        phnctlr.text,
        currentUserId: injector<AccountBloc>().auth.currentUser?.uid ?? '',
      );
      if (isPhoneNumberExist) {
        await currentuser();
        return true;
      }
    }
    return false;
  }

  Future<UserFullInfo> currentuser() async {
    var user = await injector<AccountBloc>().getFullInfoUser();
    if (mounted) {
      setState(() {
        username = user.name ?? '';
        mail = user.email ?? '';
        emailctlr.text = mail ?? '';
        photourl = user.photoUrl ?? '';
        phnctlr.text = user.phone ?? '';
        joindate = user.joindate == null
            ? ''
            : DateFormat.yMMMEd()
                .format(DateTime.parse(user.joindate ?? ''))
                .toString();

        dobctlr.text = user.dob == null
            ? ''
            : MDateUtils().getDDMMYY(DateTime.parse(user.dob ?? ''));
        dobTime = user.dob == null ? null : DateTime.parse(user.dob ?? '');
        // transaction = StringUtils.isEmpty(onData.data()["transaction"]) ? [] : onData.data()["transaction"];
      });
    }
    return user;
  }

  Container _buildProfile(BuildContext context) {
    var size = MediaQuery.sizeOf(context).height * .22;

    ImageProvider<Object> imageDe =
        const AssetImage('assets/img/ic_launcher.png');
    if (_image != null) {
      imageDe = FileImage(_image!);
    } else if (photourl?.isNotEmpty ?? false) {
      imageDe =
          NetworkImage(Tools.getHigherResProviderPhotoUrl(photourl.toString()));
    }
    return Container(
      // height: MediaQuery.sizeOf(context).height * .31,
      //width: MediaQuery.sizeOf(context).width,
      padding: const EdgeInsets.only(bottom: 3),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const SizedBox(
            height: 20,
          ),
          Container(
            width: size,
            height: size,
            child: Stack(
              children: <Widget>[
                Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.all(Radius.circular(size / 2)),
                      image: DecorationImage(
                          image: imageDe,
                          // image: _image != null ? FileImage(_image)
                          //     : photourl != null
                          //     ? NetworkImage(getHigherResProviderPhotoUrl(photourl.toString()))
                          //     : AssetImage("assets/img/ic_launcher.png"),
                          fit: BoxFit.cover)),
                ),
                edit
                    ? Container(
                        alignment: Alignment.bottomRight,
                        height: size,
                        child: Container(
                          height: 45,
                          width: 45,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.add_a_photo,
                              color: AppColors.primaryColor,
                            ),
                            onPressed: showImageAction,
                          ),
                        ),
                      )
                    : const SizedBox(),
              ],
            ),
          ),
          Container(
            // width: MediaQuery.sizeOf(context).width / 2 - 20,
            // alignment: Alignment.bottomLeft,
            padding: const EdgeInsets.only(top: 20, bottom: 5),
            child: CText.title1(
              '$username',
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      scaffoldKey: _scaffoldKey,
      floatingActionButton: BackFloatingAction(
        onPressed: () {
          context.pop();
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
      body: Center(
        child: Container(
          child: joindate == null && username == null
              ? const CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                )
              : SafeArea(
                  child: Container(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        children: <Widget>[
                          _buildProfile(context),
                          const SizedBox(height: 20),
                          _buildUserInfor(context),
                          const Divider(),
                          if (!injector<AccountBloc>().isPuchasedPremiumManual)
                            _buildPurchase(context),
                          const SizedBox(height: 20),
                          _buildDeleteAccount(context),
                          MBlock(20),
                        ],
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  BackgroundRound1 _buildUserInfor(BuildContext context) {
    return BackgroundRound1(
      paddingVertical: 0,
      child: Form(
        child: Column(
          children: <Widget>[
            ListTile(
                title: CText.body(S.of(context).userInfo),
                trailing: !edit
                    ? TextButton.icon(
                        // splashColor: Colors.blue,
                        icon: const Icon(
                          Icons.edit,
                          color: AppColors.whiteColor,
                        ),
                        label: CText.body(S.of(context).edit),
                        onPressed: () {
                          setState(() {
                            edit = true;
                          });
                        },
                      )
                    : const SizedBox()),
            edit
                ? ListTile(
                    title: TextFormField(
                      style: CText.body('').style,
                      readOnly: (mail?.isNotEmpty ?? false)
                          // !edit || emailctlr.text.length != 0
                          ? true
                          : false,
                      decoration: InputDecoration(
                        hintText: '<EMAIL>',
                        // helperText: S.of(context).immutableIfSetOnce,
                        labelText: 'Email',
                        hintStyle: CText.body(
                          '',
                          color: const Color(0xFFACB5C5),
                        ).style,
                        labelStyle: CText.body('').style,
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.white),
                        ),
                        border: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                      ),
                      controller: emailctlr,
                      onSaved: (value) {
                        emailctlr.text = value ?? '';
                      },
                    ),
                    leading: const Icon(
                      Icons.email,
                      color: AppColors.whiteColor,
                    ),
                  )
                : ListTile(
                    leading:
                        const Icon(Icons.email, color: AppColors.whiteColor),
                    title: CText.body('Email'),
                    subtitle: CText.body(
                      '${emailctlr.text}',
                      color: const Color(0xFFACB5C5),
                    ),
                  ),
            if (!edit) ...[
              const MDivider(),
            ],
            edit
                ? ListTile(
                    title: TextFormField(
                      keyboardType: TextInputType.number,
                      style: CText.body('').style,
                      readOnly: !edit ? true : false,
                      decoration: InputDecoration(
                        labelText: S.of(context).phoneNumber,
                        labelStyle: CText.body('').style,
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.white),
                        ),
                        border: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                      ),
                      controller: phnctlr,
                      onSaved: (value) {
                        phnctlr.text = value ?? '';
                      },
                    ),
                    leading: const Icon(
                      Icons.phone,
                      color: Colors.white,
                    ),
                  )
                : ListTile(
                    title: CText.body(S.of(context).phoneNumber),
                    leading: const Icon(Icons.phone, color: Colors.white),
                    subtitle: CText.body(
                      '${phnctlr.text}',
                      color: const Color(0xFFACB5C5),
                    ),
                  ),
            if (!edit) ...[
              const MDivider(),
            ],
            edit
                ? ListTile(
                    onTap: () async {
                      var date = await DateTimeDialog.selectDate(
                          context, dobTime ?? DateTime.now());
                      if (date != null) {
                        dobTime = date;
                        dobctlr.text = MDateUtils().getDDMMYY(date);
                      }
                    },
                    title: TextFormField(
                      style: CText.body('').style,
                      // readOnly: true,
                      enabled: false,
                      decoration: InputDecoration(
                        hintText: '',
                        // helperText: S.of(context).immutableIfSetOnce,
                        labelText: S.of(context).dob,
                        hintStyle: CText.body(
                          '',
                          color: const Color(0xFFACB5C5),
                        ).style,
                        labelStyle: CText.body('').style,
                        enabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.white),
                        ),
                        border: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                        disabledBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                      ),
                      controller: dobctlr,
                      // enabled: false,
                    ),
                    leading: const Icon(
                      FontAwesomeIcons.cakeCandles,
                      color: AppColors.whiteColor,
                    ),
                  )
                : ListTile(
                    leading: const Icon(FontAwesomeIcons.cakeCandles,
                        color: AppColors.whiteColor),
                    title: CText.body(S.of(context).dob),
                    subtitle: CText.body(
                      '${dobctlr.text}',
                      color: const Color(0xFFACB5C5),
                    ),
                  ),
            if (!edit) ...[
              const MDivider(),
            ],
            ListTile(
              title: CText.body(S.of(context).joinedDate),
              subtitle: CText.body(
                '$joindate',
                color: const Color(0xFFACB5C5),
              ),
              leading: const Icon(
                Icons.date_range,
                color: Colors.white,
              ),
            ),
            if (injector<AccountBloc>().expiryDate != null) ...[
              const MDivider(),
            ],
            if (injector<AccountBloc>().expiryDate != null) ...[
              ListTile(
                title: CText.body(S.of(context).trialTime),
                subtitle: CText.body(
                  '${DateFormat.yMMMEd().format(injector<AccountBloc>().expiryDate ?? DateTime.now()).toString()}',
                  color: const Color(0xFFACB5C5),
                ),
                leading: const Icon(
                  Icons.timer,
                  color: Colors.white,
                ),
              ),
            ],
            edit
                ? _buildEditActionButtons(
                    context,
                    onSubmit: () async {
                      _image == null ? updatedata() : await uploadFile(context);

                      setState(() {
                        edit = false;
                      });
                    },
                    onCancel: () {
                      //for on cancel editing to show data as it is
                      setState(() {
                        currentuser();
                        _image = null;

                        edit = false;
                      });
                    },
                  )
                : Container(),
          ],
        ),
      ),
    );
  }
}
