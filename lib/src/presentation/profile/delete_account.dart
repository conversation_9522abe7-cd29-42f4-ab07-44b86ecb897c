import 'package:flutter/material.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

import '../../../generated/l10n.dart';
import '../../core/base/bloc/base_state.dart';
import '../../core/widgets/base_widgets/m_button.dart';
import '../../core/widgets/base_widgets/m_scaffold.dart';
import '../../core/widgets/base_widgets/m_text.dart';
import '../../core/widgets/common/back_floating_action.dart';
import '../../core/widgets/dialog/dialog_widgets.dart';
import '../shared/account/account_bloc.dart';

const _captcha = 'DELETE';

class DeleteAccountScreen extends StatefulWidget {
  static const String routeName = '/delete-account-screen';

  const DeleteAccountScreen({super.key});

  @override
  State<DeleteAccountScreen> createState() => _DeleteAccountScreenState();
}

class _DeleteAccountScreenState extends BaseStateScreen<DeleteAccountScreen> {
  final ValueNotifier<bool> _enableDeleteButtonNotifier = ValueNotifier(false);

  void _checkValidCaptcha(String? captcha) {
    if (captcha == _captcha) {
      _enableDeleteButtonNotifier.value = true;
    } else {
      _enableDeleteButtonNotifier.value = false;
    }
  }

  Future<void> _onTapDelete() async {
    final currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
    final _accountBloc = injector<AccountBloc>();
    final result = await _accountBloc.delelteAccount();
    if (result) {
      // Use `showFailureDialog` instead of `show Success Dialog` there is a
      // close button on the top right which is only used to close the dialog
      DialogManager.showFailureDialog(
        context: context,
        message: S.of(context).deleteAccountSuccess,
        onClose: () async {
          await _accountBloc.logout().whenComplete(() {
            context.startLogin(isReplacement: true, useRoot: false);
          });
        },
      );
    } else {
      DialogManager.showFailureDialog(
        context: context,
        message: S.of(context).anErrorOccurredAndTryAgain,
      );
    }
  }

  @override
  void dispose() {
    _enableDeleteButtonNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MScaffold(
      floatingActionButton: const BackFloatingAction(),
      floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
      resizeToAvoidBottom: false,
      body: Padding(
        padding: const EdgeInsets.only(left: 16, top: 0, right: 16, bottom: 16),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const SizedBox(height: 40),
                    Center(
                      child: CText.header2(S.of(context).deleteAccount),
                    ),
                    const SizedBox(height: 20),
                    CText.body(S.of(context).deleteAccountMsg),
                    const SizedBox(height: 16),
                    CText.body1(S.of(context).account),
                    const SizedBox(height: 4),
                    CText.body(S.of(context).accountDeleteDescription),
                    const SizedBox(height: 16),
                    CText.body1(S.current.emailSubscription),
                    const SizedBox(height: 4),
                    CText.body(S.current.emailDeleteDescription),
                    const SizedBox(height: 16),
                    CText.body1(S.current.subscription),
                    const SizedBox(height: 4),
                    CText.body(S.current.subscriptionDeleteDescription),
                    const SizedBox(height: 16),
                    CText.body1(S.current.confirmAccountDeletion),
                    const SizedBox(height: 4),
                    CText.body(S.current.enterCaptcha(_captcha)),
                    const SizedBox(height: 16),
                    TextFormField(
                      style: CText.body('').style,
                      decoration: const InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFACB5C5)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.white),
                        ),
                      ),
                      onChanged: _checkValidCaptcha,
                    ),
                  ],
                ),
              ),
            ),
            ValueListenableBuilder<bool>(
              valueListenable: _enableDeleteButtonNotifier,
              builder: (context, enable, child) {
                return CButton(
                  S.of(context).continueNext,
                  onTap: _onTapDelete,
                  enabled: enable,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
