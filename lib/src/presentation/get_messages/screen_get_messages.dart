import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/wrappers/description_highlight_text.dart';

import '../shared/daily_inspiration/daily_inspiration_model.dart';

class ScreenGetMessages extends StatefulWidget {
  static const String routeName = '/get-message';

  const ScreenGetMessages({super.key});

  @override
  State<ScreenGetMessages> createState() => _ScreenGetMessagesState();
}

class _ScreenGetMessagesState extends State<ScreenGetMessages>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<Offset> _slideTextAnimation;
  late final Animation<Offset> _slideButtonAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _slideTextAnimation = Tween<Offset>(
      begin: const Offset(0, -5),
      end: const Offset(0, 0),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
      ),
    );

    _slideButtonAnimation = Tween<Offset>(
      begin: const Offset(0, 5),
      end: const Offset(0, 0),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
      ),
    );

    Future.delayed(const Duration(milliseconds: 500), () {
      _animationController.forward();
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<DailyInspirationModel>().preloadImage(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kDarkBG,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.secondary6Color.withOpacity(0.2),
              AppColors.transparent,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Center(
          child: Column(
            children: [
              Container(
                height: MediaQuery.of(context).size.height * 0.5,
                child: Center(
                  child: SlideTransition(
                    position: _slideTextAnimation,
                    child: KeywordsHighlightWidget(
                      text: '${S.of(context).message}\n${S.of(context).today}',
                      keywordsHighlight: [S.of(context).today],
                      textAlign: TextAlign.center,
                      fontSize: 28.0,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  child: Stack(
                    children: [
                      const Positioned(
                        bottom: 0,
                        left: 0,
                        child: ImageWidget(IconConstants.moon02),
                      ),
                      const Positioned(
                        bottom: 0,
                        right: 0,
                        child: ImageWidget(IconConstants.lotus03),
                      ),
                      Positioned.fill(
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: SafeArea(
                            minimum: const EdgeInsets.only(bottom: 50.0),
                            child: SlideTransition(
                              position: _slideButtonAnimation,
                              child: ButtonGradiantWidget.primary(
                                onPressed: context.startChooseCardScreen,
                                paddingContent: const EdgeInsets.symmetric(
                                  vertical: 16.0,
                                  horizontal: 100.0,
                                ),
                                title: S.of(context).continueNext,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
