import 'dart:math';

import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/deep_link_service.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/m_button.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:vector_math/vector_math.dart' as vector;

import '../../core/services/share_service.dart';

class InviteFirendsScreen extends StatelessWidget {
  static const String routeName = '/invite-friends-screen';

  @override
  Widget build(BuildContext context) {
    var size = Size(MediaQuery.sizeOf(context).width, 200.0);
    return Scaffold(
        // appBar: AppBar(
        //   title: Text(S.of(context).inviteGuide),
        // ),
        floatingActionButton: AnimatedOpacity(
          opacity: 1.0,
          duration: const Duration(milliseconds: 50),
          child: Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: FloatingActionButton(
              elevation: 10,
              backgroundColor: Colors.white38,
              onPressed: () {
                context.pop();
              },
              child: const BackButton(
                color: Colors.black,
              ),
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
        body: Container(
          child: Column(
            children: <Widget>[
              const SizedBox(
                height: 80,
              ),
              Container(
                width: size.width,
                height: 200,
                // child: Image.network(
                //   'https://www.tokia.io/wp-content/uploads/2018/10/tokia-refferal-illiustration-2.png',
                //   fit: BoxFit.cover,
                // )
              ),
              const SizedBox(
                height: 10.0,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20.0),
                child: Text(
                  S.of(context).inviteGuide,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 25,
                      color: AppColors.darkPrimaryColor),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Text(
                  S.of(context).inviteYourFriendInThisApp,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              MButtonGradient(
                title: S.of(context).inviteYourFriend.toUpperCase(),
                onTap: () async {
                  final box = context.findRenderObject() as RenderBox?;
                  var link = await DeeplinkService.createDynamicLink(
                      userId:
                          injector<AccountBloc>().auth.currentUser?.uid ?? '');

                  var shareContent =
                      S.of(context).inviteMessage(link.toString());
                  await ShareService.share(
                      // "Hello, I've recently installed this meditation app. It's really good. Check it out ${GlobalConfiguration().getValue("dynamicLinkInvite")}",
                      text: shareContent,
                      subject: 'Thiền Thức Tỉnh',
                      sharePositionOrigin:
                          box!.localToGlobal(Offset.zero) & box.size);
                },
              ),
              // TextButton(
              //   color: AppColors.primaryColor,
              //   textColor: Colors.white,
              //   onPressed: () async {
              //     final RenderBox box = context.findRenderObject();
              //     var link = await DeeplinkService.createDynamicLink(userId: injector<AccountBloc>().auth.currentUser.uid);
              //
              //     String shareContent = S.of(context).inviteMessage(link.toString());
              //     Share.share(
              //         // "Hello, I've recently installed this meditation app. It's really good. Check it out ${GlobalConfiguration().getValue("dynamicLinkInvite")}",
              //         shareContent,
              //         subject: "Thiền Thức Tỉnh",
              //         sharePositionOrigin:
              //             box.localToGlobal(Offset.zero) & box.size);
              //   },
              //   child: Text(S.of(context).inviteYourFriend.toUpperCase()),
              // ),
              Expanded(child: Container()),
              ShareInvite(
                size: size,
                xOffset: 50,
                color: AppColors.primaryColor,
                yOffset: 10,
              )
            ],
          ),
        ));
  }
}

class ShareInvite extends StatefulWidget {
  final Size size;
  final int xOffset;
  final int yOffset;
  final Color? color;
  ShareInvite({
    required this.size,
    required this.xOffset,
    required this.yOffset,
    required this.color,
  });

  @override
  _ShareInviteState createState() => _ShareInviteState();
}

class _ShareInviteState extends State<ShareInvite>
    with TickerProviderStateMixin {
  late AnimationController animationController;
  List<Offset> animList1 = [];

  @override
  void initState() {
    animationController =
        AnimationController(vsync: this, duration: const Duration(seconds: 2));

    animationController
      ..addListener(
        () {
          animList1.clear();
          for (var i = -2 - widget.xOffset;
              i <= widget.size.width.toInt() + 2;
              i++) {
            animList1.add(Offset(
                i.toDouble() + widget.xOffset,
                sin((animationController.value * 360 - i) %
                            360 *
                            vector.degrees2Radians) *
                        20 +
                    50 +
                    widget.yOffset));
          }
        },
      )
      ..repeat();

    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: AnimatedBuilder(
        animation: CurvedAnimation(
          parent: animationController,
          curve: Curves.easeInOut,
        ),
        builder: (context, child) => ClipPath(
          clipper: WaveClipper(animationController.value, animList1),
          child: widget.color == null
              ? Image.asset(
                  'images/demo5bg.jpg',
                  width: widget.size.width,
                  height: widget.size.height,
                  fit: BoxFit.cover,
                )
              : Container(
                  width: widget.size.width,
                  height: widget.size.height,
                  decoration: BoxDecoration(gradient: Style.linearGradient()),
                  // color: widget.color,
                ),
        ),
      ),
    );
  }
}

class WaveClipper extends CustomClipper<Path> {
  final double animation;

  List<Offset> waveList1 = [];

  WaveClipper(this.animation, this.waveList1);

  @override
  Path getClip(Size size) {
    var path = Path()
      ..addPolygon(waveList1, false)
      ..lineTo(size.width, size.height)
      ..lineTo(0.0, size.height)
      ..close();
    return path;
  }

  @override
  bool shouldReclip(WaveClipper oldClipper) =>
      animation != oldClipper.animation;
}
