import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../../../core/widgets/common/background_layout_widget.dart';

class BreathingExerciseIntroScreen extends StatefulWidget {
  const BreathingExerciseIntroScreen({super.key});

  static const String routeName = '/breathing-exercise-intro';

  @override
  State<BreathingExerciseIntroScreen> createState() =>
      _BreathingExerciseIntroScreenState();
}

class _BreathingExerciseIntroScreenState
    extends State<BreathingExerciseIntroScreen> {
  @override
  Widget build(BuildContext context) {
    final heightScreen = MediaQuery.sizeOf(context).height;
    final heightImageBg = heightScreen * 0.45;

    return BackgroundLayoutWidget(
      backgroundImage: 'assets/img/bg_breathing.png',
      useLoading: false,
      useBackgroundOverlay: false,
      heightBackground: heightImageBg,
      showLabelAppBar: false,
      body: Column(
        children: [
          SizedBox(
            height: heightScreen * 0.35,
            width: 1,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  GradientText(
                    'Tập thở cơ hoành',
                    style: const TextStyle(
                      fontSize: 22.0,
                      height: 28 / 22,
                      fontWeight: FontWeight.w600,
                    ),
                    colors: [
                      const Color(0xffFF50D9),
                      const Color(0xffFFD028),
                      const Color(0xffFFB077),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Tập thở bằng cơ hoành giúp lưu thông \nmáu lên não, điều hoà nhịp tim, \nhuyết áp, giúp thư giãn, \ngiảm căng thẳng.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Color(0xffEAECF0),
                      fontSize: 15,
                      fontWeight: FontWeight.w400,
                      height: 20 / 15,
                    ),
                  ),
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 29),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        ButtonGradiantWidget.primary(
                          onPressed: context.startBreathingExercise,
                          title: 'Bắt đầu tập thở',
                        ),
                        const SizedBox(height: 11),
                        ButtonSelectorWidget(
                          onPressed: context.startBreathingExerciseDetails,
                          isSelected: true,
                          labelText: 'Hướng dẫn tập thở',
                          styleLabel: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              height: 20 / 15),
                          backgroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(vertical: 15),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
