import 'package:flutter/material.dart';

import '../../../../domain/entities/breathing_exercise_action.dart';

class BreadthingActionWidget extends StatefulWidget {
  const BreadthingActionWidget({
    super.key,
    this.action,
    this.onStart,
    this.onPlay,
    this.onPause,
  });

  final BreathingAction? action;
  final void Function()? onStart;
  final void Function()? onPlay;
  final void Function()? onPause;

  @override
  State<BreadthingActionWidget> createState() => _BreadthingActionWidgetState();
}

class _BreadthingActionWidgetState extends State<BreadthingActionWidget> {
  late var _action = widget.action ?? BreathingAction.play;
  void _onTapAction() {
    switch (_action) {
      case BreathingAction.start:
        widget.onStart?.call();
        break;
      case BreathingAction.pause:
        widget.onPlay?.call();
        break;
      case BreathingAction.play:
        widget.onPause?.call();
      default:
    }
  }

  @override
  void didUpdateWidget(covariant BreadthingActionWidget oldWidget) {
    if (widget.action != _action && widget.action == BreathingAction.start) {
      _action = BreathingAction.start;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _onTapAction();
        setState(() {
          _action = _action.next;
        });
      },
      behavior: HitTestBehavior.translucent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            _action.title,
            style: const TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.w500,
              height: 43 / 36,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 24),
          _action.icon,
        ],
      ),
    );
  }
}
