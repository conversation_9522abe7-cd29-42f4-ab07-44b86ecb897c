import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import 'package:meditaguru/src/core/constants/general.dart';

import '../../../../../core/widgets/common/image_widget.dart';
import '../../painter/gradiant_circle_painter.dart';
import '../breathing_exercise_completed_widget.dart';
import 'breathing_execise_controller.dart';

const _timeStart = 4.0;
const _timeAudioStep = 2000; // milliseconds

class BreathingExerciseWidget extends StatefulWidget {
  const BreathingExerciseWidget({
    super.key,
    required this.controller,
  });

  final BreathingExerciseController controller;

  @override
  State<BreathingExerciseWidget> createState() =>
      _BreathingExerciseWidgetState();
}

class _BreathingExerciseWidgetState extends State<BreathingExerciseWidget>
    with TickerProviderStateMixin {
  BreathingExerciseController get _controllerWidget => widget.controller;
  final _player = AudioPlayer();
  final _playerStart = AudioPlayer();
  final _playerComplete = AudioPlayer();

  bool _isUp = true;
  double _timeTracking = 0;

  late final _controllerStart = AnimationController(
    duration: const Duration(seconds: 4),
    vsync: this,
  );

  late final _controllerBreathing = AnimationController(
    duration: Duration(milliseconds: (kBreathingOneStep * 1000).toInt()),
    vsync: this,
  );

  late final _animationStart =
      Tween<double>(begin: _timeStart, end: 0).animate(_controllerStart);

  late final _animationBreathing =
      Tween<double>(begin: 0.0, end: kBreathingOneStep.toDouble())
          .animate(_controllerBreathing);

  void _mathDiffTime() {
    double diff = 0;
    if (_isUp) {
      diff = _animationBreathing.value - _timeTracking;
    } else {
      diff = _timeTracking - _animationBreathing.value;
    }

    _controllerWidget.updateTotalTime(diff);

    _timeTracking = _animationBreathing.value;
  }

  void _pause() {
    if (_controllerStart.isAnimating) {
      _playerStart.pause();
      _controllerStart.stop();
    } else {
      _controllerBreathing.stop();
    }
  }

  void _play() {
    if (_controllerStart.isCompleted == false) {
      _playerStart.play();
      _controllerStart.forward();
    } else if (_isUp) {
      _controllerBreathing.forward();
    } else {
      _controllerBreathing.reverse();
    }
  }

  Future<void> heavyImpact() async {
    for (var i = 0; i < 5; i++) {
      await HapticFeedback.vibrate();
      await Future.delayed(const Duration(milliseconds: 50));
    }
  }

  void _listenerStart() {
    if (_controllerStart.isCompleted) {
      _playerStart.stop();
      _initAudioService();
      heavyImpact();
      _playAudio();
      _controllerBreathing.forward();
    }
  }

  void _listenerAnimation() {
    final valueTimer = _animationBreathing.value / kBreathingOneStep * 100;
    final statusController = _controllerBreathing.status;

    _controllerWidget.countStep(
      valueTimer,
      status: statusController,
      notifyStep: () {
        _playAudio();
      },
    );

    if (statusController == AnimationStatus.reverse) {
      _isUp = false;
    } else if (statusController == AnimationStatus.forward) {
      _isUp = true;
    } else if ([AnimationStatus.dismissed, AnimationStatus.completed]
        .contains(statusController)) {
      if (_controllerWidget.isCompleted) {
        _controllerBreathing.stop();
      } else {
        if (_isUp) {
          _isUp = false;
          _controllerBreathing.reverse();
        } else {
          _controllerBreathing.forward();
        }
      }
    }
  }

  void _start() async {
    if (_controllerWidget.isCompleted) {
      _isUp = true;
      _timeTracking = 0;

      _controllerBreathing
        ..stop()
        ..reset();

      _controllerStart.reset();
      _controllerWidget.reset();
    } else {
      await Future.delayed(const Duration(seconds: 1));
    }

    unawaited(_playerStart.seek(Duration.zero));
    unawaited(_playerStart.play());
    unawaited(_controllerStart.forward(from: 0));
  }

  void _playAudio({int? timeAudio}) {
    _player.seek(const Duration(seconds: 0)).then((value) {
      _player.play();
    });

    Future.delayed(Duration(milliseconds: timeAudio ?? _timeAudioStep)).then(
      (value) {
        _player.stop();
      },
    );
  }

  Future<void> _initAudioService() async {
    await _player.setAsset('assets/media/elevenlabs.mp3');
    await _player.stop();
  }

  Future<void> _initStartAudio() async {
    await _playerStart.setAsset('assets/media/countdown.mp3');
    await _playerStart.stop();
  }

  Future<void> _initCompleteAudio() async {
    await _playerComplete.setAsset('assets/media/complete.mp3');
    await _playerComplete.stop();
  }

  @override
  void initState() {
    super.initState();
    widget.controller
      ..onPlay = _play
      ..onPause = _pause
      ..onStart = _start;

    _controllerBreathing.addListener(_listenerAnimation);
    _controllerStart.addListener(_listenerStart);
    _controllerWidget.onCompleted = _playerComplete.play;
    _initStartAudio();
    _initAudioService();
    _initCompleteAudio();
  }

  @override
  void dispose() {
    _controllerBreathing
      ..stop()
      ..removeListener(_listenerAnimation)
      ..dispose();

    _controllerStart
      ..stop()
      ..removeListener(_listenerStart)
      ..dispose();

    _playerStart.stop().then((value) => _playerStart.dispose());

    _player.stop().then((value) => _player.dispose());

    _playerComplete.stop().then((value) => _playerComplete.dispose());

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationBreathing,
      builder: (context, child) {
        if (_controllerWidget.isCompleted) {
          return const BreathingExerciseCompletedWidget();
        }

        return Column(
          children: [
            const SizedBox(height: 23),
            Builder(
              builder: (context) {
                _mathDiffTime();
                final value =
                    _animationBreathing.value / kBreathingOneStep * 100;
                return SliderBreathingExercise(percent: value);
              },
            ),
            const SizedBox(height: 22),
            AnimatedBuilder(
              animation: _animationStart,
              builder: (context, child) {
                if (_animationStart.value == _timeStart ||
                    _animationStart.value == 0) {
                  return const SizedBox();
                }

                return Column(
                  children: [
                    const Text(
                      'Bắt đầu sau',
                      style: TextStyle(
                        fontSize: 13,
                        height: 18 / 13,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '${_animationStart.value.toInt()}',
                      style: const TextStyle(
                        fontSize: 30,
                        height: 37 / 30,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    )
                  ],
                );
              },
            ),
            AnimatedBuilder(
              animation: Listenable.merge([
                _animationStart,
                _animationBreathing,
              ]),
              builder: (context, child) {
                if (!_controllerStart.isCompleted) {
                  return const SizedBox();
                }

                var title = 'Thở ra';

                if (_controllerBreathing.status == AnimationStatus.forward) {
                  title = 'Hít vào';
                }

                return Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 36,
                      height: 43 / 36,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}

class SliderBreathingExercise extends StatelessWidget {
  const SliderBreathingExercise({
    super.key,
    required this.percent,
  });

  final double percent;

  @override
  Widget build(BuildContext context) {
    final sizeScreen = MediaQuery.sizeOf(context);
    final width = min(sizeScreen.width * 0.768, 300.0);

    return Container(
      width: width,
      height: width,
      child: Stack(
        children: [
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: CustomPaint(
                size: const Size(200, 200),
                painter: GradientCirclePainter(percent: percent),
              ),
            ),
          ),
          Positioned.fill(
            child: Container(
              width: width,
              decoration: BoxDecoration(
                color: Colors.white12,
                borderRadius: BorderRadius.circular(width),
              ),
            ),
          ),
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                margin: const EdgeInsets.all(0.5),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(width),
                ),
                padding: const EdgeInsets.all(11),
                child: const ImageWidget(
                    'assets/img/bg_breathing_indicator.png'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
