import 'package:flutter/material.dart';

import '../../../../../core/lib_common.dart';

class BreathingExerciseController extends ChangeNotifier {
  double _totalTime = 0;
  int _countStep = 0;

  double get totalTime => _totalTime;

  bool get isCompleted => kBreathingCount == _countStep;
  int get step => _countStep;
  bool _isNotify = false;

  late VoidCallback _onCompleted;

  set onCompleted(VoidCallback value) {
    _onCompleted = value;
  }

  void countStep(
    double valueTimer, {
    Function()? notifyStep,
    required AnimationStatus status,
  }) {
    if (((valueTimer < 8 && status == AnimationStatus.reverse) ||
            (valueTimer > 98 && status == AnimationStatus.forward)) &&
        _isNotify == false) {
      _isNotify = true;
      notifyStep?.call();
    }

    if ([AnimationStatus.completed, AnimationStatus.dismissed]
        .contains(status)) {
      if (valueTimer == 0) {
        _countStep++;

        // cheat data completed
        if (isCompleted) {
          _totalTime = (kBreathingCount * kBreathingCircle).toDouble();
          _onCompleted.call();
        }
      }
      _isNotify = false;
    }
  }

  void updateTotalTime(
    double time,
  ) {
    _totalTime += time;

    updateState();
  }

  void reset() {
    _totalTime = 0;
    _countStep = 0;
    updateState();
  }

  void updateState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        notifyListeners();
      },
    );
  }

  Function()? onPlay;
  Function()? onPause;
  Function()? onStart;
  // Function()? onCompleted;
}
