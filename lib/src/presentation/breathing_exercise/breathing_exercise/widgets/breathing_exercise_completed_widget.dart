import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';

class BreathingExerciseCompletedWidget extends StatelessWidget {
  const BreathingExerciseCompletedWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageWidget(
            'assets/icons/ic_checked.svg',
          ),
          SizedBox(height: 16),
          Text(
            'Chúc mừng',
            style: TextStyle(
              fontSize: 24,
              height: 30 / 24,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          Sized<PERSON><PERSON>(height: 10),
          Text(
            'Bạn đã hoàn thành bài \ntập thở cơ hoành!',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 15,
              height: 20 / 15,
              fontWeight: FontWeight.w400,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
