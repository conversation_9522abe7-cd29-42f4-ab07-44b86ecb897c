import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/extensions/duration_ext.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../../core/base/life_cycle/system_lifecycle_listener.dart';
import '../../../core/constants.dart';
import '../../../core/services/firebase/analytics/firebase_analytics_wapper.dart';
import '../../../core/widgets/common/breathing_exercise_layout.dart';
import '../../../domain/entities/breathing_exercise_action.dart';
import 'widgets/breathing_action_widget.dart';
import 'widgets/breathing_exercise/breathing_execise_controller.dart';
import 'widgets/breathing_exercise/breathing_exercise_widget.dart';
import 'widgets/count_breathing_widget.dart';

class BreathingExerciseScreen extends StatefulWidget {
  const BreathingExerciseScreen({super.key});

  static const String routeName = '/breathing-exercise';

  @override
  State<BreathingExerciseScreen> createState() =>
      _BreathingExerciseScreenState();
}

class _BreathingExerciseScreenState extends State<BreathingExerciseScreen> {
  final _controller = BreathingExerciseController();

  @override
  void initState() {
    super.initState();
    WakelockPlus.enable();
    _controller.onCompleted = () {};
    WidgetsBinding.instance.addPostFrameCallback((a) {
      _controller.onStart?.call();
    });

    SystemLifecycleListener.instance.addCallback((lifeCycle) {
      if (lifeCycle == AppLifeCycle.detached) {
        FirebaseAnalyticsWapper()
            .analytics
            .logEvent(name: TrackingEvent.practiceBreath, parameters: {
          TrackingParameter.userTime: DateTime.now().toString(),
          TrackingParameter.breathDuration: _controller.totalTime.toInt(),
        });
      }
    });
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    super.dispose();
  }

  void _onPause() {
    _controller.onPause?.call();
    FirebaseAnalyticsWapper()
        .analytics
        .logEvent(name: TrackingEvent.practiceBreath, parameters: {
      TrackingParameter.userTime: DateTime.now().toString(),
      TrackingParameter.breathDuration: _controller.totalTime.toInt(),
    });
  }

  void _onStart() {
    _controller.onStart?.call();
    FirebaseAnalyticsWapper()
        .analytics
        .logEvent(name: TrackingEvent.practiceBreath, parameters: {
      TrackingParameter.userTime: DateTime.now().toString(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return BreathingExerciseLayout(
      title: 'Tập thở cơ hoành',
      onBack: _onPause,
      body: Column(
        children: [
          Expanded(
            child: BreathingExerciseWidget(controller: _controller),
          ),
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                height: 250,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 32),
                      child: Row(
                        children: [
                          Expanded(
                            child: CountBreathingWidget(
                              count: (_controller.isCompleted
                                      ? Duration(
                                          milliseconds:
                                              (_controller.totalTime * 1000)
                                                  .toInt())
                                      : Duration(
                                          milliseconds:
                                              (_controller.totalTime * 1000)
                                                  .toInt()))
                                  .timemmss,
                              icon: 'assets/icons/ic_breathing.svg',
                              label: 'Thời gian thở',
                            ),
                          ),
                          Expanded(
                            child: CountBreathingWidget(
                              count: '${_controller.step}/$kBreathingCount',
                              icon: 'assets/icons/ic_clock.svg',
                              label: 'Số lần thở',
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    BreadthingActionWidget(
                      action: _controller.isCompleted
                          ? BreathingAction.start
                          : null,
                      onStart: _onStart,
                      onPlay: () => _controller.onPlay?.call(),
                      onPause: _onPause,
                    ),
                    const SizedBox(height: 25),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
