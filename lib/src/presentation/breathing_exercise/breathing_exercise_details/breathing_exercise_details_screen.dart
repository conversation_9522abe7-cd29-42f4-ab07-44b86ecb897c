import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';

import '../../../core/widgets/common/breathing_exercise_layout.dart';
import '../../video/video_player_short.dart';

class BreathingExerciseDetailsScreen extends StatefulWidget {
  const BreathingExerciseDetailsScreen({super.key});

  static const String routeName = '/breathing-exercise-details';

  @override
  State<BreathingExerciseDetailsScreen> createState() =>
      _BreathingExerciseDetailsScreenState();
}

class _BreathingExerciseDetailsScreenState
    extends State<BreathingExerciseDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final style = const TextStyle(
      fontSize: 14,
      height: 22 / 14,
      fontWeight: FontWeight.w400,
      color: Colors.white,
    );

    return BreathingExerciseLayout(
      title: 'Hướng dẫn tập thở cơ hoành',
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 16),
            Si<PERSON><PERSON>ox(
              width: MediaQuery.sizeOf(context).width,
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: VideoPlayerNormal(
                  url: 'assets/media/0715.mp4',
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '''Khi thở cơ hoành, cơ liên sườn, cơ bụng cũng hoạt động. Đây là kiểu thở tối ưu vì cách thở này trung đạo, giúp mát xa tích cực các bộ phận dưới cơ hoành như gan, ruột già, thận. Khi thở chậm, ta thức tỉnh một số năng lực, cùng tần số rung của vũ trụ. Đây là cách thở tốt nhất cho sinh lý, tâm lý, tâm linh.\n\nCó rất nhiều lợi ích khi thở cơ hoành:''',
              style: style,
            ),
            ...[
              'Tăng lượng oxy trong máu',
              'Giúp cơ thể thải khí ra khỏi phổi dễ dàng hơn.',
              'Cải thiện sự ổn định trong các cơ cốt lõi, tăng khả năng chịu đựng của cơ thể khi tập thể dục cường độ cao.',
              'Làm chậm nhịp thở giúp tiêu hao ít năng lượng hơn.',
            ]
                .map(
                  (e) => SubInfoWidget(
                    info: e,
                    style: style,
                  ),
                )
                .toList(),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: ButtonGradiantWidget.secondary02(
                onPressed: context.startBreathingExercise,
                title: 'Thực hành ngay',
              ),
            ),
            const SizedBox(height: 25),
          ],
        ),
      ),
    );
  }
}

class SubInfoWidget extends StatelessWidget {
  const SubInfoWidget({
    super.key,
    required this.info,
    required this.style,
  });
  final String info;
  final TextStyle style;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('  •   ', style: style),
        Expanded(
          child: Text(
            info,
            style: style,
          ),
        ),
      ],
    );
  }
}
