import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/lib_common.dart' hide Style;
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/back_floating_action.dart';
import 'package:meditaguru/src/core/widgets/common/webview.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';

class HtmlDetail {
  String image;
  String title;
  String content;

  HtmlDetail({required this.image, required this.title, required this.content});
}

class HtmlDetailScreen extends StatefulWidget {
  static const String routeName = '/html-detail-screen';

  final HtmlDetail news;

  const HtmlDetailScreen({Key? key, required this.news}) : super(key: key);

  @override
  _NewsDetailScreenState createState() => _NewsDetailScreenState();
}

class _NewsDetailScreenState extends BaseStateScreen<HtmlDetailScreen> {
  late String content;

  @override
  void initState() {
    super.initState();
    content = widget.news.content;
    if (mounted) {
      injector<AccountBloc>().getGlobalConfig().then((value) {
        setState(() {
          content = injector<AccountBloc>().globalConfig?.bannerLink ?? '';
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      floatingActionButton: const BackFloatingAction(),
      floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            const SizedBox(
              height: 40,
            ),
            const Padding(
              padding: EdgeInsets.only(left: 0.0),
              child: Text(
                'Thiền Thức Tỉnh',
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 35,
                    fontFamily: 'MyFont2',
                    color: AppColors.secondaryColor),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            _buildDescription(),
          ],
        ),
      ),
    );
  }

  Container _buildDescription() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Html(
            style: {
              'html': Style(
                fontFamily: 'MyFont',
                color: AppColors.contentBlackColor,
                fontSize: FontSize(16),
              )
            },
            onLinkTap: (String? url, Map<String, String> attributes, _) {
              WebView.navigate(
                context: context,
                url: url ?? '',
                title: widget.news.title,
              );
            },
            data: content,
          ),
          MBlock(16),
        ],
      ),
    );
  }
}
