import 'package:flutter/material.dart';

class ScaleFadeTransition extends StatelessWidget {
  const ScaleFadeTransition({
    required this.scale,
    required this.opacity,
    required this.child,
  });

  final Animation<double> scale;
  final Animation<double> opacity;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: scale,
      child: FadeTransition(
        opacity: opacity,
        child: child,
      ),
    );
  }
}
