import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/services/url_launcher_service.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/back_floating_action.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/global_config.dart';
import 'package:meditaguru/src/domain/usecase/app_setting_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/dashboard_old/settings/setting.dart';
import 'package:meditaguru/src/presentation/payment/subscription_bloc.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:provider/provider.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

import '../../core/utils.dart';

class Subscription extends StatefulWidget {
  static const String routeName = '/payment-screen';

  Subscription();

  static Provider<SubscriptionBloc> newInstance() => Provider<SubscriptionBloc>(
        create: (_) => SubscriptionBloc(),
        dispose: (_, _bloc) => _bloc.dispose(),
        child: Subscription(),
      );

  @override
  _SubscriptionState createState() => _SubscriptionState();
}

class _SubscriptionState extends State<Subscription> {
  late SubscriptionBloc _subscriptionBloc;

  @override
  void initState() {
    super.initState();
    _subscriptionBloc = Provider.of<SubscriptionBloc>(context, listen: false);
    _subscriptionBloc.handleUIBloc.uiHandleController.listen((value) async {
      var context = AppBloc.navigatorKey.currentState!.context;
      if (value.isError) {
        await Alert(
          context: context,
          type: AlertType.error,
          title: S.of(context).failed,
          desc: value.message,
          buttons: [
            DialogButton(
              onPressed: () => context.pop(),
              width: 120,
              child: Text(
                S.of(context).tryAgain,
                style: const TextStyle(color: Colors.white, fontSize: 20),
              ),
            )
          ],
        ).show();
      }

      if (value.isSucess) {
        await context.startPaymentSuccess(message: value.message);
      }
    });
    _subscriptionBloc.init();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Container _buildItemFeatureDes(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Expanded(
                flex: 0,
                child: Icon(
                  Icons.check,
                  color: AppColors.secondaryColor,
                  size: 18,
                ),
              ),
              MBlock(16),
              Expanded(
                flex: 1,
                child: CText.body(
                  title,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MScaffold(
      safeAreaTop: false,
      floatingActionButton: const BackFloatingAction(),
      floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    const SizedBox(
                      height: 30,
                    ),
                    CText.header2(S.of(context).subcribeProPackage),
                    const SizedBox(
                      height: 20,
                    ),
                    BackgroundRound1(
                      child: Column(
                        children: [
                          _buildItemFeatureDes(S.of(context).accessAllLesson),
                          MBlock(10),
                          const MDivider(),
                          MBlock(10),
                          _buildItemFeatureDes(S.of(context).accessAllFeature),
                          MBlock(10),
                          const MDivider(),
                          MBlock(10),
                          _buildItemFeatureDes(S.of(context).freeTrial7Days),
                          MBlock(10),
                          const MDivider(),
                          MBlock(10),
                          _buildItemFeatureDes(S.of(context).supportFree),
                          MBlock(10),
                          const MDivider(),
                          MBlock(10),
                          _buildItemFeatureDes(S.of(context).removeAds),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    StreamBuilder<GlobalConfig?>(
                      stream: injector<AccountBloc>().globalConfigController,
                      builder: (context, snap) {
                        if (snap.hasData) {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: <Widget>[
                              InkWell(
                                onTap: () => UrlLauncherService.launchURL(
                                  snap.data?.termsAndConditions ?? '',
                                ),
                                child: Text('${S.of(context).termsCondition} ',
                                    style:
                                        const TextStyle(color: Colors.white)),
                              ),
                              InkWell(
                                onTap: () => UrlLauncherService.launchURL(
                                  snap.data?.privacyPolicy ?? '',
                                ),
                                child: Text('${S.of(context).privacyPolicy} ',
                                    style:
                                        const TextStyle(color: Colors.white)),
                              ),
                            ],
                          );
                        } else {
                          return const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.whiteColor));
                        }
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    productsBuy(),
                    const SizedBox(
                      height: 10,
                    ),
                    BackgroundRound2(
                      margin: 40,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CText.body(
                              S.of(context).discountPrice,
                              textAlign: TextAlign.left,
                              color: AppColors.secondary2Color,
                            ),
                            MBlock(6),
                            CText.body(
                              S.of(context).discountPromo,
                              textAlign: TextAlign.left,
                              // color: Color(0xFFACB5C5),
                            )
                          ],
                        ),
                      ),
                    ),
                    StreamBuilder<GlobalConfig?>(
                      stream: injector<AccountBloc>().globalConfigController,
                      builder: (context, snap) {
                        if (snap.hasData &&
                            (snap.data?.isDisplayBankMethodPayment ?? false)) {
                          return Column(
                            children: <Widget>[
                              const SizedBox(
                                height: 10,
                              ),
                              BackgroundRound3(
                                margin: 40,
                                child: Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CText.body(
                                        S.of(context).transferMoney,
                                        textAlign: TextAlign.left,
                                        // color: Color(0xFFACB5C5),
                                      ),
                                      MBlock(6),
                                      SelectableText(
                                        injector<AppSettingUseCase>()
                                                    .getLanguage() ==
                                                'vi'
                                            ? snap.data?.transferMoneyInfoVi ??
                                                ''
                                            : snap.data?.transferMoneyInfoEn ??
                                                '',
                                        style: CText.body(
                                          '',
                                          textAlign: TextAlign.left,
                                          color: const Color(0xFFACB5C5),
                                        ).style,
                                      ),
                                      MBlock(6),
                                      MClickable.noPadding(
                                        onTap: () {
                                          UrlLauncherService.launchPhone(
                                              '0903848083');
                                        },
                                        radius: 0,
                                        child: CText.body(
                                          '${S.of(context).hotlineTitle}0903848083',
                                          textAlign: TextAlign.left,
                                          // color: Color(0xFFACB5C5),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          );
                        } else {
                          return const SizedBox();
                        }
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: CButton(
                        S.of(context).continueNext,
                        onTap: () async {
                          if (_subscriptionBloc.selectedProduct != null) {
                            await FirebaseAnalyticsWapper()
                                .analytics
                                .logEvent(name: 'Purchase', parameters: {
                              'action': 'continue',
                              'product_id':
                                  _subscriptionBloc.selectedProduct!.id,
                              'product_title':
                                  _subscriptionBloc.selectedProduct!.title,
                            });

                            if (injector<AccountBloc>().isUXCamInit) {
                              await FlutterUxcam.logEventWithProperties(
                                  'Purchase', {
                                'action': 'continue',
                                'product_id':
                                    _subscriptionBloc.selectedProduct?.id,
                                'product_title':
                                    _subscriptionBloc.selectedProduct?.title,
                              });
                            }
                          }
                          _subscriptionBloc.buyProduct();
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 60,
                      width: 250,
                      child: InkWell(
                        child: Card(
                            color: Colors.black,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25)),
                            child: Center(
                                child: CText.body(
                              S.of(context).restorePurchase,
                            ))),
                        onTap: () async {
                          var result =
                              await injector<AccountBloc>().restorePurchases();
                          if (!result) {
                            await showDialog(
                                context: context,
                                builder: (ctx) {
                                  return AlertDialog(
                                    content:
                                        Text(S.of(context).noPurchaseFound),
                                    title: Text(S.of(context).pastPurchase),
                                  );
                                });
                          }
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ]),
        ),
      ),
    );
  }

  StreamBuilder<List<ProductDetails?>> productsBuy() {
    return StreamBuilder<List<ProductDetails?>>(
      stream: _subscriptionBloc.productsController,
      builder: (context, snap) {
        if (snap.hasData) {
          return (snap.data?.length ?? 0) > 0
              ? Container(
                  padding: const EdgeInsets.all(4),
                  margin: const EdgeInsets.symmetric(horizontal: 40),
                  // height: 52.0,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                    gradient: const LinearGradient(
                      begin: Alignment(0.14, -2.15),
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF414141), Colors.black],
                    ),
                    border: Border.all(
                      width: 1.0,
                      color: const Color(0xFF3E434E),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: snap.data!.map((product) {
                      return productListItem(
                        context: context,
                        product: product,
                        interval: Platform.isIOS
                            ? _subscriptionBloc.getInterval(product)
                            : _subscriptionBloc.getIntervalAndroid(product),
                        price: product?.price,
                        onTap: () {
                          _subscriptionBloc.selectProduct(product);
                        },
                      );
                    }).toList(),
                  ),
                )
              : Center(child: CText.body(S.of(context).noProductFound));
        } else {
          return const Center(
            child: CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppColors.primaryColor)),
          );
        }
      },
    );
  }

  String getPriceDisplay(ProductDetails? product) {
    if (product?.currencyCode == 'VND') {
      return '${getPriceK(product?.rawPrice ?? 0)} ${product?.currencyCode}';
    } else {
      return '${product?.currencyCode} ${getPriceK(product?.rawPrice ?? 0)}';
    }
  }

  String getPriceK(double price) {
    return StringUtils.convertToThoundsandK(price);
  }

  Widget productListItem({
    required BuildContext context,
    String? intervalCount,
    String? interval,
    Function()? onTap,
    ProductDetails? product,
    String? price,
  }) {
    var isSelected = _subscriptionBloc.isSelectedProduct(product);
    return Expanded(
      flex: 1,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: isSelected
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(5.0),
                  gradient: const LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment(-0.92, 0.92),
                    colors: [Color(0xFFEAF818), Color(0xFFF6FC9C)],
                  ),
                  boxShadow: [
                    const BoxShadow(
                      color: Color(0xFF1A1C1F),
                      offset: Offset(1.0, 1.0),
                      blurRadius: 8.0,
                    ),
                  ],
                )
              : null,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              CText.body(
                '${getPriceDisplay(ProductDetails(
                  id: '',
                  title: '',
                  description: '',
                  price: product?.price ?? '',
                  rawPrice: ((product?.rawPrice ?? 0) / 62) * 100,
                  currencyCode: product?.currencyCode ?? '',
                ))} /${interval ?? ''}',
                textAlign: TextAlign.center,
                color: isSelected ? Colors.black : const Color(0xFFACB5C5),
                textDecoration: TextDecoration.lineThrough,
              ),
              MBlock(4),
              CText.body(
                '${getPriceDisplay(product)} /${interval ?? ''}',
                textAlign: TextAlign.center,
                color: isSelected ? Colors.black : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
