import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

import '../../core/widgets/common/will_pop_scope.dart';

class PaymentSuccessScreen extends StatefulWidget {
  static const String routeName = '/payment-success-screen';

  final String? message;

  const PaymentSuccessScreen({Key? key, this.message}) : super(key: key);

  @override
  _PaymentSuccessScreenState createState() => _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends BaseStateScreen<PaymentSuccessScreen> {
  @override
  Widget build(BuildContext context) {
    return WillPopScopeWidget(
      onWillPop: _onWillPop,
      child: MScaffold(
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.check,
                color: AppColors.secondary2Color,
                size: 120,
              ),
              MBlock(60),
              CText.header2(S.of(context).successfullyPurchased),
              MBlock(30),
              CText.title2(
                widget.message ?? '',
                textAlign: TextAlign.center,
              ),
              MBlock(60),
              CButton(
                S.of(context).learnMeditationNow,
                onTap: () {
                  _onWillPop();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    context.startDashboardAndRemoveUntil();
    return false;
  }
}
