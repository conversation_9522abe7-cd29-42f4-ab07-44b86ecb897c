import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/lib_common.dart';

import '../../../core/widgets/common/premium_tag_widget.dart';
import '../../../di/injection/injection.dart';
import '../../shared/account/account_bloc.dart';
import '../../shared/categories/categories_model.dart';

class AdvancedMeditaionPage extends StatefulWidget {
  const AdvancedMeditaionPage({
    super.key,
    required this.scrollController,
    required this.listCourses,
  });

  final ScrollController scrollController;
  final List<CourseMeditation> listCourses;

  @override
  State<AdvancedMeditaionPage> createState() => _AdvancedMeditaionPageState();
}

class _AdvancedMeditaionPageState extends State<AdvancedMeditaionPage> {
  CategoriesModel get categoriesModel => context.read<CategoriesModel>();
  @override
  Widget build(BuildContext context) {
    final isPaid = injector<AccountBloc>().isUnLock;

    return MScaffoldPage(
      backgroundImage: ImageConstants.backgroundAdvancedMedication,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: CustomScrollView(
          controller: widget.scrollController,
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.only(
                    top: MediaQuery.sizeOf(context).height * 0.3),
                child: const Center(
                  child: Text(
                    'Thiền nâng cao',
                    style: TextStyle(
                      fontSize: 35,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                ).copyWith(top: 8, bottom: 30),
                child: const Center(
                  child: Text(
                    'Khám phá sâu hơn về tâm hồn qua lộ trình thiền nâng cao',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w400,
                      color: AppColors.subTitleColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            if (isPaid == false)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 30),
                  child: Center(
                    child: PremiumTagWidget(
                      isPaid: isPaid,
                    ),
                  ),
                ),
              ),
            SliverPadding(
              padding: const EdgeInsets.only(bottom: 16),
              sliver: SliverList.separated(
                itemCount: widget.listCourses.length,
                itemBuilder: (context, index) {
                  final course = widget.listCourses[index];
                  final categoryData =
                      categoriesModel.getCategoryByCourse(course);
                  final hasImage =
                      categoryData?.coverImage?.isNotEmpty ?? false;
                  final isPremium = course.isPaid ?? false;

                  return CardSharingDayWidget(
                    isPremium: isPremium,
                    isPaid: isPaid,
                    countLessonText: categoryData?.audios?.length != null
                        ? '${categoryData!.audios!.length} bài thiền'
                        : null,
                    imageUrl: hasImage
                        ? categoryData!.coverImage!.mediaLinkFirebase
                        : 'https://i.ibb.co/1dH1Mfv/image.png',
                    description: categoryData?.name ?? '',
                    onTap: () {
                      context.startDetailCourse(
                        course,
                        category: categoryData,
                        isPremium: isPremium,
                      );
                    },
                  );
                },
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
