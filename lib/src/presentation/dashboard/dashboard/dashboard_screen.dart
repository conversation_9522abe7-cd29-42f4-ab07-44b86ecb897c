import 'dart:async';
import 'dart:io';

import 'package:app_developer/app_developer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/image_widget.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/bottom_bar_item.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/dashboard_old/dashboard_bloc.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_widgets.dart';
import 'package:new_version_plus/new_version_plus.dart';
import 'package:provider/provider.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

import '../../../core/config.dart';
import '../../../core/constants.dart';
import '../../../core/utils/in_app_update_for_android.dart';
import '../../../core/widgets/common/will_pop_scope.dart';
import '../../../core/widgets/disable_color_widget.dart';
import '../../../domain/usecase/medita_usecase.dart';

class DashboardScreen extends StatefulWidget {
  static const String routeName = '/dashboard';

  static MultiProvider newInstance() => MultiProvider(
        providers: [
          Provider<DashboardBloc>(
            create: (_) => DashboardBloc(),
            dispose: (_, _bloc) => _bloc.dispose(),
          ),
        ],
        child: DashboardScreen(),
      );

  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends BaseStateScreen<DashboardScreen> {
  late List<Widget> widgets;
  late DashboardBloc _dashboardBloc;
  late AccountBloc accountBloc;

  void _onTapNaviagtionItem(int value) {
    var navigationBloc = Provider.of<NavigationBloc>(context, listen: false);
    final navigationItems = navigationBloc.navigationSate.navigationItems;

    if (navigationBloc.indexCurrent == value) {
      if (navigationItems[value].scrollController.hasClients) {
        navigationItems[value].scrollController.animateTo(0,
            duration: const Duration(milliseconds: 400), curve: Curves.linear);
      }
    } else {
      navigationBloc.onUserChangeTab(navigationItems[value].layout);
    }
  }

  @override
  void initState() {
    super.initState();
    accountBloc = Provider.of<AccountBloc>(context, listen: false);
    accountBloc.setView(handleUIBloc);
    accountBloc.handleUIPaymentBloc.uiHandleController.listen((value) async {
      var context = AppBloc.navigatorKey.currentState!.context;
      if (value.isError) {
        await Alert(
          context: context,
          type: AlertType.error,
          title: S.of(context).failed,
          desc: value.message ?? '',
          buttons: [
            DialogButton(
              onPressed: () => context.pop(),
              width: 120,
              child: Text(
                S.of(context).tryAgain,
                style: const TextStyle(color: Colors.white, fontSize: 20),
              ),
            )
          ],
        ).show();
      }

      if (value.isSucess) {
        await context.startPaymentSuccess(message: value.message);
        // popup charge tiền thành công và bay qua home
      }
    });

    _dashboardBloc = Provider.of<DashboardBloc>(context, listen: false);
    _dashboardBloc
      ..init()
      ..setView(handleUIBloc);

    var navigationBloc = Provider.of<NavigationBloc>(context, listen: false)
      ..onUserChangeTab(BottomBarItemType.home);

    widgets = navigationBloc.navigationSate.navigationItems
        .map((item) => MainPage(
            keepAlive: true,
            child:
                item.layout.build(context, item.scrollController, item.data)))
        .toList();
    if (mounted) {
      fetchDatas();
    }
    // In App Update For Android will have higher priority than New Version
    checkNewVersion();
  }

  Future<void> checkNewVersion() async {
    // In App Update For Android will have higher priority than New Version
    if (isAndroid && kAdvanceConfig.inAppUpdateForAndroid.enable) {
      await InAppUpdateForAndroid().checkForUpdate();
    } else if (kAdvanceConfig.versionCheck.enable) {
      final versionCheck = kAdvanceConfig.versionCheck;
      final newVersionPlus = NewVersionPlus(
        // iOSId: versionCheck.iosId,
        iOSAppStoreCountry: versionCheck.iOSAppStoreCountry,
        // androidId: versionCheck.androidId,
        androidPlayStoreCountry: versionCheck.androidPlayStoreCountry,
      );
      final status = await newVersionPlus.getVersionStatus();
      if (status == null) return;
      newVersionPlus.showUpdateDialog(
        context: context,
        versionStatus: status,
        dialogTitle: 'Thông báo cập nhật',
        dialogText: 'Hiện tại có phiên bản mới, bạn có muốn cập nhật không?',
        updateButtonText: 'Cập nhật',
        dismissButtonText: 'Để sau',
      );
    }
  }

  Future<void> fetchDatas() async {
    await accountBloc.getGlobalConfig();
    await accountBloc.initializePayment();
    await injector<AccountBloc>().getUser();
    await injector<AccountBloc>().getFullInfoUser();
    injector<AccountBloc>().regisListener();
    unawaited(injector<MeditaUsecase>()
        .pushLastLoginInfo(injector<AccountBloc>().auth.currentUser));
  }

  @override
  Widget build(BuildContext context) {
    var navigationBloc = Provider.of<NavigationBloc>(context);
    final navigationItems = navigationBloc.navigationSate.navigationItems;

    return WillPopScopeWidget(
      onWillPop: _onWillPop,
      child: MScaffold(
        safeAreaTop: false,
        body: NavigationPageChooser(widgets: widgets),
        bottomNavigationBar: StreamBuilder<NavigationSate>(
          stream: navigationBloc.navigationController.stream,
          initialData: navigationBloc.navigationSate,
          builder: (BuildContext context, snapshot) {
            final itemSelected =
                snapshot.data?.moduleIdSelected ?? BottomBarItemType.home;
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: const Color(0xff495465),
                  height: 0.9,
                  width: double.infinity,
                ),
                BottomNavigationBar(
                  backgroundColor: Colors.black,
                  currentIndex: navigationBloc.getIndex(itemSelected),
                  showUnselectedLabels: true,
                  selectedItemColor: const Color(0xffECF939),
                  selectedLabelStyle: const TextStyle(color: Color(0xffECF939)),
                  selectedFontSize: 12,
                  unselectedFontSize: 12,
                  unselectedItemColor: Colors.white,
                  type: BottomNavigationBarType.fixed,
                  onTap: _onTapNaviagtionItem,
                  items: navigationItems.map((e) {
                    final isSelected = itemSelected == e.layout;
                    final isFillColor = e.icon!.contains('svg');

                    return BottomNavigationBarItem(
                      backgroundColor: Colors.black,
                      icon: DisableColorWidget(
                        disable: isSelected == false,
                        child: ImageWidget(
                          e.icon!,
                          height: 25,
                          width: 25,
                          color: isSelected && isFillColor
                              ? const Color(0xffECF939)
                              : null,
                        ),
                      ),
                      label: e.layout.title(context),
                    );
                  }).toList(),
                ),
              ],
            );
          },
        ),
        floatingActionButton: kDebugMode ? const DeveloperButton() : null,
      ),
    );
  }

  Future<bool> _onWillPop() async {
    var res = await context.startDialogConfirmExitApp();

    /// Because Flutter does not exit app, just make the app goes to background
    Future.delayed(const Duration(milliseconds: 250), () {
      exit(0);
    });
    return res ?? false;
  }
}
