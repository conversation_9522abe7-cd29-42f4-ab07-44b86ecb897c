import 'package:auto_size_text/auto_size_text.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_model.dart';
import 'package:shimmer/shimmer.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../../app_delegate.dart';
import '../../../core/utils.dart';
import '../../../core/widgets/dynamic_layout/dynamic_layout_widget.dart';
import '../../shared/app_bloc/app_bloc.dart';
import '../../shared/categories/categories_model.dart';
import '../../shared/daily_inspiration/daily_inspiration_model.dart';

class HomePage extends StatefulWidget {
  HomePage({
    super.key,
    required this.scrollController,
  });

  final ScrollController scrollController;
  @override
  _HomeState createState() => _HomeState();
}

class _HomeState extends BaseStateScreen<HomePage> with WidgetsBindingObserver {
  @override
  void onResume() {
    if (UniversalPlatform.isIOS) {
      audioHandler?.resumeApplication();
    }
  }

  FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  AppBloc get appBloc => context.read<AppBloc>();
  ConfigModel get configModel => appBloc.appModel.appConfig;
  List featuredStoryList = [];

  Future<void> _onRefresh() async {
    final future = [
      context.read<CategoriesModel>().getCategories(),
      context.read<DailyInspirationModel>().loadData(),
    ];

    await Future.wait(future);
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<DailyInspirationModel>().loadData(
        loadDone: () async {
          if (mounted) {
            setState(() {});
          }
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget body = CustomScrollView(
      controller: widget.scrollController,
      slivers: [
        if (UniversalPlatform.isIOS)
          CupertinoSliverRefreshControl(
            onRefresh: _onRefresh,
          ),
        ...List.generate(
          configModel.horizonLayout?.length ?? 0,
          (index) {
            final item = configModel.horizonLayout![index];

            return SliverDynamicLayoutWidget(
              configs: Map<String, dynamic>.from(item),
            );
          },
        ),
      ],
    );

    if (UniversalPlatform.isAndroid) {
      body = RefreshIndicator(
        onRefresh: _onRefresh,
        child: body,
      );
    }

    return MScaffoldPage(body: body);
  }

  Container moreInfo() {
    return Container(
      width: 30.0,
      height: 30.0,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7.0),
        gradient: const LinearGradient(
          begin: Alignment(0.85, -1.11),
          end: Alignment.bottomCenter,
          colors: [Color(0xFF414141), Color(0xFF0D0D0D)],
        ),
        border: Border.all(
          width: 1.0,
          color: const Color(0xFF707070),
        ),
      ),
      child: const Center(
        child: Text(
          '?',
          style: TextStyle(
            fontSize: 21.0,
            color: Colors.white,
            letterSpacing: 1.5000000457763671,
            height: 0.95,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Positioned appBarWidget() {
    return Positioned(
      top: -10,
      height: 270,
      width: MediaQuery.sizeOf(context).width,
      child: Container(
        // color: AppColors.primaryColor,
        color: Colors.transparent,
        child: const Padding(
          padding: EdgeInsets.all(20.0),
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[]),
        ),
      ),
    );
  }

  InkWell buildGuidedMeditation() {
    return InkWell(
      onTap: () {
        context.startHtmlDetail(
            content: injector<AccountBloc>().globalConfig?.bannerLink);
      },
      child: Container(
          // height: MediaQuery.sizeOf(context).height * .28
          height: 200,
          width: MediaQuery.sizeOf(context).width - 40,
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
          decoration: BoxDecoration(
              image: const DecorationImage(
                  image: AssetImage('assets/img/bg/banner.png'),
                  alignment: Alignment.center,
                  fit: BoxFit.cover),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                const BoxShadow(
                  color: Color.fromRGBO(12, 12, 12, .3),
                  blurRadius: 10,
                  offset: Offset(0, 10),
                )
              ]),
          child: Container()),
    );
  }

  Widget buildmeditationStep() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CText.title1(
                  S
                      .of(context)
                      .featuredStories
                      .replaceAll('Thiền Thức Tỉnh', ''),
                  fontWeight: FontWeight.normal,
                  fontSize: 22,
                ),
                CText.title1(
                  'Thiền Thức Tỉnh',
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Container(
            height: 185,
            child: AnimatedSwitcher(
              duration: const Duration(seconds: 2),
              child: featuredStoryList.isNotEmpty
                  ? ListView.builder(
                      itemCount: featuredStoryList.length,
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      itemBuilder: (context, index) {
                        return _storyItem(featuredStoryList[index]);
                      },
                    )
                  : SizedBox(
                      height: 180.0,
                      child: Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: AppColors.darkPrimaryColor,
                          child: Container()),
                    ),
            ),
          )
        ],
      ),
    );
  }

  InkWell _storyItem(dynamic fsl) {
    var imageUrl = fsl['coverImage']?.toString().mediaLinkFirebase;
    return InkWell(
      onTap: () {
        FirebaseAnalyticsWapper().analytics.logEvent(
            name: 'Feature_story', parameters: {'storyItems': fsl['name']});
        if (injector<AccountBloc>().isUXCamInit) {
          FlutterUxcam.logEventWithProperties(
              'Feature_story', {'storyItems': fsl['name']});
        }

        context.startStories(fsl['storyItems']);
      },
      child: Container(
        margin: const EdgeInsets.only(left: 16, bottom: 5),
        alignment: Alignment.bottomLeft,
        width: 152.0,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          // image: DecorationImage(
          //   image: NetworkImage(imageUrl, scale: 152 / 180),
          //   fit: BoxFit.cover,
          // ),
          color: Colors.black,
          border: Border.all(
            width: 1.0,
            color: const Color(0xFF707070),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF626262).withOpacity(0.6),
              offset: const Offset(2.0, 2.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
                bottom: 0,
                top: 0,
                right: 0,
                left: 0,
                child: LayoutBuilder(builder: (context, constrains) {
                  return ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Tools.image(
                          url: imageUrl,
                          fit: BoxFit.cover,
                          width: constrains.maxWidth,
                          height: constrains.maxHeight));
                })),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                width: 152.0,
                height: 38.0,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20.0),
                    bottomRight: Radius.circular(16.0),
                    bottomLeft: Radius.circular(16.0),
                  ),
                  gradient: const LinearGradient(
                    begin: Alignment(0.0, -1.26),
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF414141), Colors.black],
                  ),
                  border: Border.all(
                    width: 0.1,
                    color: const Color(0xFF707070),
                  ),
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      flex: 1,
                      child: AutoSizeText(
                        fsl['name'] ?? '',
                        // 'margin: const EdgeInsets.symmetric(horizontal: 6),',
                        // overflow: TextOverflow.ellipsis,
                        style: CText.title2(
                          '',
                          // fontWeight: FontWeight.w700
                          fontWeight: FontWeight.w500,
                        ).style,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
