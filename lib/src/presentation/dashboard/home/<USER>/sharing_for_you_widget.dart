import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/header_widget.dart';

import '../../../../core/widgets/common/page_dynamic_widget.dart';
import '../../../../core/widgets/common/tab_bar_widget.dart';
import '../../../../domain/entities/category_data.dart';
import '../../../../domain/entities/dynamic_config_basic.dart';
import '../../../shared/categories/categories_model.dart';

class SharingForYouWidget extends StatefulWidget {
  const SharingForYouWidget({
    super.key,
    required this.infosShared,
    this.configBasic,
  });
  final List<SharingForYour> infosShared;
  final DynamicConfigBasic? configBasic;

  @override
  State<SharingForYouWidget> createState() => _SharingForYouWidgetState();
}

class _SharingForYouWidgetState extends State<SharingForYouWidget> {
  final _pageController = PageController(initialPage: 0);
  CategoriesModel get categoriesModel => context.read<CategoriesModel>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 15),
            child: HeaderWidget(
              text: 'Dành thêm cho bạn',
              maxLines: 4,
            ),
          ),
          const SizedBox(height: 10),
          Container(
            height: 70,
            child: TabbarAutoScrollWidget(
              controller: _pageController,
              paddingContent: widget.configBasic?.margin?.toEdgeInsets(),
              useScroll: true,
              items: List.generate(
                widget.infosShared.length,
                (index) {
                  final item = widget.infosShared[index];
                  return ItemTabBar(item.title, item.icon);
                },
              ),
            ),
          ),
          AnimatedBuilder(
              animation: categoriesModel,
              builder: (_, __) {
                return PageDynamicWidget(
                  pageController: _pageController,
                  children: List.generate(
                    widget.infosShared.length,
                    (index) {
                      final item = widget.infosShared[index];
                      final courses =
                          item.getCourses(categoriesModel.categories);

                      if (categoriesModel.isLoading) {
                        return Center(
                          child: kLoadingWidget(context),
                        );
                      }

                      if (courses.isEmpty) {
                        return const Center(
                          child: Text(
                            'Chưa có bài viết mới.',
                          ),
                        );
                      }

                      return Padding(
                        padding: widget.configBasic?.margin?.toEdgeInsets() ??
                            EdgeInsets.zero,
                        child: Column(
                          children: List.generate(
                            courses.length,
                            (indexPost) {
                              final course = courses[indexPost];
                              CategoryData category =
                                  categoriesModel.getCategoryByCourse(course)!;

                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 10),
                                child: CardSharingDayWidget(
                                  isPremium: false,
                                  countLessonText:
                                      category.audios?.length != null
                                          ? '${category.audios!.length} bài'
                                          : null,
                                  imageUrl:
                                      category.coverImage?.mediaLinkFirebase ??
                                          '',
                                  description: category.name,
                                  onTap: () => context.startDetailCourse(course,
                                      category: category),
                                ),
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),
                );
              })
        ],
      ),
    );
  }
}
