import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/presentation/shared/categories/categories_model.dart';

import '../../core/widgets/common/background_layout_widget.dart';
import '../../di/injection/injection.dart';
import '../shared/app_bloc/loading_bloc.dart';

class DetailCategoryMeditaionScreen extends StatefulWidget {
  static const String routeName = '/detail-category-meditation';

  const DetailCategoryMeditaionScreen({super.key, required this.category});

  final CategoryMeditation category;

  @override
  State<DetailCategoryMeditaionScreen> createState() =>
      _DetailCategoryMeditaionScreenState();
}

class _DetailCategoryMeditaionScreenState
    extends State<DetailCategoryMeditaionScreen> {
  CategoryMeditation get category => widget.category;
  CategoriesModel get categoriesModel => context.read<CategoriesModel>();
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();
    if (categoriesModel.categories.isEmpty) {
      _isLoading = true;
      injector<LoadingBloc>().loading();
    }
  }

  @override
  void dispose() {
    injector<LoadingBloc>().loaded();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isLoading = categoriesModel.categories.isEmpty;

    return BackgroundLayoutWidget(
      backgroundImage: category.urlBackground,
      useLoading: true,
      body: AnimatedBuilder(
          animation: categoriesModel,
          builder: (_, __) {
            if (categoriesModel.categories.isNotEmpty && _isLoading) {
              _isLoading = false;
              injector<LoadingBloc>().loaded();
            }

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.only(
                        top: MediaQuery.sizeOf(context).height * 0.15,
                      ),
                      child: Center(
                        child: Text(
                          category.title,
                          style: const TextStyle(
                            fontSize: 30,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (category.description?.isNotEmpty ?? false)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 3,
                        ).copyWith(top: 8, bottom: 40),
                        child: Center(
                          child: Text(
                            category.description!,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              color: AppColors.subTitleColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    )
                  else
                    const SliverToBoxAdapter(
                      child: SizedBox(height: 40),
                    ),
                  if ((category.courses?.isNotEmpty ?? false) &&
                      isLoading == false)
                    SliverList.separated(
                      itemCount: category.courses!.length,
                      itemBuilder: (context, index) {
                        final course = category.courses![index];
                        final categoryData =
                            (course.categoryId?.isNotEmpty ?? false)
                                ? categoriesModel.getCategoryByCourse(course)
                                : null;
                        final isPaid =
                            course.isPaid ?? categoryData?.isPaid ?? false;

                        final countLesson = course.lessons.isEmpty
                            ? (categoryData?.audios?.length ?? 0)
                            : course.lessons.length;

                        return CardSharingDayWidget(
                          imageUrl: course.backgroundImage,
                          description: course.title ?? categoryData?.name ?? '',
                          isPremium: false,
                          isPaid: isPaid,
                          alignment: course.alignment,
                          countLessonText: countLesson != 0
                              ? '$countLesson bài thiền'
                              : null,
                          onTap: () {
                            context.startDetailCourse(
                              course,
                              category: categoryData,
                            );
                          },
                        );
                      },
                      separatorBuilder: (context, index) => const SizedBox(
                        height: 16,
                      ),
                    )
                  else if (isLoading)
                    SliverToBoxAdapter(child: kLoadingWidget(context)),
                ],
              ),
            );
          }),
    );
  }
}
