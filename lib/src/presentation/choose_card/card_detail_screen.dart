import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/constants.dart';
import 'package:meditaguru/src/presentation/shared/daily_inspiration/daily_inspiration_model.dart';

import '../../core/services/firebase/analytics/firebase_analytics_wapper.dart';
import '../animation/animation_flip.dart';

class CardDetailScreen extends StatefulWidget {
  const CardDetailScreen({
    super.key,
    required this.heroTag,
  });

  final String heroTag;

  static const String routeName = '/card-detail-screen';

  @override
  State<CardDetailScreen> createState() => _CardDetailScreenState();
}

class _CardDetailScreenState extends State<CardDetailScreen>
    with SingleTickerProviderStateMixin {
  late (DailyInspirationData, ImageProvider) _infoCard;

  late final AnimationController _controller;
  late final Animation<double> _scaleAnimation;
  late final Animation<Offset> _slideImageAnimation;
  late final Animation<double> _flipAnimation;
  late final Animation<double> _textOpacityAnimation;
  late final Animation<Offset> _slideTextAnimation;
  late final Animation<Offset> _slideButtonAnimation;

  @override
  void initState() {
    super.initState();
    _infoCard = context.read<DailyInspirationModel>().getCard();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    final beginScale = 0.7;
    final transitionScale = 1.6;
    final endScale = 1.2;
    _scaleAnimation = TweenSequence([
      TweenSequenceItem(
        tween: Tween<double>(begin: beginScale, end: transitionScale),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: ConstantTween<double>(transitionScale),
        weight: 20,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: transitionScale, end: endScale),
        weight: 40,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    final beginOffset = const Offset(0, 0);
    final transitionOffset = const Offset(0, 0.17);
    final endOffset = const Offset(0, -0.5);
    _slideImageAnimation = TweenSequence([
      TweenSequenceItem(
        tween: Tween<Offset>(begin: beginOffset, end: transitionOffset),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: ConstantTween<Offset>(transitionOffset),
        weight: 20,
      ),
      TweenSequenceItem(
        tween: Tween<Offset>(begin: transitionOffset, end: endOffset),
        weight: 40,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _flipAnimation = Tween<double>(begin: 0.0, end: 0.1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.4, curve: Curves.easeOut),
      ),
    );

    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.6, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideTextAnimation =
        Tween<Offset>(begin: const Offset(0, -0.35), end: const Offset(0, 0.65))
            .animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 0.8, curve: Curves.easeInOutBack),
      ),
    );

    _slideButtonAnimation =
        Tween<Offset>(begin: const Offset(0, 0.6), end: const Offset(0, 0))
            .animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1.0, curve: Curves.easeInOutBack),
      ),
    );

    Future.delayed(const Duration(milliseconds: 500), () {
      _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: AppColors.kDarkBG,
        body: Stack(
          fit: StackFit.expand,
          alignment: Alignment.center,
          children: [
            Center(
              child: SlideTransition(
                position: _slideTextAnimation,
                child: FadeTransition(
                  opacity: _textOpacityAnimation,
                  child: CardContentHomeWidget(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 36,
                    ).copyWith(top: 40),
                    card: _infoCard.$1,
                  ),
                ),
              ),
            ),
            Center(
              child: Hero(
                tag: widget.heroTag,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: SlideTransition(
                    position: _slideImageAnimation,
                    child: SizedBox(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: AnimatedBuilder(
                          animation: _flipAnimation,
                          builder: (BuildContext context, Widget? child) {
                            return SizedBox(
                              child: AnimationFlip(
                                showFront: _flipAnimation.value == 0.0,
                                duration: const Duration(milliseconds: 200),
                                frontWidget: const SizedBox(
                                  height: 300,
                                  child: ImageWidget(
                                    ImageConstants.card,
                                  ),
                                ),
                                rearWidget: SizedBox(
                                  height: 300,
                                  child: AspectRatio(
                                    aspectRatio: 912 / 1212,
                                    child: ExtendedImage(
                                      image: _infoCard.$2,
                                      fit: BoxFit.contain,
                                      handleLoadingProgress: true,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            SafeArea(
              child: SlideTransition(
                position: _slideButtonAnimation,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: SizedBox(
                      width: 250,
                      height: 48,
                      child: ButtonGradiantWidget.secondary02(
                        title: 'Lấy thông điệp',
                        onPressed: () {
                          FirebaseAnalyticsWapper().analytics.logEvent(
                              name: TrackingEvent.dailyMessages,
                              parameters: {
                                TrackingParameter.action: 'take_messages',
                                TrackingParameter.userTime:
                                    DateTime.now().toString(),
                              });
                          context
                              .read<DailyInspirationModel>()
                              .selectCard(_infoCard.$1);
                          context.startDashboardAndRemoveUntil();
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const Positioned(
              bottom: 10,
              left: 10,
              child: ImageWidget(IconConstants.moon02),
            ),
            const Positioned(
              right: 10,
              bottom: -5,
              child: ImageWidget(IconConstants.lotus03),
            ),
          ],
        ),
      ),
    );
  }
}
