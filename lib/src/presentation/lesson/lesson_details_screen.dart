import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/back_floating_action.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/lesson/lesson_details_bloc.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:provider/provider.dart';

import '../../core/utils.dart';

class LessonDetailsScreen extends StatefulWidget {
  static const String routeName = '/lessons-screen';

  final String? id;
  final String? coverImage;
  final String? name;
  final String? description;

  LessonDetailsScreen({
    this.id,
    this.coverImage,
    this.name,
    this.description,
  });

  @override
  _LessonDetailsScreenState createState() => _LessonDetailsScreenState();

  static Provider<LessonDetailsBloc> newInstance(
          {String? id, String? coverImage, String? name, String? desc}) =>
      Provider<LessonDetailsBloc>(
        create: (_) => LessonDetailsBloc(injector.get()),
        dispose: (_, _bloc) => _bloc.dispose(),
        child: LessonDetailsScreen(
          id: id,
          coverImage: coverImage,
          name: name,
          description: desc,
        ),
      );
}

class _LessonDetailsScreenState extends State<LessonDetailsScreen> {
  final PageController ctrl = PageController(viewportFraction: 0.8);
  int currentPage = 0;
  bool isExpired = true;
  bool isTrial = true;
  late LessonDetailsBloc lessonDetailsBloc;

  @override
  void initState() {
    lessonDetailsBloc = Provider.of<LessonDetailsBloc>(context, listen: false);
    lessonDetailsBloc.id = widget.id;
    super.initState();
    if (mounted) {
      lessonDetailsBloc.getAudioDetailsAndCheckExpiry();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MScaffold(
      floatingActionButton: BackFloatingAction(
        onPressed: () {
          context.pop();
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
      body: Column(
        children: [
          Hero(
            tag: widget.id ?? '',
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: Column(
                children: [
                  const SizedBox(
                    height: 50,
                  ),
                  CText.header2('${widget.name}'),
                  const SizedBox(
                    height: 4,
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: StreamBuilder<List>(
              stream: lessonDetailsBloc.detailsController,
              builder: (context, snap) {
                if (snap.hasData) {
                  var audioLessonDetailsList = snap.data;
                  if ((audioLessonDetailsList?.length ?? 0) == 0) {
                    return SingleChildScrollView(
                      child: Column(
                        children: [
                          const SizedBox(
                            height: 16,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            margin: const EdgeInsets.only(bottom: 15),
                            width: double.infinity,
                            child: CText.body(
                              widget.description ?? '',
                              fontWeight: FontWeight.normal,
                              textAlign: TextAlign.center,
                              fontSize: AppFontSize.normalFontSize2,
                            ),
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                          SizedBox(
                            width: 240,
                            height: 240,
                            child: Stack(
                              children: [
                                Container(
                                  // padding: EdgeInsets.all(10),
                                  width: double.infinity,
                                  child: Image.asset(
                                    'assets/img/round_logo.png',
                                    // height: 270,
                                    // width: 270,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 30,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            margin: const EdgeInsets.only(bottom: 15),
                            width: double.infinity,
                            child: CText.body(
                              S.of(context).comingSoon,
                              fontWeight: FontWeight.normal,
                              textAlign: TextAlign.center,
                              fontSize: 21,
                            ),
                          )
                        ],
                      ),
                    );
                  }
                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    physics: const BouncingScrollPhysics(),
                    itemCount: (audioLessonDetailsList?.length ?? 0) + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        if (StringUtils.isEmpty(widget.description)) {
                          return const SizedBox();
                        } else {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            margin: const EdgeInsets.only(bottom: 15),
                            width: double.infinity,
                            child: CText.body(
                              widget.description ?? '',
                              fontWeight: FontWeight.normal,
                              textAlign: TextAlign.center,
                              fontSize: AppFontSize.normalFontSize2,
                            ),
                          );
                        }
                      }
                      return LessonItemWidget(
                        data: audioLessonDetailsList?[index - 1],
                      );
                    },
                  );
                } else {
                  return const SizedBox();
                }
              },
            ),
          )
        ],
      ),
    );
  }
}

class LessonItemWidget extends StatefulWidget {
  final dynamic data;

  const LessonItemWidget({Key? key, this.data}) : super(key: key);

  @override
  _LessonItemWidgetState createState() => _LessonItemWidgetState();
}

class _LessonItemWidgetState extends State<LessonItemWidget> {
  String? audioCoverImage;

  @override
  void initState() {
    super.initState();
    // audioCoverImage = widget.data['audioCoverImageUrl'];
    widget.data['audioCoverImage'][0].get().then((documentSnapshot) {
      setState(() {
        audioCoverImage =
            documentSnapshot.data()['file']?.toString().mediaLinkFirebase;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final data = widget.data;
    var isLocked = true;
    print(data);
    if (!data['isPaid']) {
      //for free content
      isLocked = false;
    } else {
      //fist check if user has buy something
      if (injector<AccountBloc>().isUnLock) {
        isLocked = false;
      }
    }
    //= 170 / 382 = h / w
    return InkWell(
      onTap: () {
        onSongTap(isLocked, data);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 15),
        child: LayoutBuilder(
          builder: (context, constraints) {
            var height = (170 * constraints.maxWidth) / 382;
            return SizedBox(
              width: double.infinity,
              height: height,
              child: Stack(
                children: <Widget>[
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: audioCoverImage != null
                        ? BoxDecoration(
                            borderRadius: BorderRadius.circular(18.0),
                            image: DecorationImage(
                              image: NetworkImage(audioCoverImage ?? ''),
                              fit: BoxFit.cover,
                            ),
                            border: Border.all(
                              width: 1.0,
                              color: const Color(0xFF69707D).withOpacity(0.64),
                            ),
                          )
                        : BoxDecoration(
                            borderRadius: BorderRadius.circular(18),
                            boxShadow: [const BoxShadow(color: Colors.black26)],
                          ),
                  ),
                  Container(
                    alignment: Alignment.center,
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(18.0),
                      gradient: LinearGradient(
                        begin: const Alignment(-0.27, 0.86),
                        end: const Alignment(-0.07, -0.49),
                        colors: [
                          Colors.black.withOpacity(0.8),
                          const Color(0xFF414141).withOpacity(0.0)
                        ],
                      ),
                      border: Border.all(
                        width: 1.0,
                        color: const Color(0xFF69707D).withOpacity(0.64),
                      ),
                    ),
                  ),
                  Align(
                    alignment: const Alignment(0.0, 0.68),
                    child: SizedBox(
                      width: double.infinity,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: <Widget>[
                                  AutoSizeText("${data['audioName'] ?? ''}",
                                      style: CText.title5(
                                        '',
                                        fontWeight: FontWeight.normal,
                                      ).style),
                                  MBlock(4),
                                  AutoSizeText(
                                      // 'Ut enim ad minim veniam, quis nostrud\nexercitation ultrices nulla quis nibh. Ut enim ad minim veniam, quis nostrud\nexercitation ultrices nulla quis nibh. Ut enim ad minim veniam, quis nostrud\nexercitation ultrices nulla quis nibh. Ut enim ad minim veniam, quis nostrud\nexercitation ultrices nulla quis nibh.',
                                      data['audioDescription'] ?? '',
                                      style: CText.body2(
                                        '',
                                        color: const Color(0xFFACB5C5),
                                      ).style),
                                ],
                              ),
                            ),
                            MBlock(4),
                            Expanded(
                              flex: 0,
                              child: Container(
                                alignment: const Alignment(-0.03, 0.01),
                                width: 36.0,
                                height: 36.0,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: const LinearGradient(
                                    begin: Alignment(-0.64, -0.72),
                                    end: Alignment(0.65, 0.7),
                                    colors: [
                                      Color(0xFF2E3138),
                                      Color(0xFF1A1C1F)
                                    ],
                                  ),
                                  border: Border.all(
                                    width: 1.0,
                                    color: const Color(0xFF3E434E),
                                  ),
                                  boxShadow: [
                                    const BoxShadow(
                                      color: Color(0xFF3E434E),
                                      offset: Offset(-3.0, -2.0),
                                      blurRadius: 12.0,
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  isLocked
                                      ? FontAwesomeIcons.lock
                                      : Icons.arrow_forward_ios_rounded,
                                  size: 14,
                                  color: AppColors.secondary2Color,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void onSongTap(isLocked, data) async {
    if (isLocked) {
      await context.startPayment();
    } else {
      var audioTypes = [
        '.amr',
        '.ogg',
        '.m4a',
        '.3gp',
        '.aac',
        '.mp3',
        '.wav',
        '.flac'
      ];

      // lessonDetailsBloc.loadingBloc.loading();
      String fileName =
          await data['audioFile'][0].get().then((documentSnapshot) {
        return documentSnapshot.data()['file'];
      });
      // lessonDetailsBloc.loadingBloc.loaded();

      // String fileName = data['audioFile'];
      var urlFile = fileName.mediaLinkFirebase;
      var isAudio = false;

      if (StringUtils.isEmpty(fileName)) {
        urlFile = data['urlFile'] ?? '';
        isAudio = data['typeFile'] != 1; // 0 : audio - 1: video
      } else {
        audioTypes.forEach((element) async {
          if (fileName.contains(element)) {
            isAudio = true;
            return;
          }
        });
      }

      if (isAudio) {
        await context.startSongBackgroundScreen(
          url: urlFile,
          audioName: data['audioName'],
          description: data['audioDescription'],
          imageUrl: audioCoverImage,
        );
      } else {
        await context.startVideoScreen(
          url: urlFile,
          videoName: data['audioName'],
          description: data['audioDescription'],
          imageUrl: audioCoverImage,
        );
      }
      await FirebaseAnalyticsWapper()
          .analytics
          .logEvent(name: 'Lesson', parameters: {
        'audioName': data['audioName'],
      });
      if (injector<AccountBloc>().isUXCamInit) {
        await FlutterUxcam.logEventWithProperties('Lesson', {
          'audioName': data['audioName'],
        });
      }
    }
  }
}
