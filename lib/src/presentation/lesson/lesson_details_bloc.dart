import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:rxdart/rxdart.dart';

import '../../domain/usecase/lesson_usecase.dart';

class LessonDetailsBloc extends BaseBloc {
  late LoadingBloc loadingBloc;
  final LessonUsecase _lessonUsecase;

  BehaviorSubject<List> detailsController = BehaviorSubject<List>();
  List audioDetailsList = [];
  int trialDays = 0;
  String? id;

  LessonDetailsBloc(this._lessonUsecase) {
    loadingBloc = injector<LoadingBloc>();
  }

  @override
  void init() {}

  @override
  void dispose() {}

  Future<void> getAudioDetailsAndCheckExpiry() async {
    final listItems = await _lessonUsecase.getLessonById(id);
    audioDetailsList.addAll(listItems);
    detailsController.add(audioDetailsList);
    return;
  }
}
