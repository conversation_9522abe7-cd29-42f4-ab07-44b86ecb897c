import 'package:firebase_auth/firebase_auth.dart' as fireb;
import 'package:flutter/material.dart';
import 'package:meditaguru/src/domain/entities/category_meditation.dart';
import 'package:meditaguru/src/presentation/auth/forget/forgetPassword.dart';
import 'package:meditaguru/src/presentation/auth/login/login.dart';
import 'package:meditaguru/src/presentation/auth/signup/signup.dart';
import 'package:meditaguru/src/presentation/auth/signup/signup_social.dart';
import 'package:meditaguru/src/presentation/breathing_exercise/breathing_exercise/breathing_exercise_screen.dart';
import 'package:meditaguru/src/presentation/choose_card/choose_card_screen.dart';
import 'package:meditaguru/src/presentation/dashboard/dashboard/dashboard_screen.dart';
import 'package:meditaguru/src/presentation/detail_category_meditaion/detail_category_meditaion_screen.dart';
import 'package:meditaguru/src/presentation/detail_course/detail_course_screen.dart';
import 'package:meditaguru/src/presentation/get_messages/screen_get_messages.dart';
import 'package:meditaguru/src/presentation/html_detail/html_detail_screen.dart';
import 'package:meditaguru/src/presentation/payment/payment_success_screen.dart';
import 'package:meditaguru/src/presentation/payment/subscription_bloc.dart';
import 'package:meditaguru/src/presentation/stories/stories.dart';
import 'package:meditaguru/src/presentation/welcome/welcome_screen.dart';
import 'package:provider/provider.dart';

import '../../generated/l10n.dart';
import '../core/configurations/configurations.dart';
import '../core/services/email_sender_service.dart';
import '../core/widgets/common/alert_dialog/alert_dialog.dart';
import '../core/widgets/common/subscription_widget.dart';
import '../domain/entities/category_data.dart';
import 'auth/login/twitterLogin.dart';
import 'breathing_exercise/breathing_exercise_details/breathing_exercise_details_screen.dart';
import 'breathing_exercise/breathing_exercise_intro/breathing_exercise_intro_screen.dart';
import 'choose_card/card_detail_screen.dart';
import 'dashboard_old/dashboard_screen.dart';
import 'lesson/lesson_details_screen.dart';
import 'profile/delete_account.dart';
import 'profile/edit_profile.dart';
import 'shared/app_bloc/app_bloc.dart';
import 'song/audio_player_background.dart';
import 'video/video_player.dart';

extension AppCoodinator on BuildContext {
  BuildContext get _rootContext => AppBloc.navigatorKey.currentContext ?? this;

  void sendFeedbackThienThucTinh({
    String body = '',
  }) {
    EmailSenderService.sendMail(
      subject: '${S.of(this).feedback} ${Configurations.name}',
      recipients: Configurations.emailSupports,
    );
  }

  Future<void> startConfirmReportError({
    required String courseName,
  }) async {
    final isReport = await showDialog(
      context: this,
      builder: (context) =>
          AlertDialogCommon.reportCourse(context, courseName: courseName),
    );

    if (true == isReport) {
      await EmailSenderService.sendMail(
          subject:
              '${Configurations.name} - ${S.of(this).reportError}: $courseName',
          recipients: Configurations.emailSupports,
          body:
              'Đường dẫn audio/video "$courseName" đang bị lỗi. Tôi không thể phát được. \nVui lòng kiểm tra lại.');
    }
  }

  Future<dynamic> startWelcome() {
    return Navigator.of(_rootContext)
        .pushReplacementNamed(WelcomeScreen.routeName);
  }

  void startDashboardAndRemoveUntil({bool useRoot = false}) {
    Navigator.of(useRoot ? _rootContext : this)
        .pushNamedAndRemoveUntil(DashboardScreen.routeName, (route) => false);
  }

  void startLogin({bool useRoot = true, bool isReplacement = false}) {
    final ct = useRoot ? _rootContext : this;
    if (isReplacement) {
      Navigator.of(ct).pushReplacementNamed(LoginScreen.routeName);
    } else {
      Navigator.of(ct).pushNamed(LoginScreen.routeName);
    }
  }

  Future startScreen(
    Widget screen, {
    bool fullscreenDialog = true,
    bool useRoot = false,
  }) {
    return Navigator.of(useRoot ? _rootContext : this).push(MaterialPageRoute(
      builder: (context) => screen,
      fullscreenDialog: fullscreenDialog,
    ));
  }

  Future<void> startRegisterSocial({fireb.User? user, String? displayName}) async {
    await Navigator.of(this)
        .pushNamed(SocialSignupScreen.routeName, arguments: {'user': user, 'displayName': displayName});
  }

  Future<dynamic> startForgetPassword() {
    return Navigator.of(this).pushNamed(ForgetPassword.routeName);
  }

  Future<void> startRegister(
      {bool useRoot = false, bool isRemoveUntil = false}) async {
    final ct = useRoot ? _rootContext : this;
    if (isRemoveUntil) {
      await Navigator.of(ct).pushNamedAndRemoveUntil(
          SignupScreen.routeName, (route) => route.isFirst);
    } else {
      await Navigator.of(ct).pushNamed(SignupScreen.routeName);
    }
  }

  Future<void> startPaymentSuccess({String? message}) async {
    await Navigator.of(this).pushNamed(PaymentSuccessScreen.routeName,
        arguments: {'message': message});
  }

  Future<void> startProfile() async {
    await Navigator.of(this).pushNamed(EditProfileScreen.routeName);
  }

  void pop<T extends Object?>([T? result]) {
    Navigator.of(this).pop<T>(result);
  }

  Future startHtmlDetail({String? content}) {
    return Navigator.of(this)
        .pushNamed(HtmlDetailScreen.routeName, arguments: {'content': content});
  }

  Future startStories(List? storyItems) {
    return Navigator.of(this)
        .pushNamed(Stories.routeName, arguments: {'storyItems': storyItems});
  }

  Future starttWitterLogin() {
    return Navigator.pushNamed(this, TwitterLoginScreen.routeName);
  }

  Future startPayment({bool useRoot = false}) {
    return showModalBottomSheet(
      isScrollControlled: true,
      barrierColor: Colors.white24,
      context: this,
      builder: (p0) => Provider<SubscriptionBloc>(
        create: (_) => SubscriptionBloc(),
        dispose: (_, _bloc) => _bloc.dispose(),
        child: const SubcriptionWidget(
          title: 'Thiền Thức Tỉnh',
          percent: 38,
          description:
              'Chưa đến 75k vnđ/tháng, tương đương 1 bữa ăn/tháng, cho số '
              'lượng lớn các bài thiền là “thức ăn” tốt lành cho tâm '
              'hồn, giúp chữa lành và tiến hoa tâm thức toàn diện',
          oldPrice: 1450000,
          newPrice: 890000,
        ),
      ),
    );
  }

  Future startLessons({
    String? id,
    String? coverImage,
    String? name,
    String? desc,
    bool useRoot = false,
  }) {
    final ct = useRoot ? _rootContext : this;
    return Navigator.of(ct)
        .pushNamed(LessonDetailsScreen.routeName, arguments: {
      'id': id,
      'coverImage': coverImage,
      'name': name,
      'description': desc,
    });
  }

  Future startVideoScreen({
    required String url,
    required String videoName,
    String? description,
    String? imageUrl,
    bool useRoot = false,
  }) {
    final ct = useRoot ? _rootContext : this;
    return Navigator.of(ct).pushNamed(
      BumbleBeeRemoteVideo.routeName,
      arguments: {
        'videoName': videoName,
        'imageUrl': imageUrl,
        'url': Uri.tryParse(url).toString(),
        'description': description ?? ''
      },
    );
  }

  Future startSongBackgroundScreen({
    required String url,
    required String audioName,
    String? description,
    String? imageUrl,
    String? backgroundImage,
    bool useRoot = false,
  }) {
    final ct = useRoot ? _rootContext : this;
    return Navigator.of(ct).pushNamed(
      AudioPlayerBackgroundScreen.routeName,
      arguments: {
        'audioName': audioName,
        'url': Uri.tryParse(url).toString(),
        'imageUrl': imageUrl,
        'description': description ?? '',
        'backgroundImage': backgroundImage ?? '',
      },
    );
  }

  Future startDeleteAccount() {
    return Navigator.of(this).pushNamed(DeleteAccountScreen.routeName);
  }

  /////------- Temp Coodinator
  void startOldDashboardAndRemoveUntil({bool useRoot = false}) {
    Navigator.of(useRoot ? _rootContext : this).pushNamedAndRemoveUntil(
        OldDashboardScreen.routeName, (route) => false);
  }

  Future startDetailCategoryMeditation(CategoryMeditation category) {
    return Navigator.of(_rootContext).pushNamed(
        DetailCategoryMeditaionScreen.routeName,
        arguments: {'category': category});
  }

  Future startGetMessages() {
    return Navigator.of(this).pushReplacementNamed(ScreenGetMessages.routeName);
  }

  Future startChooseCardScreen() {
    return Navigator.of(this).pushNamed(ChooseCardScreen.routeName);
  }

  Future startDetailCourse(CourseMeditation course,
      {CategoryData? category, bool? isPremium}) {
    return Navigator.of(_rootContext)
        .pushNamed(DetailCourseScreen.routeName, arguments: {
      'course': course,
      'category': category,
      'isPremium': isPremium,
    });
  }

  Future startCardDetail({
    required String heroTag,
  }) {
    return Navigator.of(this)
        .pushReplacementNamed(CardDetailScreen.routeName, arguments: {
      'heroTag': heroTag,
    });
  }

  void startSnackBar(String message) {
    ScaffoldMessenger.of(this).showSnackBar(SnackBar(
      content: Text(message),
      duration: const Duration(seconds: 5),
    ));
  }

  Future<bool?> startDialogConfirmExitApp() {
    return showDialog<bool?>(context: this, builder: AlertDialogCommon.exitApp);
  }

  Future<bool?> startDialogConfirmLogout() {
    return showDialog<bool?>(
      context: this,
      builder: AlertDialogCommon.logOutAccount,
    );
  }

  Future startBreathingExercise() {
    return Navigator.of(this).pushNamed(BreathingExerciseScreen.routeName);
  }

  Future startBreathingExerciseDetails() {
    return Navigator.of(this)
        .pushNamed(BreathingExerciseDetailsScreen.routeName);
  }

  Future startBreathingExerciseIntro() {
    return Navigator.of(this).pushNamed(BreathingExerciseIntroScreen.routeName);
  }
}
