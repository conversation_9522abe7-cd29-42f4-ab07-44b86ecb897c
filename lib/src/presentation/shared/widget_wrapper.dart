import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';
import 'package:meditaguru/src/core/services/deep_link_service.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/services/firebase/push_noti/firebase_cloud_messaging_wapper.dart';
import 'package:meditaguru/src/core/services/firebase/push_noti/local_push.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/category_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';

import '../../core/services/url_launcher_service.dart';
import '../../core/utils.dart';

class WidgetWrapper extends StatefulWidget {
  final Widget child;

  const WidgetWrapper({Key? key, required this.child}) : super(key: key);

  @override
  _WidgetWrapperState createState() => _WidgetWrapperState();
}

class _WidgetWrapperState extends BaseStateScreen<WidgetWrapper>
    implements
        FirebaseCloudMessagingDelegate,
        LocalPushServicegDelegate,
        DeeplinkServiceDelegate {
  @override
  void initState() {
    super.initState();

    if (!mounted) {
      return;
    } else {
      injector<AccountBloc>().loginFirst.listen((value) {
        if (value) {
          FirebaseCloudMessagagingWapper()
            ..delegate = this
            ..init();
        }
      });
      injector<LocalPushService>().delegate = this;
      injector<LocalPushService>().requestPermissions();

      Future.delayed(const Duration(milliseconds: 2200)).then((value) {
        DeeplinkService()
          ..delegate = this
          ..fetchLinkData();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void onResume() {}

  @override
  void onPause() {}

  @override
  void onDetach() {}

  @override
  void onMessage(Map<String, dynamic> message) {
    Log.info('[app.dart]', 'onMessage Pushnotification: $message');
    handleOnShowMessage(message['popup_content']);
  }

  @override
  void onResumeFCM(Map<String, dynamic> message) async {
    Log.info('[app.dart]', 'onResume Pushnotification: $message');
    handleOnTapNotification(message);
  }

  @override
  void onLaunch(Map<String, dynamic> message) async {
    Log.info('[app.dart]', 'onLaunch Pushnotification: $message');
    handleOnTapNotification(message);
  }

  @override
  void onSelectNotification(String payload) {
    Log.info('[app.dart]', 'onSelectNotification Pushnotification: $payload');
    handleOnTapNotification(json.decode(payload));
  }

  void handleOnTapNotification(Map<String, dynamic> message) {
    switch (message['type']) {
      case 'lesson':
        injector
            .get<CategoryUsecase>()
            .getLessonFromCategory(
              isUnLock: injector<AccountBloc>().isUnLock,
              categoryName: message['category_name'],
              lessonName: message['lesson_name'],
            )
            .then((lesson) async {
          if (lesson != null) {
            if (lesson.isAudio) {
              await context.startSongBackgroundScreen(
                url: lesson.url,
                audioName: lesson.name,
                description: lesson.description,
                imageUrl: lesson.imageUrl,
                useRoot: true,
              );
            } else {
              await context.startVideoScreen(
                url: lesson.url,
                videoName: lesson.name,
                description: lesson.description,
                imageUrl: lesson.imageUrl,
                useRoot: true,
              );
            }
            await FirebaseAnalyticsWapper()
                .analytics
                .logEvent(name: 'Lesson', parameters: {
              'audioName': lesson.name,
            });

            if (injector<AccountBloc>().isUXCamInit) {
              await FlutterUxcam.logEventWithProperties('Lesson', {
                'audioName': lesson.name,
              });
            }
          }
        });

        break;
      case 'category':
        injector
            .get<CategoryUsecase>()
            .getCategoryByName(message['category_name'].toString())
            .then((category) {
          if (category != null) {
            context.startLessons(
              id: category.id,
              coverImage: category.coverImage,
              desc: category.description,
              name: category.name,
              useRoot: true,
            );
          }
        });
        break;
      case 'update':
        _launchAppStore();
        break;
      default:
        handleOnShowMessage(message['popup_content']);
        break;
    }
  }

  void handleOnShowMessage(String? message) {
    if (StringUtils.isNotEmpty(message)) {
      handleUIBloc.handleSuccess(message ?? '');
    }
  }

  @override
  void onTokenPushReceive(String token) async {
    Log.printSimpleLog('Token push: $token');
    await injector<SharedPreferencesManager>().putString(keyTokenPush, token);
  }

  @override
  void onDidReceiveLocalNotification(
      int id, String? title, String? body, String? payload) {
    Log.info('[app.dart]',
        'onDidReceiveLocalNotification Pushnotification: $payload');
  }

  @override
  void receiveInviteCode(String code) async {
    await injector<SharedPreferencesManager>().putString(keyInviteCode, code);

    if (injector<AccountBloc>().isLogedIn()) {
      await injector<AccountBloc>().logout();
    }

    await context.startRegister(useRoot: true, isRemoveUntil: true);
  }

  Future<void> _launchAppStore() async {
    final String androidAppId =
        'com.inapps.medita'; // Replace with your actual Android package name
    final String iosAppId = '**********'; // Replace with your actual iOS app ID

    if (Platform.isAndroid) {
      final Uri playStoreUrl = Uri.parse('market://details?id=$androidAppId');

      if (await UrlLauncherService.canLaunch(playStoreUrl)) {
        await UrlLauncherService.launch(playStoreUrl);
      } else {
        // Handle the case where the Play Store app isn't installed
        await UrlLauncherService.launchURL(
            'https://play.google.com/store/apps/details?id=$androidAppId');
      }
    } else if (Platform.isIOS) {
      final Uri appStoreUrl =
          Uri.parse('itms-apps://itunes.apple.com/app/id$iosAppId');

      if (await UrlLauncherService.canLaunch(appStoreUrl)) {
        await UrlLauncherService.launch(appStoreUrl);
      } else {
        // Handle the case where the App Store app isn't installed
        await UrlLauncherService.launchURL(
            'https://apps.apple.com/app/id$iosAppId');
      }
    }
  }
}
