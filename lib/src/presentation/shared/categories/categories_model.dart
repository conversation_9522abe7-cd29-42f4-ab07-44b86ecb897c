import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/domain/entities/category_data.dart';
import 'package:meditaguru/src/domain/usecase/category_usecase.dart';

class CategoriesModel extends ChangeNotifier {
  final List<CategoryData> _categories = <CategoryData>[];
  final CategoryUsecase _categoryUsecase;
  bool _isLoading = false;

  CategoriesModel(this._categoryUsecase);

  List<CategoryData> get categories => _categories;
  bool get isLoading => _isLoading;

  Future<void> getCategories() async {
    _isLoading = true;
    notifyListeners();

    final ctg = await _categoryUsecase.getCategoriesData();
    _categories
      ..clear()
      ..addAll(ctg);

    _isLoading = false;
    notifyListeners();
  }

  CategoryData? getCategoryByCourse(CourseMeditation course) {
    final index =
        _categories.indexWhere((element) => element.id == course.categoryId);

    return index >= 0
        ? _categories[index].copyWith(
            isPaid: course.isPaid,
            time: course.time,
          )
        : null;
  }
}
