import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/network/api_exception.dart';
import 'package:meditaguru/src/core/services/facebook_login_service.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_crashlytics_wapper.dart';
import 'package:meditaguru/src/core/services/firebase/push_noti/local_push.dart';
import 'package:meditaguru/src/core/services/google_login_service.dart';
import 'package:meditaguru/src/data/datasource/firestore/firestore_user.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/user/user_full_info.dart';
import 'package:meditaguru/src/domain/usecase/medita_usecase.dart';
import 'package:meditaguru/src/domain/usecase/user_usecase.dart';
import 'package:meditaguru/src/presentation/dashboard_old/reminder/alarm.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/handle_ui_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/configurations/configurations.dart';
import '../../../core/utils.dart';
import '../../../domain/entities/global_config.dart';

class AccountBloc extends BaseBloc {
  static const twitterConsumerKey = '*************************';
  static const twitterConsumerSecret =
      'f7Dbe6p6hL4raVIE5KRNBsGpkal0nbU6kCklGaLbcuq2aNN3gM';
  static const twitterCallbackHandler =
      'https://medita-fef9a.firebaseapp.com/__/auth/handler';
  static const firebaseProjectID = 'medita-fef9a';
  static const fbAppid = '****************';
  static const fbAuthHandlerRedirectUrl =
      'https://medita-fef9a.firebaseapp.com/__/auth/handler';
  static const dynamicLinkInvite = 'https://thienthuctinh.page.link/install';

  late LoadingBloc loadingBloc;
  late HandleUIBloc handleUIBloc;
  HandleUIBloc handleUIPaymentBloc = HandleUIBloc();
  StreamSubscription? _streamSubscription;
  FirebaseAuth auth = FirebaseAuth.instance;
  bool isPuchased = false;
  bool isPuchasedPremium = false;
  bool isPuchasedPremiumManual = false;
  DateTime? expiryDate;
  DateTime? expiryDatePremium;
  DateTime? expiryDatePremiumManual;
  late MeditaUsecase meditaUsecase;
  UserFullInfo userFull = UserFullInfo();

  bool get isUnLock =>
      (isPuchased || isPuchasedPremium || isPuchasedPremiumManual);
  bool get isBothPuchasedPremium =>
      (isPuchasedPremium || isPuchasedPremiumManual);

  InAppPurchase iap = InAppPurchase.instance;
  StreamSubscription? subscription;

  ReplaySubject<User?> userController = ReplaySubject();
  ReplaySubject<UserFullInfo?> userFullInfoController = ReplaySubject();

  ReplaySubject<List<PurchaseDetails?>?> purchaseDetailsController =
      ReplaySubject();
  List<PurchaseDetails?>? purchases = [];

  BehaviorSubject<GlobalConfig?> globalConfigController =
      BehaviorSubject<GlobalConfig?>();
  GlobalConfig? globalConfig;

  BehaviorSubject<bool> loginFirst = BehaviorSubject<bool>.seeded(false);

  Future<void> getGlobalConfig() async {
    try {
      globalConfig = await meditaUsecase.getGlobalConfig();
      if (StringUtils.isEmpty(globalConfig?.bannerLink)) {
        globalConfig?.bannerLink =
            'https://docs.google.com/document/d/1dTisY879tjFoCJpx0bWsoPuO8_lDX_LJ9JAAlXYHTCg/edit?usp=sharing';
      }
    } catch (e) {
      globalConfig = GlobalConfig(
        termsAndConditions: 'https://inapps.net',
        privacyPolicy: 'https://inapps.net',
        bannerLink:
            'https://docs.google.com/document/d/1dTisY879tjFoCJpx0bWsoPuO8_lDX_LJ9JAAlXYHTCg/edit?usp=sharing',
        storiesDuration: 6,
      );
    }
    globalConfigController.add(globalConfig);
  }

  AccountBloc() {
    loadingBloc = injector<LoadingBloc>();
    meditaUsecase = injector<MeditaUsecase>();
  }

  @override
  void init() {}

  void setView(HandleUIBloc handleBloc) {
    loadingBloc.loaded();
    handleUIBloc = handleBloc;
  }

  bool isLogedIn() {
    return auth.currentUser != null;
  }

  bool isFirstRun = true;

  void regisListener() {
    if (isFirstRun) {
      loginFirst.add(true);
    }

    injector
        .get<FirestoreUserDataSource>()
        .subscriptionRegisterUser('${auth.currentUser?.uid}')
        .listen((QuerySnapshot snapshot) {
      if (isFirstRun) {
        isFirstRun = false;
      } else {
        if (snapshot.docs.isNotEmpty) {
          getUser(isInit: false);
        }
      }
    });
  }

  bool isUXCamInit = false;
  bool _isUXCamInitializing = false;
  int _initRetryCount = 0;
  static const int _maxRetryCount = 3;

  Future<void> initUXCam() async {
    // Prevent multiple simultaneous initializations
    if (_isUXCamInitializing || isUXCamInit) {
      Log.printSimpleLog('UXCam already initializing or initialized, skipping...');
      return;
    }

    // Skip UXCam initialization in debug mode during Hot Restart to prevent crashes
    // You can enable this for testing by setting the environment variable ENABLE_UXCAM_DEBUG=true
    if (kDebugMode) {
      Log.printSimpleLog('UXCam initialization skipped in debug mode to prevent Hot Restart crashes');
      Log.printSimpleLog('To enable UXCam in debug mode, set ENABLE_UXCAM_DEBUG=true');
      return;
    }

    _isUXCamInitializing = true;

    try {
      Log.printSimpleLog('UXCamInit attempt ${_initRetryCount + 1}');
      var _firebaseAuth = FirebaseAuth.instance;
      var user = _firebaseAuth.currentUser;

      await FlutterUxcam
          .optIntoSchematicRecordings(); //// Confirm that you have user permission for screen recording
      await FlutterUxcam.startWithConfiguration(
          FlutterUxConfig(userAppKey: 'bp4f1ra5bxb8g90'));
      await FlutterUxcam.setAutomaticScreenNameTagging(false);

      if (user != null) {
        var onData = await injector.get<UserUsecase>().getUserByDocId(user.uid);
        final data = onData.data() ?? {};
        var userFull = UserFullInfo()
          ..name = data['name'] == null ? '' : data['name'].toString()
          ..email = data['email'] == null ? '' : data['email'].toString()
          ..photoUrl =
              data['photoUrl'].toString().isEmpty ? null : data['photoUrl']
          ..phone = data['phone'] == null ? '' : data['phone'].toString()
          ..joindate = data['joiningDate'] == null
              ? null
              : data['joiningDate'].toString()
          ..dob = data['dob'] == null ? null : data['dob'].toString();

        await FlutterUxcam.setUserIdentity(user.email ?? (user.uid));
        await FlutterUxcam.setUserProperty('alias', userFull.name ?? '');
        await FlutterUxcam.setUserProperty('full_name', userFull.name ?? '');
        await FlutterUxcam.setUserProperty('phone', userFull.phone ?? '');
        await FlutterUxcam.setUserProperty('email', userFull.email ?? '');
        await FlutterUxcam.setUserProperty('uid', user.uid);
      } else {
        await FlutterUxcam.setUserIdentity('Guest');
        await FlutterUxcam.setUserProperty('alias', 'Guest');
      }

      isUXCamInit = true;
      _initRetryCount = 0; // Reset retry count on success
      Log.printSimpleLog('UXCamInit success');
    } catch (e) {
      Log.printSimpleLog('UXCamInit failed: ${e.toString()}');
      _initRetryCount++;

      // Retry with exponential backoff if under retry limit
      if (_initRetryCount < _maxRetryCount) {
        final delayMs = 1000 * _initRetryCount; // 1s, 2s, 3s delays
        Log.printSimpleLog('Retrying UXCam init in ${delayMs}ms...');
        await Future.delayed(Duration(milliseconds: delayMs));
        _isUXCamInitializing = false;
        return initUXCam(); // Recursive retry
      } else {
        Log.printSimpleLog('UXCam init failed after $_maxRetryCount attempts');
      }
    } finally {
      _isUXCamInitializing = false;
    }
  }

  /// Get and update user information
  Future<UserFullInfo> getFullInfoUser() async {
    var _firebaseAuth = FirebaseAuth.instance;
    var user = _firebaseAuth.currentUser;
    var onData =
        await injector.get<UserUsecase>().getUserByDocId('${user?.uid}');

    // final data = onData.docs.first.data();
    final data = onData.data() ?? {};

    userFull
      ..name = data['name'] == null ? '' : data['name'].toString()
      ..email = data['email'] == null ? '' : data['email'].toString()
      ..photoUrl = (data['photoUrl']?.isEmpty ?? true) ? null : data['photoUrl']
      ..phone = (data['phone']?.isEmpty ?? true) ? '' : data['phone'].toString()
      ..joindate =
          data['joiningDate'] == null ? null : data['joiningDate'].toString()
      ..dob = data['dob'] == null ? null : data['dob'].toString();
    userFullInfoController.add(userFull);

    // Only update UXCam user properties if UXCam is properly initialized
    if (isUXCamInit) {
      try {
        await FlutterUxcam.setUserIdentity(user?.email ?? (user?.uid));
        await FlutterUxcam.setUserProperty('alias', userFull.name ?? '');
        await FlutterUxcam.setUserProperty('full_name', userFull.name ?? '');
        await FlutterUxcam.setUserProperty('phone', userFull.phone ?? '');
        await FlutterUxcam.setUserProperty('email', userFull.email ?? '');
        await FlutterUxcam.setUserProperty('uid', user?.uid ?? '');
      } catch (e) {
        printLog('UXCam setUserProperty failed: $e');
      }
    }
    return userFull;
  }

  /// Get and check purchase information
  Future<bool> getUser({bool isInit = true}) async {
    if (auth.currentUser == null) {
      expiryDate = null;
      expiryDatePremiumManual = null;
      expiryDatePremium = null;
      return false;
    }
    var isSuccess = false;
    try {
      var snapshot = Configurations.isPremium
          ? await injector.get<UserUsecase>().getUserSystem()
          : await injector.get<UserUsecase>().getUser(auth.currentUser!.uid);

      if (isInit) {
        await checkPurchased(snapshot.docs);
      }

      if (snapshot.docs.isNotEmpty) {
        if (snapshot.docs[0].data()['bonus'] != null &&
            StringUtils.isNotEmpty(
                snapshot.docs[0].data()['bonus']['expiryDate'])) {
          expiryDate =
              DateTime.parse(snapshot.docs[0].data()['bonus']['expiryDate']);
          if (expiryDate!.isAfter(DateTime.now())) {
            isPuchased = true;
          } else {
            expiryDate = null;
            isPuchased = false;
          }
        } else {
          expiryDate = null;
          isPuchased = false;
        }

        if (snapshot.docs[0].data()['premiumPlan'] != null &&
            StringUtils.isNotEmpty(snapshot.docs[0].data()['premiumPlan'])) {
          expiryDatePremiumManual =
              DateTime.parse(snapshot.docs[0].data()['premiumPlan'])
                  .toUtc()
                  .toLocal();
          if (expiryDatePremiumManual!.isAfter(DateTime.now())) {
            isPuchasedPremiumManual = true;
          } else {
            expiryDatePremiumManual = null;
            isPuchasedPremiumManual = false;
          }
        } else {
          expiryDatePremiumManual = null;
          isPuchasedPremiumManual = false;
        }

        userFullInfoController.add(userFull);
      }
      isSuccess = true;
      unawaited(FirebaseCrashlyticsService.setUserIdentifier(
          auth.currentUser?.uid ?? ''));
      userController.add(auth.currentUser);
    } on CustomException catch (e) {
      handleUIBloc.handleError(e.errorMessage?.clearLogError);
    } catch (e) {
      handleUIBloc.handleError(e.toString().clearLogError);
    } finally {
      if (isInit) {
        // loadingBloc.loaded();
      }
    }
    return isSuccess;
  }

  Future<bool> logout() async {
    var isSuccess = false;
    loadingBloc.loading();
    try {
      resetPuchaseField();
      if (subscription != null) {
        await subscription?.cancel();
      }
      isFirstRun = true;
      await auth.signOut();
      await GoogleService.logout();
      await FacebookService.logout();
      // TwitterService.logout(
      //     consumerKey: GlobalConfiguration().getValue("twitterConsumerKey"),
      //     consumerSecret: GlobalConfiguration().getValue("twitterConsumerSecret")
      // );
      var myPrefs = await SharedPreferences.getInstance();
      List? checkalarmset = myPrefs.getStringList('multipleAlarm');
      if (checkalarmset != null) {
        await myPrefs.remove('multipleAlarm');
        await injector<LocalPushService>().cancelAllNotifications();
        multipleAlarm?.clear();
      }
    } on CustomException catch (e) {
      handleUIBloc.handleError(e.errorMessage, customException: e);
    } finally {
      loadingBloc.loaded();
    }
    return isSuccess;
  }

  void resetPuchaseField() {
    isPuchased = false;
    expiryDate = null;
    isPuchasedPremium = false;
    isPuchasedPremiumManual = false;
    expiryDatePremium = null;
    expiryDatePremiumManual = null;
  }

  @override
  void dispose() {
    subscription?.cancel();
    _streamSubscription?.cancel();
    userController.close();
    purchaseDetailsController.close();
    isFirstRun = true;

    // Reset UXCam state on dispose to prevent issues during Hot Restart
    isUXCamInit = false;
    _isUXCamInitializing = false;
    _initRetryCount = 0;
  }

  Future<void> completeOldTransaction() async {
    if (isIos) {
      var paymentWrapper = SKPaymentQueueWrapper();
      var transactions = await paymentWrapper.transactions();
      transactions.forEach((transaction) async {
        print(transaction.transactionState);
        await paymentWrapper
            .finishTransaction(transaction)
            .catchError((onError) {
          print('finishTransaction Error $onError');
        });
      });
    }
  }

  Future initializePayment() async {
    var isAvailable = await iap.isAvailable();
    if (isAvailable) {
      /// removing all the pending puchases.
      await completeOldTransaction();

      _streamSubscription = iap.purchaseStream.listen((data) async {
        print('objec===============t');

        for (var item in data) {
          if (!(purchases?.contains(item) ?? false)) {
            if (item.status == PurchaseStatus.purchased ||
                item.status == PurchaseStatus.restored) {
              purchases?.add(item);
            }
          }
        }
        purchaseDetailsController.add(purchases);

        PurchaseDetails? purchaseSuccess;
        var purchaseFailed = false;
        for (var purchase in data) {
          var res = await verifyPuchase(purchase);
          if (res != null) {
            if (res) {
              purchaseSuccess = purchase;
            } else {
              purchaseFailed = true;
            }
          }
        }
        if (purchaseSuccess != null) {
          unawaited(injector<MeditaUsecase>()
              .updateItemPurchased(auth.currentUser, purchaseSuccess));
          unawaited(injector<MeditaUsecase>()
              .updateItemPurchasedByUser(auth.currentUser, purchaseSuccess));
          handleUIPaymentBloc
              .handleSuccess(getDurationText(purchaseSuccess.productID));
          await FirebaseAnalyticsWapper()
              .analytics
              .logEvent(name: 'Purchase', parameters: {
            'action': 'payment_success',
            'product_id': purchaseSuccess.productID,
          });
          if (injector<AccountBloc>().isUXCamInit) {
            await FlutterUxcam.logEventWithProperties('Purchase', {
              'action': 'payment_success',
              'product_id': purchaseSuccess.productID,
            });
          }
        } else if (purchaseFailed) {
          handleUIPaymentBloc.handleError(S.current.paymentFailed);
        }
      });
      _streamSubscription?.onError(
        (error) {
          handleUIPaymentBloc.handleError(S.current.paymentFailed);
        },
      );
    }
  }

  String getDuration(String? productId) {
    var text = productId ?? '';
    switch (productId) {
      case 'month_1':
        text = '1 ${S.current.month}';
        break;
      case 'month_6':
        text = '6 ${S.current.months}';
        break;
      case 'year_1':
        text = '1 ${S.current.year}';
        break;
    }
    return text;
  }

  int getMonthsExprie(String? productId) {
    var months = 0;
    switch (productId) {
      case 'month_1':
        months = 1;
        break;
      case 'month_6':
        months = 6;
        break;
      case 'year_1':
        months = 12;
        break;
    }
    return months;
  }

  String getDurationText(String? productId) {
    return S.current.paymentSuccessful(getDuration(productId));
  }

  ///get past purchases of user
  Future<bool> checkPurchased(
      List<QueryDocumentSnapshot<Map<String, dynamic>>> docs) async {
    var isPuchased = false;
    try {
      if (isIos) {
        /// Cannot load past purchases of user for iOS platform, so we need to load the past purchases from Firebase to verify premium plan
        final haveOldPurhcases = docs.isNotEmpty &&
            docs[0].data()['listReceiptByDevice'] != null &&
            docs[0].data()['listReceiptByDevice']['purchasedItemsByDevice'] !=
                null;
        final pastPurchasesList = haveOldPurhcases
            ? docs[0].data()['listReceiptByDevice']['purchasedItemsByDevice']
            : null;

        List<PurchaseDetails?>? oldIosPurchases;
        if (pastPurchasesList?.isNotEmpty ?? false) {
          oldIosPurchases =
              pastPurchasesList?.map<AppStorePurchaseDetails>((e) {
            final decodedData =
                json.decode(e['originalJson']) as Map<String, dynamic>;
            return decodedData.convertToAppStorePurchaseDetails();
          }).toList();

          oldIosPurchases?.removeWhere(
              (element) => element?.verificationData.source != 'app_store');
        }

        purchases = oldIosPurchases;
      } else if (isAndroid) {
        var androidAddition =
            iap.getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();
        var response = await androidAddition.queryPastPurchases();
        purchases = response.pastPurchases;
      }

      purchaseDetailsController.add(purchases);
      if (purchases != null && (purchases?.length ?? 0) > 0) {
        for (var purchase in purchases!) {
          print('Plan    ${purchase?.productID}');
          var res = await verifyPuchase(purchase);

          if (res != null) {
            if (res) {
              isPuchased = true;
            } else {
              isPuchased = false;
            }
          }
        }
      }
    } catch (e) {
      log(e.toString());
    }
    return isPuchased;
  }

  /// restore past purchases of user
  Future<bool> restorePurchases() async {
    if (Platform.isIOS) {
      final iosPlatformAddition =
          iap.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
      await iap.restorePurchases();
      return true;
    }
    // handleUIPaymentBloc.handleSuccess(getDurationText('month_1'));

    // loadingBloc.loading();
    // List<PurchaseDetails> response = await iap.purchaseStream.single;
    var androidAddition =
        iap.getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();

    // InAppPurchaseAndroidPlatform androidAddition =
    // InAppPurchase.instance.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();

    var response = await androidAddition.queryPastPurchases();

    // QueryPurchaseDetailsResponse response = await iap.queryPastPurchases();
    for (PurchaseDetails purchase in response.pastPurchases) {
      if (isIos) {
        await iap.completePurchase(purchase);
      }
    }
    purchases = response.pastPurchases;
    purchaseDetailsController.add(purchases);
    if ((purchases?.length ?? 0) > 0) {
      bool? isSuccess;
      PurchaseDetails? purchaseSuccess;
      for (var purchase in purchases!) {
        print('Plan    ${purchase?.productID}');
        var res = await verifyPuchase(purchase);
        if (res != null) {
          isSuccess = res;
          purchaseSuccess = purchase;
        }
      }
      if (isSuccess != null) {
        if (isSuccess) {
          handleUIPaymentBloc
              .handleSuccess(getDurationText(purchaseSuccess?.productID));
        } else {
          handleUIPaymentBloc.handleError(S.current.paymentFailed);
        }
      }
      // loadingBloc.loaded();
      return true;
    } else {
      // loadingBloc.loaded();
      return false;
    }
  }

  /// check if user has pruchased
  // PurchaseDetails? _hasPurchased(String? productId) {
  //   return purchases?.firstWhere((purchase) => purchase?.productID == productId,
  //       orElse: () => null);
  // }

  Future<bool?> verifyPuchase(PurchaseDetails? purchase) async {
    print('purchase');
    print(purchase?.toString());
    if (purchase != null && purchase.pendingCompletePurchase) {
      print(purchase.productID);
      await iap.completePurchase(purchase);
    }
    var newExpireOn = DateTime.fromMillisecondsSinceEpoch(
            int.parse(purchase?.transactionDate ?? '0'))
        .add(extendDate(purchase?.productID));
    if (purchase != null &&
        purchase.status == PurchaseStatus.purchased &&
        newExpireOn.isAfter(DateTime.now())) {
      // print('InAppPurchase');
      // if (isAndroid) {
      //   PurchaseWrapper? billingClientPurchase = purchase.billingClientPurchase;
      //   print(billingClientPurchase?.originalJson??'');
      // } else {
      //   SKPaymentTransactionWrapper? skProduct = purchase.skPaymentTransaction;
      //   print(skProduct?.transactionState);
      // }
      var needUpdate = false;
      if (expiryDatePremium == null ||
          newExpireOn.isAfter(expiryDatePremium!)) {
        needUpdate = true;
        expiryDatePremium = newExpireOn;
      }

      // if(expiryDatePremium != null && expiryDatePremium!.isAfter(DateTime.now())) {
      //   isPuchasedPremium = true;
      //   userFullInfoController.add(userFull);
      // }

      isPuchasedPremium = true;
      if (needUpdate) {
        userFullInfoController.add(userFull);
        return isPuchasedPremium;
      } else {
        return false;
      }
    } else if (purchase != null && purchase.status == PurchaseStatus.error) {
      isPuchasedPremium = false;
      expiryDatePremium = null;
      userFullInfoController.add(userFull);
      return false;
    } else {
      return null;
    }
  }

  Duration extendDate(id) {
    if (id == 'month_1') {
      return const Duration(days: 30);
    } else if (id == 'month_6') {
      return const Duration(days: 180);
    } else if (id == 'year_1') {
      return const Duration(days: 365);
    } else {
      return const Duration(days: 0);
    }
  }

  Future<bool> delelteAccount() async {
    try {
      loadingBloc.loading();

      await meditaUsecase.deleteAccount(auth.currentUser);

      /// If so fast, Apple will be suspect this action is ambiguous
      /// And has no effect on the account.
      /// So we need to wait for a while.
      /// This is a workaround.
      await Future.delayed(const Duration(seconds: 2));

      loadingBloc.loaded();
      return true;
    } catch (e) {
      loadingBloc.loaded();
      return false;
    }
  }
}

/// Example implementation of the
/// [`SKPaymentQueueDelegate`](https://developer.apple.com/documentation/storekit/skpaymentqueuedelegate?language=objc).
///
/// The payment queue delegate can be implementated to provide information
/// needed to complete transactions.
class ExamplePaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(
      SKPaymentTransactionWrapper transaction, SKStorefrontWrapper storefront) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}

extension AppStorePurchaseDetailsExtension on Map<String, dynamic> {
  AppStorePurchaseDetails convertToAppStorePurchaseDetails() {
    return AppStorePurchaseDetails(
      productID: this['productID'],
      purchaseID: this['purchaseID'],
      verificationData: PurchaseVerificationData(
        localVerificationData: this['verificationDataLocal'],
        serverVerificationData: this['serverVerificationData'],
        source: this['verificationDataSource'],
      ),
      transactionDate:
          (DateTime.tryParse(this['transactionDate'])?.millisecondsSinceEpoch ??
                  0 ~/ 1000)
              .toString(),
      status: PurchaseStatus.values.firstWhere(
          (element) =>
              element.name ==
              (this['status'] as String?)?.replaceAll('PurchaseStatus.', ''),
          orElse: () => PurchaseStatus.purchased),
      skPaymentTransaction: SKPaymentTransactionWrapper(
        payment: SKPaymentWrapper(productIdentifier: this['productID']),
        transactionState: SKPaymentTransactionStateWrapper.values.firstWhere(
            (element) =>
                element.name ==
                (this['status'] as String?)?.replaceAll('PurchaseStatus.', ''),
            orElse: () => SKPaymentTransactionStateWrapper.purchased),
      ),
    );
  }
}
