import 'package:flutter/material.dart';

import '../../../../core/widgets/common/card_user_widget.dart';
import '../../../../di/injection/injection.dart';
import '../../../../domain/entities/user/user_full_info.dart';
import '../account_bloc.dart';

class UserCardWidget extends StatefulWidget {
  const UserCardWidget({super.key});

  @override
  State<UserCardWidget> createState() => _UserCardWidgetState();
}

class _UserCardWidgetState extends State<UserCardWidget> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UserFullInfo?>(
      stream: injector<AccountBloc>().userFullInfoController,
      builder: (context, snapshot) {
        var user = snapshot.data;
        return CardUserWidget(user: user);
      },
    );
  }
}
