import 'dart:math';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/domain/entities/choose_card_info.dart';
import 'package:meditaguru/src/domain/entities/daily_inspiration_data.dart';
import 'package:meditaguru/src/domain/usecase/daily_inspiration_usecase.dart';

import '../../../core/configurations/configurations.dart';

class DailyInspirationModel extends ChangeNotifier {
  final DailyInspirationUsecase _inspirationUsecase;

  DailyInspirationModel(this._inspirationUsecase) {
    _cardSelected = _inspirationUsecase.getCard();
  }

  final _listDailyInspiration = <DailyInspirationData>[];
  final _listImageCard = <String>[];
  final _imageProviders = <ImageProvider>[];
  ChooseCardInfo? _cardSelected;
  ChooseCardInfo? get cardSelected => _cardSelected;
  List<DailyInspirationData> get listDailyInspiration => _listDailyInspiration;

  bool get canGetCard =>
      _cardSelected == null ||
      (_cardSelected != null &&
          DateTime.now().compareTo(_cardSelected!.time
                  .add(Duration(seconds: Configurations.timeDrawCard))) ==
              1);

  Future<void> loadData({void Function()? loadDone}) async {
    final listCard = await _inspirationUsecase.getListDailyInspiration();

    _listDailyInspiration
      ..clear()
      ..addAll(listCard);

    _listImageCard
      ..clear()
      ..addAll(
        listCard.map((e) => e.image.mediaLinkFirebase).toList(),
      );

    notifyListeners();
    loadDone?.call();
  }

  Future<void> preloadImage(BuildContext context) async {
    if (_imageProviders.isEmpty) {
      final futureList = <Future>[];
      for (var element in _listImageCard) {
        late ImageProvider provider;
        if (element.contains('http')) {
          provider = ExtendedNetworkImageProvider(element);
        } else {
          provider = ExtendedAssetImageProvider(element);
        }

        _imageProviders.add(provider);
        futureList.add(precacheImage(provider, context));
      }

      await Future.wait(futureList);
    }
  }

  void selectCard(DailyInspirationData card) {
    _cardSelected = ChooseCardInfo(card: card, time: DateTime.now());
    _inspirationUsecase.saveCardSelected(_cardSelected!);
  }

  (DailyInspirationData, ImageProvider) getCard() {
    final randomCard = Random().nextInt(_listDailyInspiration.length);
    final card = _listDailyInspiration[randomCard];

    return (
      card,
      _imageProviders[randomCard],
    );
  }
}
