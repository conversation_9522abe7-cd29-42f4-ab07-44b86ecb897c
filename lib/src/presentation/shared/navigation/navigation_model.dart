// To parse this JSON data, do
//
//     final configModel = configModelFromJson(jsonString);

import 'dart:convert';

import 'package:flutter/widgets.dart';

import '../../../domain/entities/bottom_bar_item.dart';

ConfigModel configModelFromJson(String str) =>
    ConfigModel.fromJson(json.decode(str));

String configModelToJson(ConfigModel data) => json.encode(data.toJson());

class ConfigModel {
  ConfigModel({
    this.setting,
    this.splashScreen,
    this.isRequiredLogin,
    this.onBoardOnlyShowFirstTime,
    this.tabBar,
    this.onBoarding,
    this.horizonLayout,
  });

  Setting? setting;
  SplashScreen? splashScreen;
  bool? isRequiredLogin;
  bool? onBoardOnlyShowFirstTime;
  List<NavigationItem>? tabBar;
  List<Map>? horizonLayout;
  OnBoarding? onBoarding;

  factory ConfigModel.fromJson(Map<String, dynamic> json) => ConfigModel(
        setting: Setting.fromJson(json['Setting']),
        splashScreen: SplashScreen.fromJson(json['SplashScreen']),
        isRequiredLogin: json['IsRequiredLogin'],
        onBoardOnlyShowFirstTime: json['OnBoardOnlyShowFirstTime'],
        horizonLayout: List<Map>.from(json['HorizonLayout'] ?? []),
        tabBar: List<NavigationItem>.from(
            // ignore: unnecessary_lambdas
            json['TabBar'].map((x) => NavigationItem.fromJson(x))),
        onBoarding: OnBoarding.fromJson(json['OnBoarding']),
      );

  Map<String, dynamic> toJson() => {
        'Setting': setting?.toJson(),
        'SplashScreen': splashScreen?.toJson(),
        'IsRequiredLogin': isRequiredLogin,
        'OnBoardOnlyShowFirstTime': onBoardOnlyShowFirstTime,
        'HorizonLayout': horizonLayout,
        'TabBar': List<dynamic>.from(tabBar!.map((x) => x.toJson())),
        'OnBoarding': onBoarding?.toJson(),
      };
}

class OnBoarding {
  OnBoarding({
    this.data,
  });

  List<Datum>? data;

  factory OnBoarding.fromJson(Map<String, dynamic> json) => OnBoarding(
        // ignore: unnecessary_lambdas
        data: List<Datum>.from(json['data'].map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        'data': List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  Datum({
    this.title,
    this.image,
    this.desc,
    this.background,
  });

  String? title;
  String? image;
  String? desc;
  String? background;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        title: json['title'],
        image: json['image'],
        desc: json['desc'],
        background: json['background'],
      );

  Map<String, dynamic> toJson() => {
        'title': title,
        'image': image,
        'desc': desc,
        'background': background,
      };
}

class Setting {
  Setting({
    this.mainColor,
    this.productListLayout,
    this.stickyHeader,
  });

  String? mainColor;
  String? productListLayout;
  bool? stickyHeader;

  factory Setting.fromJson(Map<String, dynamic> json) => Setting(
        mainColor: json['MainColor'],
        productListLayout: json['ProductListLayout'],
        stickyHeader: json['StickyHeader'],
      );

  Map<String, dynamic> toJson() => {
        'MainColor': mainColor,
        'ProductListLayout': productListLayout,
        'StickyHeader': stickyHeader,
      };
}

class SplashScreen {
  SplashScreen({
    this.data,
    this.type,
  });

  String? data;
  String? type;

  factory SplashScreen.fromJson(Map<String, dynamic> json) => SplashScreen(
        data: json['Data'],
        type: json['Type'],
      );

  Map<String, dynamic> toJson() => {
        'Data': data,
        'Type': type,
      };
}

class NavigationItem {
  NavigationItem({
    required this.layout,
    this.icon,
    this.defaultSelected,
    this.categoryLayout,
    this.showChat,
    this.data,
    required this.scrollController,
  });

  BottomBarItemType layout;
  String? icon;
  bool? defaultSelected;
  String? categoryLayout;
  Map? data;
  bool? showChat;
  final ScrollController scrollController;

  factory NavigationItem.fromJson(Map<String, dynamic> json) => NavigationItem(
        layout: BottomBarItemType.fromString(json['layout'].toString()),
        icon: json['icon'],
        defaultSelected: json['default_selected'],
        categoryLayout: json['categoryLayout'],
        showChat: json['showChat'],
        data: json['data'],
        scrollController: ScrollController(),
      );

  Map<String, dynamic> toJson() => {
        'layout': layout.toString(),
        'icon': icon,
        'default_selected': defaultSelected,
        'categoryLayout': categoryLayout,
        'showChat': showChat,
        'data': data,
      };
}
