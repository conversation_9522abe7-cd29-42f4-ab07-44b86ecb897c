import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/styles.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/domain/entities/bottom_bar_item.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_model.dart';
import 'package:provider/provider.dart';

class NavigationScaffold extends StatefulWidget {
  final Widget child;

  const NavigationScaffold({Key? key, required this.child}) : super(key: key);

  @override
  _NavigationScaffoldState createState() => _NavigationScaffoldState();
}

class _NavigationScaffoldState extends State<NavigationScaffold> {
  late NavigationBloc navigationBloc;

  @override
  void initState() {
    super.initState();
    navigationBloc = Provider.of<NavigationBloc>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      safeAreaTop: false,
      body: Scaffold(
        extendBody: true,
        body: Container(
          width: double.infinity,
          height: double.infinity,
          child: widget.child,
        ),
        bottomNavigationBar: StreamBuilder<NavigationSate>(
          stream: navigationBloc.navigationController.stream,
          initialData: navigationBloc.navigationSate,
          builder: (BuildContext context, snapshot) {
            return NavigationBar(
              moduleIdSelected:
                  snapshot.data?.moduleIdSelected ?? BottomBarItemType.home,
              items: snapshot.data?.navigationItems ?? [],
              onTap: navigationBloc.onUserChangeTab,
              navigationBloc: navigationBloc,
            );
          },
        ),
      ),
    );
  }
}

class NavigationBar extends StatefulWidget {
  final List<NavigationItem> items;
  final Function(BottomBarItemType) onTap;
  final BottomBarItemType moduleIdSelected;
  final NavigationBloc navigationBloc;

  const NavigationBar({
    Key? key,
    required this.items,
    required this.onTap,
    required this.moduleIdSelected,
    required this.navigationBloc,
  }) : super(key: key);
  @override
  _NavigationBarState createState() => _NavigationBarState();
}

class _NavigationBarState extends State<NavigationBar> {
  @override
  Widget build(BuildContext context) {
    return buildBottomNavigationBar();
  }

  Widget buildBottomNavigationBar() {
    var indexCurrent = widget.navigationBloc.getIndex(widget.moduleIdSelected);

    return Container(
      width: double.infinity,
      height: 68.0,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20.0),
        ),
        gradient: const LinearGradient(
          begin: Alignment(0.08, 1.0),
          end: Alignment(0.15, -2.3),
          colors: [Colors.black, Color(0xFF040404), Color(0xFF414141)],
          stops: [0.0, 0.296, 1.0],
        ),
        border: Border.all(
          width: 0.15,
          color: const Color(0xFF3E434E),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            flex: 1,
            child: InkWell(
              onTap: () {
                if (0 != indexCurrent) {
                  widget.onTap(widget.items[0].layout);
                }
              },
              child: Container(
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/img/round_logo.png',
                      width: 35,
                      height: 35,
                      color: indexCurrent == 0
                          ? AppColors.secondary2Color
                          : const Color(0xFF6B6B6B),
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    CText.caption(
                      'Thiền thức tỉnh',
                      color: indexCurrent == 0
                          ? AppColors.secondaryColor
                          : const Color(0xFF6B6B6B),
                    )
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: InkWell(
              onTap: () {
                if (1 != indexCurrent) {
                  widget.onTap(widget.items[1].layout);
                }
              },
              child: Container(
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.alarm,
                      color: indexCurrent == 1
                          ? AppColors.secondary2Color
                          : const Color(0xFF6B6B6B),
                      size: 35,
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    CText.caption(
                      S.of(context).meditationReminder,
                      color: indexCurrent == 1
                          ? AppColors.secondaryColor
                          : const Color(0xFF6B6B6B),
                    )
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: InkWell(
              onTap: () {
                if (2 != indexCurrent) {
                  widget.onTap(widget.items[2].layout);
                }
              },
              child: Container(
                width: double.infinity,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_outline,
                      color: indexCurrent == 2
                          ? AppColors.secondary2Color
                          : const Color(0xFF6B6B6B),
                      size: 35,
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    CText.caption(
                      S.of(context).profile,
                      color: indexCurrent == 2
                          ? AppColors.secondaryColor
                          : const Color(0xFF6B6B6B),
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
    // var indexCurrent = widget.navigationBloc.getIndex(widget.moduleIdSelected);
    // var widgetsItem = widget.items.map((e) {
    //   if(e.layout == 'home') {
    //     return Icon(Icons.home, color: AppColors.iconColor);
    //   } else if(e.layout == 'alarm') {
    //     return Icon(Icons.alarm, color: AppColors.iconColor);
    //   } else {
    //     return Icon(Icons.settings, color: AppColors.iconColor);
    //   }
    // }).toList();
    // return CurvedNavigationBar(
    //     index: indexCurrent,
    //     height: 60.0,
    //     color: AppColors.primaryColor,
    //     backgroundColor: Colors.transparent,
    //     buttonBackgroundColor: AppColors.primaryColor,
    //     items: [
    //       ...widgetsItem
    //     ],
    //     onTap: (int index) {
    //       if(index != indexCurrent) {
    //         widget.onTap(widget.items[index].layout??'');
    //       }
    //     });
  }

  String getTitle(String layoutKey) {
    switch (layoutKey) {
      case 'home':
        return S.of(context).home;
      case 'product':
        return S.of(context).shopping;
      case 'store':
        return S.of(context).store;
      case 'account':
        return S.of(context).account;
      default:
        return 'default';
    }
  }
}

class NavigationPageChooser extends StatefulWidget {
  final List<Widget> widgets;

  const NavigationPageChooser({Key? key, required this.widgets})
      : super(key: key);

  @override
  _NavigationPageChooserState createState() => _NavigationPageChooserState();
}

class _NavigationPageChooserState extends State<NavigationPageChooser>
    with SingleTickerProviderStateMixin<NavigationPageChooser> {
  late NavigationBloc _navigationBloc;

  var _controller = PageController();

  @override
  void initState() {
    super.initState();
    _navigationBloc = Provider.of<NavigationBloc>(context, listen: false);
    _controller = PageController(
        initialPage: _navigationBloc
            .getIndex(_navigationBloc.navigationSate.moduleIdSelected));
    _navigationBloc.navigationController.stream.listen((event) {
      if (_controller.hasClients) {
        _controller.jumpToPage(_navigationBloc
            .getIndex(_navigationBloc.navigationSate.moduleIdSelected));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // if(widgets == null) {
    //   widgets = _navigationBloc.navigationSate.navigationItems.map(
    //           (item) => MainPage(
    //               child: RouterApp.of(context).getWidget(item.moduleId),
    //               keepAlive: true
    //           )
    //   ).toList();
    // }
    return PageView(
      physics: const NeverScrollableScrollPhysics(),
      controller: _controller,
      children: widget.widgets,
    );
  }
}

class MainPage extends StatefulWidget {
  final Widget child;
  final bool keepAlive;

  const MainPage({Key? key, required this.child, this.keepAlive = true})
      : super(key: key);
  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage>
    with AutomaticKeepAliveClientMixin<MainPage> {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => widget.keepAlive;
}
