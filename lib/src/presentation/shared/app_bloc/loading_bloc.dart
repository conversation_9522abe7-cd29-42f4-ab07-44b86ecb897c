import 'package:rxdart/rxdart.dart';

import '../../../core/base/bloc/base_bloc.dart';

class LoadingBloc extends BaseBloc {
  BehaviorSubject<bool> loadingController = BehaviorSubject<bool>();

  @override
  void init() {
    if (loadingController.hasValue && loadingController.value == true) {
      loaded();
    }
  }

  @override
  void dispose() {
    if (!loadingController.isClosed) {
      loadingController.close();
    }
  }

  void loading() {
    loadingController.add(true);
  }

  void loaded() {
    loadingController.add(false);
  }
}
