import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/app_setting_usecase.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_model.dart';
import 'package:rxdart/rxdart.dart';

import '../../../core/services/firebase/firebase_remote_service_impl.dart';
import '../../../core/utils.dart';
import '../../../data/datasource/local/local_stograge.dart';
import '../../../domain/entities/design_settings.dart';

class AppState {
  ConfigModel appConfig = ConfigModel();
  String? message;
  bool darkTheme = false;
  String locale = kAdvanceConfigRaw['DefaultLanguage'].toString();
  List<String>? categories;
  String? productListLayout;
  String? currency; //USD, VND
  bool isInit = false;
  Map? deeplink;
}

class AppBloc extends BaseBloc {
  ReplaySubject<AppState> appController = ReplaySubject<AppState>();
  AppState appModel = AppState();
  late SharedPreferencesManager _prefs;
  late AppSettingUseCase appSettingUseCase;
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  final FirebaseRemoteServices firebaseRemoteServices;
  final LocalStorageDataSource localStorageDataSource;

  AppBloc({
    required this.appSettingUseCase,
    required this.firebaseRemoteServices,
    required this.localStorageDataSource,
  }) {
    _prefs = injector<SharedPreferencesManager>();
  }

  @override
  Future<void> init() async {
    // await GlobalConfiguration().loadFromPath("assets/config/app_settings.json");
    await getConfig();
    await loadAppConfig();
  }

  @override
  void dispose() {
    appController.close();
  }

  Future<bool> getConfig() async {
    try {
      appModel
        ..locale = appSettingUseCase.getLanguage()
        ..darkTheme = _prefs.getBool(keyDarkTheme) ?? false
        // appModel.currency = _prefs.getString("currency") ??
        //     (kAdvanceConfig['DefaultCurrency'] as Map)['currency'];
        ..isInit = true;
      return true;
    } catch (err) {
      return false;
    }
  }

  Future<bool> changeLanguage(String country) async {
    try {
      appModel.locale = country;

      await appSettingUseCase.setLanguage(country);
      appModel.locale = appSettingUseCase.getLanguage();
      appController.add(appModel);
      return true;
    } catch (err) {
      Log.shout('AppBloc', err.toString());
      return false;
    }
  }

  Future<void> updateTheme(bool theme) async {
    try {
      // SharedPreferences prefs = await SharedPreferences.getInstance();
      // darkTheme = theme;
      // await prefs.setBool("darkTheme", theme);
      // notifyListeners();
    } catch (err) {
      Log.shout('AppBloc', err.toString());
    }
  }

  Future<ConfigModel> _getDesignConfig(DesignSettings designSettings) async {
    ConfigModel? config;
    final localConfig = await _loadLocalConfig();

    try {
      if (designSettings.enableFirestoreConfig) {
        config = await appSettingUseCase.getConfigDesign(appModel.locale);
      } else if (designSettings.enableRemoteConfig) {
        config = await _loadConfigUrl(designSettings.remoteUrl);
      }
    } catch (e) {
      Log.shout('AppBloc', e.toString());
    }

    config ??= localConfig;
    await localStorageDataSource.saveDesignConfig(config);

    return config;
  }

  Future<ConfigModel?> loadAppConfig() async {
    DesignSettings designSettings = DesignSettings.local();

    try {
      designSettings =
          (await appSettingUseCase.getDesignSettings()) ?? designSettings;

      if (!appModel.isInit) {
        await getConfig();
      }

      appModel.appConfig = await _getDesignConfig(designSettings);
      return appModel.appConfig;
    } catch (err) {
      Log.shout('AppBloc', err.toString());
      appModel.appConfig = await _loadLocalConfig();
    }

    return appModel.appConfig;
  }

  Future<ConfigModel> _loadConfigUrl([String? url]) async {
    // load on cloud config and update on air
    String path = url ?? kAppConfig;
    final appJson = await http
        .get(Uri.parse(path), headers: {'Accept': 'application/json'});
    final configApp = configModelFromJson(utf8.decode(appJson.bodyBytes));

    return configApp;
  }

  Future<ConfigModel> _loadLocalConfig() async {
    var path = 'lib/src/core/config/config_${appModel.locale}.json';
    ConfigModel appConfig = ConfigModel();

    try {
      final appJson = await rootBundle.loadString(path);
      appConfig = configModelFromJson(appJson);
    } catch (e, trace) {
      Log.printSimpleLog(trace.toString());
      final appJson = await rootBundle.loadString(kAppConfig);
      appConfig = configModelFromJson(appJson);
    }

    return appConfig;
  }

  String getCurrentLanguageFlag() {
    var currentCode = appSettingUseCase.getLanguage();
    for (var item in Utils.getLanguagesList()) {
      if (currentCode == item['code']) {
        return item['icon'];
      }
    }
    return ImageCountry.VN;
  }
}
