import 'package:meditaguru/src/core/network/api_exception.dart';
import 'package:rxdart/rxdart.dart';

import '../../../core/base/bloc/base_bloc.dart';

class UIState {
  CustomException? customException;
  bool isSucess;
  bool isError;
  int errorCount;
  String? message;

  UIState({
    this.isError = false,
    this.message = '',
    this.errorCount = 0,
    this.isSucess = false,
    this.customException,
  });
}

class HandleUIBloc extends BaseBloc {
  PublishSubject<UIState> uiHandleController = PublishSubject<UIState>();
  UIState uiState = UIState();

  @override
  void dispose() {
    if (!uiHandleController.isClosed) {
      uiHandleController.close();
    }
  }

  @override
  void init() {}

  void handleError(String? msg, {CustomException? customException}) {
    uiState
      ..isError = true
      ..message = msg
      ..errorCount = uiState.errorCount++
      ..customException = customException;

    uiHandleController.add(uiState);
  }

  void handleSuccess(String msg) {
    uiState
      ..isError = false
      ..isSucess = true
      ..message = msg;

    uiHandleController.add(uiState);
  }
}
