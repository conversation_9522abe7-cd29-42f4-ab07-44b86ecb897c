import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/domain/entities/category_data.dart';
import 'package:meditaguru/src/domain/usecase/cart_data_usecase.dart';
import 'package:meditaguru/src/presentation/shared/categories/categories_model.dart';

import '../../core/widgets/common/background_layout_widget.dart';
import '../../core/widgets/common/tag_widget.dart';
import '../../di/injection/injection.dart';
import '../shared/account/account_bloc.dart';

class DetailCourseScreen extends StatefulWidget {
  static const String routeName = '/detail-course';

  const DetailCourseScreen({
    super.key,
    required this.course,
    this.category,
    this.isPremium,
  });

  final CourseMeditation course;
  final CategoryData? category;
  final bool? isPremium;

  @override
  State<DetailCourseScreen> createState() => _DetailCourseScreenState();
}

class _DetailCourseScreenState extends State<DetailCourseScreen> {
  CategoryData? get category => widget.category;
  CourseMeditation get course => widget.course;

  String get name => course.title ?? category?.name ?? '';

  @override
  Widget build(BuildContext context) {
    var ctg = widget.category;

    if (widget.category == null) {
      ctg = course.toCategoryData(context.read<CategoriesModel>().categories);
    }
    if (ctg == null) {
      return const SizedBox();
    }

    bool isUnLock = injector<AccountBloc>().isUnLock;
    final isPremium = widget.isPremium ?? ctg.isPaid;

    return BackgroundLayoutWidget(
      backgroundImage: course.backgroundImage,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: SizedBox(
                height: MediaQuery.sizeOf(context).height * 0.15,
              ),
            ),
            if (ctg.time?.isNotEmpty ?? false)
              SliverToBoxAdapter(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      CupertinoIcons.clock,
                      color: Colors.white,
                      size: 15,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      ctg.time!,
                      style: const TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            if (isPremium)
              SliverToBoxAdapter(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: TagWidget(
                      iconPath: isUnLock ? null : IconConstants.lock,
                      text: 'Premium',
                      colorTag: AppColors.secondary2Color,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                    ),
                  ),
                ),
              ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 3),
                child: Center(
                  child: Text(
                    name,
                    style: const TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            if (course.description?.isNotEmpty ?? false)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                  ).copyWith(top: 8, bottom: 40),
                  child: Center(
                    child: Text(
                      course.description!,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                        color: AppColors.subTitleColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              )
            else
              const SliverToBoxAdapter(child: SizedBox(height: 40)),
            if (ctg.listMedia.isNotEmpty)
              SliverList.separated(
                itemCount: ctg.listMedia.length,
                itemBuilder: (context, index) {
                  final lesson = ctg!.listMedia[index];
                  final isLessonPremium = isPremium && lesson.isPremium(index);

                  return CardLessonMeditaWidget(
                    primaryColor: HexColor(course.primaryColor),
                    secondaryColor: HexColor(course.secondaryColor),
                    audio: lesson,
                    imageBackground: course.imageCard,
                    isPaid: isLessonPremium,
                    category: ctg,
                  );
                },
                separatorBuilder: (context, index) => const SizedBox(
                  height: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
