import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/domain/entities/splash_data.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/dashboard_old/settings/setting.dart';

class WelcomeScreen extends StatefulWidget {
  static const String routeName = '/welcome-screen';

  @override
  _WelcomeScreenState createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  late PageController _pagecontroller;
  int currentPage = 0;

  @override
  void initState() {
    _pagecontroller = PageController();
    _pagecontroller.addListener(() {
      if (currentPage != _pagecontroller.page?.floor() &&
          (_pagecontroller.page == 0.0 ||
              _pagecontroller.page == 1.0 ||
              _pagecontroller.page == 2.0)) {
        setState(() {
          currentPage = _pagecontroller.page?.floor() ?? 0;
        });
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var welcomeData = <SplashData>[
      SplashData(
          header: S.of(context).onboardHeader1,
          title: S.of(context).onboardTitle1,
          description: S.of(context).onboardDes1,
          img: 'assets/img/welcome_1.png'),
      SplashData(
          header: S.of(context).onboardHeader2,
          title: S.of(context).onboardTitle2,
          description: S.of(context).onboardDes2,
          img: 'assets/img/welcome_2.png'),
      SplashData(
          header: S.of(context).onboardHeader3,
          title: S.of(context).onboardTitle3,
          description: S.of(context).onboardDes3,
          img: 'assets/img/welcome_3.png'),
    ];
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        child: Stack(
          children: <Widget>[
            Container(
              child: Column(
                children: <Widget>[
                  Expanded(
                      child: PageView.builder(
                          controller: _pagecontroller,
                          itemCount: 3,
                          itemBuilder: (context, position) {
                            var data = welcomeData[position];
                            return Container(
                              child: Column(
                                children: <Widget>[
                                  Expanded(
                                    child: Stack(
                                      children: [
                                        Container(
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            image: DecorationImage(
                                              fit: BoxFit.cover,
                                              image: AssetImage(data.img ?? ''),
                                            ),
                                          ),
                                        ),
                                        BackgroundTextImage(),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    height: 0,
                                  )
                                ],
                              ),
                            );
                          })),
                  // buildBottomNavigationBar()
                ],
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: buildPageIndicator(welcomeData),
            )
          ],
        ),
      ),
    );
  }

  Container buildPageIndicator(List<SplashData> welcomeData) {
    var data = welcomeData[currentPage];
    var indicatorsize = 24.0;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Container(width: double.infinity,),
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              InkWell(
                onTap: () {
                  if (_pagecontroller.page != 0) {
                    _pagecontroller.animateToPage(0,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.linear);
                  }
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 5.0),
                  width: indicatorsize,
                  height: indicatorsize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(indicatorsize),
                    color: currentPage == 0
                        ? AppColors.secondaryColor
                        : AppColors.whiteColor,
                  ),
                ),
              ),
              MBlock(6),
              InkWell(
                onTap: () {
                  if (_pagecontroller.page != 1) {
                    _pagecontroller.animateToPage(1,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.linear);
                  }
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 5.0),
                  width: indicatorsize,
                  height: indicatorsize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(indicatorsize),
                    color: currentPage == 1
                        ? AppColors.secondaryColor
                        : AppColors.whiteColor,
                  ),
                ),
              ),
              MBlock(6),
              InkWell(
                onTap: () {
                  if (_pagecontroller.page != 2) {
                    _pagecontroller.animateToPage(2,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.linear);
                  }
                },
                child: Container(
                  width: indicatorsize,
                  height: indicatorsize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(indicatorsize),
                    color: currentPage == 2
                        ? AppColors.secondaryColor
                        : AppColors.whiteColor,
                  ),
                ),
              )
            ],
          ),
          MBlock(15),
          IgnorePointer(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                CText.title5(
                  data.title ?? '',
                ),
                const SizedBox(height: 15.0),
                Container(
                  child: CText.body(
                    data.description ?? '',
                    fontSize: AppFontSize.normalFontSize2,
                  ),
                )
              ],
            ),
          ),
          MBlock(10),
          buildBottomNavigationBar()
        ],
      ),
    );
  }

  Widget buildBottomNavigationBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 24),
      child: CButton(
        S.of(context).getStarted,
        onTap: () {
          context.startLogin(isReplacement: true);
        },
      ),
    );
  }

  @override
  void dispose() {
    _pagecontroller.dispose();
    super.dispose();
  }
}
