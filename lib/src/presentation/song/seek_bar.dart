import 'dart:math';

import 'package:flutter/material.dart';

import '../../core/lib_common.dart';

class SeekBar extends StatefulWidget {
  final Duration duration;
  final Duration position;
  final ValueChanged<Duration>? onChanged;
  final ValueChanged<Duration>? onChangeEnd;
  final double? downloadProgress;

  SeekBar({
    required this.duration,
    required this.position,
    this.onChanged,
    this.onChangeEnd,
    this.downloadProgress,
  });

  @override
  _SeekBarState createState() => _SeekBarState();
}

class _SeekBarState extends State<SeekBar> {
  double? _dragValue;
  bool _dragging = false;
  double _downloadProgress = 0;
  bool get _enableDownloadProgress => widget.downloadProgress != null;

  @override
  void didUpdateWidget(covariant SeekBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.downloadProgress != oldWidget.downloadProgress &&
        _enableDownloadProgress) {
      _downloadProgress = widget.downloadProgress ?? 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final value = min(_dragValue ?? widget.position.inMilliseconds.toDouble(),
        widget.duration.inMilliseconds.toDouble());
    if (_dragValue != null && !_dragging) {
      _dragValue = null;
    }
    return Stack(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.secondary2Color,
            inactiveTrackColor:
                _enableDownloadProgress ? Colors.white24 : Colors.white,
            trackShape: const RoundedRectSliderTrackShape(),
            // tickMarkShape: RoundSliderTickMarkShape(tickMarkRadius: 4),
            // valueIndicatorShape: PaddleSliderValueIndicatorShape(),

            trackHeight: 4.0,
            thumbColor: AppColors.secondary2Color,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            overlayColor: AppColors.secondary2Color.withAlpha(32),
            // overlayShape: RoundSliderOverlayShape(overlayRadius: 28.0),
          ),
          child: Slider(
            // activeColor: AppColors.secondary2Color,
            // inactiveColor: Colors.white,
            min: 0.0,
            max: widget.duration.inMilliseconds.toDouble(),
            value: value,
            onChanged: (value) {
              if (!_dragging) {
                _dragging = true;
              }
              setState(() {
                _dragValue = value;
                print('value---------: $value');
              });
              if (widget.onChanged != null) {
                widget.onChanged!(Duration(milliseconds: value.round()));
              }
            },
            onChangeEnd: (value) {
              if (widget.onChangeEnd != null) {
                widget.onChangeEnd!(Duration(milliseconds: value.round()));
              }
              print('value---------:onChangeEnd $value');
              _dragging = false;
            },
          ),
        ),
        if (_enableDownloadProgress)
          Positioned.fill(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 23),
                child: LayoutBuilder(builder: (_, constraints) {
                  final width = constraints.maxWidth * _downloadProgress;

                  return Row(
                    children: [
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        height: 4,
                        width: width,
                      ),
                    ],
                  );
                }),
              ),
            ),
          ),

        // Positioned(
        //   right: 16.0,
        //   bottom: 0.0,
        //   child: Text(
        //       RegExp(r'((^0*[1-9]\d*:)?\d{2}:\d{2})\.\d+$')
        //           .firstMatch("$_remaining")
        //           ?.group(1) ??
        //           '$_remaining',
        //       style: Theme.of(context).textTheme.caption?.copyWith(color: Colors.white)),
        // ),
      ],
    );
  }

  // Duration get _remaining => widget.duration - widget.position;
}
