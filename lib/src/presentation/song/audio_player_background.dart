import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:audio_service/audio_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/app_delegate.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/wrappers/toast_message/toast_message.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:meditaguru/src/presentation/song/audio_service_2/common.dart';
import 'package:meditaguru/src/presentation/song/riple_circle/ripple_animation.dart';
import 'package:meditaguru/src/presentation/song/seek_bar.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../core/utils.dart';
import '../../core/widgets/common/will_pop_scope.dart';

class AudioPlayerBackgroundScreen extends StatelessWidget {
  static const routeName = '/song-background';
  final String url;
  final String audioName;
  final String? imageUrl;
  final String? description;
  final String? backgroundImage;

  const AudioPlayerBackgroundScreen({
    Key? key,
    required this.url,
    required this.audioName,
    this.imageUrl,
    this.backgroundImage,
    this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AudioPlayerBackgroundWidget(
      url: url,
      audioName: audioName,
      imageUrl: imageUrl,
      description: description,
      backgroundImage: backgroundImage,
    );
  }
}

class AudioPlayerBackgroundWidget extends StatefulWidget {
  final String url;
  final String audioName;
  final String? imageUrl;
  final String? description;
  final String? backgroundImage;

  AudioPlayerBackgroundWidget({
    Key? key,
    required this.url,
    required this.audioName,
    this.imageUrl,
    this.description,
    this.backgroundImage,
  }) : super(key: key);

  @override
  _State createState() => _State();
}

class _State extends BaseStateScreen<AudioPlayerBackgroundWidget> {
  Duration? _seekValue;

  late StreamSubscription<List<ConnectivityResult>> connectivity;

  void _listenerError() {
    final hasError = audioHandler?.hasError.value;
    if (true == hasError) {
      injector<LoadingBloc>().loaded();
      showToastMessage(
        S.of(context).anErrorOccurredAndTryAgain,
        ToastMessageType.error,
      );

      context.startConfirmReportError(courseName: widget.audioName).then(
        (value) {
          context.pop();
        },
      );
    }
  }

  @override
  void initState() {
    super.initState();
    checkConnectivity();
    connectivity =
        Connectivity().onConnectivityChanged.listen(checkConnectionResult);
    audioHandler?.hasError.addListener(_listenerError);
    initAudioService();
  }

  Future<void> initAudioService() async {
    await audioHandler?.playMediaItem(MediaItem(
      id: widget.url,
      title: widget.audioName,
      artUri: Uri.parse(widget.imageUrl ?? ''),
    ));
    try {
      await Permission.ignoreBatteryOptimizations.request();
    } catch (_) {
      // ignore
    }
  }

  checkConnectionResult(List<ConnectivityResult> result) {
    if (result.contains(ConnectivityResult.none)) {
      showToastMessage(
        S.of(context).oopsInternetLost,
        ToastMessageType.error,
      );
    }
  }

  @override
  void dispose() {
    injector<LoadingBloc>().loaded();
    connectivity.cancel();
    audioHandler?.hasError.removeListener(_listenerError);
    audioHandler?.stop();
    super.dispose();
  }

  Future<bool> _onWillPop() async {
    if (injector<LoadingBloc>().loadingController.value == false) {
      return true;
    }
    return false;
  }

  /// check internet is available or not
  void checkConnectivity() async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      injector<LoadingBloc>().loaded();
      showToastMessage(
        S.of(context).oopsInternetLost,
        ToastMessageType.error,
      );
    }
  }

  String transformMilliseconds(int milliseconds) {
    var hundreds = (milliseconds / 10).truncate();
    var seconds = (hundreds / 100).truncate();
    var minutes = (seconds / 60).truncate();
    var hours = (minutes / 60).truncate();

    var hourStr = (hours % 60).toString().padLeft(2, '0');
    var minuteStr = (minutes % 60).toString().padLeft(2, '0');
    var secondStr = (seconds % 60).toString().padLeft(2, '0');
    if (hours > 0) {
      return '$hourStr:$minuteStr:$secondStr';
    }
    return '$minuteStr:$secondStr';
  }

  Widget _buildCircleImage() {
    final sizeAvatar = 240.0;
    var size = min(MediaQuery.sizeOf(context).width, sizeAvatar + 80);
    return Stack(
      children: [
        Center(
          child: RipplesAnimation(
            isGlow: true,
            size: size,
            color: const Color(0xFFF6FC9C),
            child: Container(
              alignment: Alignment.center,
              width: sizeAvatar,
              height: sizeAvatar,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  width: 1.0,
                  color: AppColors.secondary2Color,
                ),
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFF6FC9C).withOpacity(0.5),
                    offset: const Offset(-1.0, 1.0),
                    blurRadius: 30.0,
                  ),
                ],
              ),
              child: Container(
                width: sizeAvatar - 20,
                height: sizeAvatar - 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(
                      widget.imageUrl ?? '',
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
        ),

        // TODO(tri): not publish in version 2.1.0
        // Align(
        //   alignment: Alignment.topRight,
        //   child: Padding(
        //     padding: const EdgeInsets.only(right: 10),
        //     child: ListenableBuilder(
        //       listenable: _downloadAudioModel,
        //       builder: (_, __) {
        //         final enableDownload = _downloadAudioModel.isEnableCacheAudio(
        //             name: widget.audioName);

        //         return GestureDetector(
        //           onTap: () {
        //             showToastMessage(
        //               '${enableDownload ? 'Tắt' : 'Bật'} cache audio',
        //               ToastMessageType.success,
        //             );

        //             _downloadAudioModel.enableCacheAudio(
        //               widget.url,
        //               widget.audioName,
        //               enableCache: !enableDownload,
        //             );
        //           },
        //           child: Builder(
        //             builder: (context) {
        //               final circleDownload = Container(
        //                 decoration: BoxDecoration(
        //                   color: Colors.white.withOpacity(0.2),
        //                   shape: BoxShape.circle,
        //                 ),
        //                 width: 30,
        //                 height: 30,
        //                 child: Builder(
        //                   builder: (context) {
        //                     if (enableDownload == false) {
        //                       return const Icon(
        //                         Icons.download,
        //                       );
        //                     }

        //                     final streamPercent = _downloadAudioModel
        //                         .getStreamPercent(widget.audioName, widget.url);

        //                     return StreamBuilder<double?>(
        //                       stream: streamPercent,
        //                       builder: (context, snapshot) {
        //                         _isDownloaded =
        //                             snapshot.hasData && snapshot.data == 1.0;

        //                         if (_isDownloaded) {
        //                           return const Icon(
        //                             Icons.download_done,
        //                             color: Colors.green,
        //                           );
        //                         }

        //                         final percent = (snapshot.data ?? 0);
        //                         return SizedBox(
        //                           width: 40,
        //                           height: 40,
        //                           child: Stack(
        //                             children: [
        //                               Center(
        //                                 child: CircularProgressIndicator(
        //                                   value: percent,
        //                                   valueColor:
        //                                       const AlwaysStoppedAnimation<
        //                                           Color>(
        //                                     Colors.green,
        //                                   ),
        //                                   strokeWidth: 2,
        //                                 ),
        //                               ),
        //                               Center(
        //                                 child: Center(
        //                                   child: Padding(
        //                                     padding: const EdgeInsets.all(2.0),
        //                                     child: FittedBox(
        //                                       fit: BoxFit.contain,
        //                                       child: Text(
        //                                         '${(percent * 100).toStringAsFixed(1)}%',
        //                                         style: const TextStyle(
        //                                           color: Colors.white,
        //                                           fontSize: 12,
        //                                         ),
        //                                       ),
        //                                     ),
        //                                   ),
        //                                 ),
        //                               ),
        //                             ],
        //                           ),
        //                         );
        //                       },
        //                     );
        //                   },
        //                 ),
        //               );

        //               return SizedBox(
        //                 width: 40,
        //                 height: 40,
        //                 child: Center(child: circleDownload),
        //               );
        //             },
        //           ),
        //         );
        //       },
        //     ),
        //   ),
        // )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var appWidget = SingleChildScrollView(
      child: Column(
        children: <Widget>[
          // ParticleAnimation(
          //   screenSize: MediaQuery.sizeOf(context),
          //   bgColor: AppColors.primaryColor,
          // ),
          // const SizedBox(
          //   height: 10,
          // ),
          _buildCircleImage(),
          const SizedBox(
            height: 30,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            width: MediaQuery.sizeOf(context).width,
            alignment: Alignment.center,
            child: Text(
              widget.audioName,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 23,
                  color: Colors.white,
                  fontFamily: 'MyFont',
                  fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            width: MediaQuery.sizeOf(context).width,
            alignment: Alignment.center,
            child: Text(
              widget.description ?? '',
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontFamily: 'MyFont',
                  fontWeight: FontWeight.normal),
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Container(
              width: MediaQuery.sizeOf(context).width,
              // padding: EdgeInsets.symmetric(horizontal: 1),
              child: StreamBuilder<bool>(
                stream: audioHandler?.playbackState
                    .map((state) =>
                        state.processingState != AudioProcessingState.idle &&
                        state.processingState != AudioProcessingState.error)
                    .distinct(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState != ConnectionState.active) {
                    // Don't show anything until we've ascertained whether or not the
                    // service is running, since we want to show a different UI in
                    // each case.
                    return const SizedBox();
                  }
                  final running = snapshot.data ?? false;
                  return Column(
                    // mainAxisSize: MainAxisSize.max,
                    // crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (!running) ...[
                        // UI to show when we're not running, i.e. a menu.
                        audioPlayerButton(),
                        // Expanded(
                        //   child: kLoadingWidget(context),
                        // )
                        // if (kIsWeb || !Platform.isMacOS) textToSpeechButton(),
                      ] else ...[
                        // UI to show when we're running, i.e. player state/controls.

                        // Queue display/controls.
                        // StreamBuilder<QueueState>(
                        //   stream: _queueStateStream,
                        //   builder: (context, snapshot) {
                        //     final queueState = snapshot.data;
                        //     final queue = queueState?.queue ?? [];
                        //     final mediaItem = queueState?.mediaItem;
                        //     return Column(
                        //       mainAxisSize: MainAxisSize.min,
                        //       children: [
                        //         if (queue != null && queue.isNotEmpty)
                        //           Row(
                        //             mainAxisAlignment: MainAxisAlignment.center,
                        //             children: [
                        //               IconButton(
                        //                 icon: Icon(Icons.skip_previous),
                        //                 iconSize: 64.0,
                        //                 onPressed: mediaItem == queue.first
                        //                     ? null
                        //                     : AudioService.skipToPrevious,
                        //               ),
                        //               IconButton(
                        //                 icon: Icon(Icons.skip_next),
                        //                 iconSize: 64.0,
                        //                 onPressed: mediaItem == queue.last
                        //                     ? null
                        //                     : AudioService.skipToNext,
                        //               ),
                        //             ],
                        //           ),
                        //         if (mediaItem?.title != null) Text(mediaItem.title),
                        //       ],
                        //     );
                        //   },
                        // ),
                        // A seek bar.

                        // StreamBuilder<MediaState>(
                        //   stream: _mediaStateStream,
                        //   builder: (context, snapshot) {
                        //     final mediaState = snapshot.data;
                        //     double percent = updateTime(mediaState?.position?.inMilliseconds);
                        //     return CircularPercentIndicator(
                        //       radius: 240.0,
                        //       lineWidth: 4.0,
                        //       percent: percent,
                        //       center: Text(
                        //         elapsedTime,
                        //         style: const TextStyle(
                        //           color: Colors.white,
                        //           fontWeight: FontWeight.normal,
                        //           fontSize: 24.0,
                        //         ),
                        //       ),
                        //       backgroundColor: Colors.white,
                        //       progressColor: Colors.blue,
                        //     );
                        //   },
                        // ),
                        MBlock(12),
                        // A seek bar.
                        StreamBuilder<PositionData>(
                          stream: audioHandler?.positionDataStream,
                          builder: (context, snapshot) {
                            final positionData = snapshot.data;

                            return Container(
                              width: double.infinity,
                              padding: const EdgeInsets.only(top: 20),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 40),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  // Container(
                                  //     padding: const EdgeInsets.all(12.0),
                                  //     child: Container(
                                  //       width: 24.0,
                                  //       height: 24.0,
                                  //       child: Center(
                                  //           child: Stack(
                                  //             alignment: AlignmentDirectional.center,
                                  //             children: <Widget>[
                                  //               CircularProgressIndicator(
                                  //                 strokeWidth: 4.0,
                                  //                 backgroundColor: Colors.blueAccent,
                                  //               ),
                                  //               Text("$percent%",
                                  //                   style: TextStyle(fontSize: 8.0, color: Colors.white),
                                  //                   textAlign: TextAlign.center)
                                  //             ],
                                  //           )),
                                  //     )),
                                  Container(
                                    width: 45,
                                    child: Center(
                                      child: Text(
                                        '${transformMilliseconds((_seekValue ?? positionData?.position)?.inMilliseconds ?? 0)}',
                                        style: CText.body2('',
                                                color: const Color(0xFFF5F5F5))
                                            .style,
                                      ),
                                    ),
                                  ),
                                  // FIX: https://github.com/flutter/flutter/issues/29896
                                  Expanded(
                                    flex: 1,
                                    child: SeekBar(
                                      duration: positionData?.duration ??
                                          Duration.zero,
                                      position: positionData?.position ??
                                          Duration.zero,
                                      onChanged: (value) {
                                        _seekValue = value;
                                      },
                                      onChangeEnd: (newPosition) {
                                        _seekValue = null;
                                        audioHandler?.seek(newPosition);
                                      },
                                      downloadProgress:
                                          snapshot.data?.downloadProgress ?? 0,
                                    ),
                                  ),
                                  Container(
                                    width: 45,
                                    child: Center(
                                      child: Text(
                                        '${transformMilliseconds(positionData?.duration.inMilliseconds ?? 0)}',
                                        style: CText.body2(
                                          '',
                                          color: const Color(0xFFF5F5F5),
                                        ).style,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        ),
                        MBlock(12),
                        // Play/pause/stop buttons.
                        StreamBuilder<PlaybackState>(
                          stream: audioHandler?.playbackState,
                          // .map((state) => state.playing)
                          // .distinct(),
                          builder: (context, snapshot) {
                            final playbackState = snapshot.data;
                            final bufferedMs = playbackState
                                    ?.bufferedPosition.inMilliseconds ??
                                0;
                            if (bufferedMs > 0) {
                              injector<LoadingBloc>().loaded();
                            }

                            final playing = playbackState?.playing ?? false;
                            // if (playing) {
                            //   watch.start();
                            // } else {
                            //   watch.stop();
                            // }
                            return Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                IconButton(
                                  icon: Image.asset(
                                    StringUtils.getAssetPath(
                                        'img/ic_forward.png'),
                                    width: 20,
                                    height: 14,
                                  ),
                                  // Icon(Icons.fast_rewind_outlined),
                                  iconSize: 35.0,
                                  onPressed: audioHandler?.rewind,
                                  color: Colors.white,
                                ),
                                const SizedBox(
                                  width: 30,
                                ),
                                ValueListenableBuilder(
                                  valueListenable: audioHandler!.isLoading,
                                  builder: (_, bool isLoading, ___) {
                                    if (isLoading) {
                                      return kLoadingCircleWidget(context);
                                    }

                                    if (playing)
                                      return pauseButton();
                                    else
                                      return playButton();
                                  },
                                ),

                                const SizedBox(
                                  width: 30,
                                ),
                                IconButton(
                                  icon: Image.asset(
                                    StringUtils.getAssetPath(
                                        'img/ic_rewind.png'),
                                    width: 20,
                                    height: 14,
                                  ),
                                  // icon: Icon(Icons.fast_forward_outlined),
                                  iconSize: 35.0,
                                  onPressed: audioHandler?.fastForward,
                                  color: Colors.white,
                                ),
                                // stopButton(),
                              ],
                            );
                          },
                        ),

                        // Display the processing state.
                        // StreamBuilder<AudioProcessingState>(
                        //   stream: AudioService.playbackStateStream
                        //       .map((state) => state.processingState)
                        //       .distinct(),
                        //   builder: (context, snapshot) {
                        //     final processingState =
                        //         snapshot.data ?? AudioProcessingState.none;
                        //     return Text(
                        //         "Processing state: ${describeEnum(processingState)}");
                        //   },
                        // ),
                        // // Display the latest custom event.
                        // StreamBuilder(
                        //   stream: AudioService.customEventStream,
                        //   builder: (context, snapshot) {
                        //     return Text("custom event: ${snapshot.data}");
                        //   },
                        // ),
                        // // Display the notification click status.
                        // StreamBuilder<bool>(
                        //   stream: AudioService.notificationClickEventStream,
                        //   builder: (context, snapshot) {
                        //     return Text(
                        //       'Notification Click Status: ${snapshot.data}',
                        //     );
                        //   },
                        // ),
                      ],
                    ],
                  );
                },
              )),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );

    return WillPopScopeWidget(
      onWillPop: _onWillPop,
      child: MScaffold(
        safeAreaTop: false,
        safeAreaBottom: false,
        onlyUseBgColor: false,
        alignmentBackgroundImage: Alignment.center,
        backgroundImage: widget.backgroundImage,
        fitBackgroundImage: BoxFit.fitHeight,
        floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
        body: ClipRect(
          child: Stack(
            children: [
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.5),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 40, sigmaY: 40),
                    child: const SizedBox(),
                  ),
                ),
              ),
              Positioned.fill(
                child: Column(
                  children: [
                    AppBarCustom(),
                    Expanded(child: appWidget),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // /// A stream reporting the combined state of the current media item and its
  // /// current position.
  // Stream<MediaState> get _mediaStateStream =>
  //     Rx.combineLatest2<MediaItem?, Duration, MediaState>(
  //         audioHandler!.mediaItem, AudioService.position, MediaState.new);

  /// A stream reporting the combined state of the current queue and the current
  /// media item within that queue.

  ElevatedButton audioPlayerButton() => startButton(
        'Play audio',
        initAudioService,
      );

  ElevatedButton startButton(String label, VoidCallback onPressed) =>
      ElevatedButton(
        onPressed: onPressed,
        child: Text(label),
      );

  InkWell playButton() {
    return InkWell(
      onTap: audioHandler?.play,
      child: Container(
        alignment: Alignment.center,
        width: 48.0,
        height: 48.0,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            width: 1.0,
            color: AppColors.secondary2Color,
          ),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFF6FC9C).withOpacity(0.5),
              offset: const Offset(-1.0, 1.0),
              blurRadius: 30.0,
            ),
          ],
        ),
        child: const Icon(
          Icons.play_arrow,
          size: 30,
        ),
      ),
    );
  }

  InkWell pauseButton() {
    return InkWell(
      onTap: audioHandler?.pause,
      child: Container(
        alignment: Alignment.center,
        width: 48.0,
        height: 48.0,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            width: 1.0,
            color: AppColors.secondary2Color,
          ),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF6FC9C), Color(0xFFEAF818)],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFF6FC9C).withOpacity(0.5),
              offset: const Offset(-1.0, 1.0),
              blurRadius: 30.0,
            ),
          ],
        ),
        child: const Icon(
          Icons.pause,
          size: 30,
        ),
      ),
    );
  }

  IconButton stopButton() => IconButton(
      icon: const Icon(Icons.stop),
      iconSize: 64.0,
      onPressed: audioHandler?.stop,
      color: Colors.white);
}

class QueueState {
  final List<MediaItem>? queue;
  final MediaItem? mediaItem;

  QueueState(this.queue, this.mediaItem);
}

class MediaState {
  final MediaItem? mediaItem;
  final Duration? position;

  MediaState(this.mediaItem, this.position);
}
