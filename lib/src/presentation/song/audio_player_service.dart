// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:just_audio/just_audio.dart';
import 'package:meditaguru/src/core/constants/general.dart';

/// This task defines logic for playing a list of podcast episodes.
class AudioPlayerTask extends BackgroundAudioTask {
  MediaLibrary mediaLibrary = MediaLibrary();
  MediaItem? media;
  final AudioPlayer _player = AudioPlayer();
  AudioProcessingState? _skipState;
  Seeker? _seeker;
  StreamSubscription<PlaybackEvent>? _eventSubscription;

  List<MediaItem?> get queue => mediaLibrary.items;
  int get index => _player.currentIndex ?? 0;
  MediaItem? get mediaItem => queue[index];

  @override
  Future<void> onTaskRemoved() async {
    // for android
    await onStop();
  }

  @override
  Future<void> onStart(Map<String, dynamic>? params) async {
    printLog('----->>[Audio][onStart]');

    // We configure the audio session for speech since we're playing a podcast.
    // You can also put this in your app's initialisation if your app doesn't
    // switch between two types of audio as this example does.
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.speech());
    // Broadcast media item changes.
    // _player.currentIndexStream.listen((index) async {
    //   if (index != null) {
    //     await AudioServiceBackground.setMediaItem(media);
    //   }
    // });
    // Propagate all events from the audio player to AudioService clients.
    _eventSubscription = _player.playbackEventStream.listen((event) {
      _broadcastState();
    });
    // Special processing for state transitions.
    _player.processingStateStream.listen((state) {
      switch (state) {
        case ProcessingState.completed:
          // In this example, the service stops when reaching the end.
          try {
            onSeekTo(const Duration(milliseconds: 0));
            onPause();
          } catch (e) {
            printLog(e);
          }
          break;
        case ProcessingState.ready:
          // If we just came from skipping between tracks, clear the skip
          // state now that we're ready to play.
          _skipState = null;
          break;
        default:
          break;
      }
    });

    // Load and broadcast the queue
    // AudioServiceBackground.setQueue(queue);
    try {
      // await _player.setAudioSource(ConcatenatingAudioSource(
      //   children:
      //   queue.map((item) => AudioSource.uri(Uri.parse(item.id))).toList(),
      // ));
      final audioSource = LockCachingAudioSource(
        Uri.parse(params?['audioUrl']),
        tag: params?['audioUrl'],
      );
      var duration = await _player.setAudioSource(audioSource);

      media = MediaItem(
        id: params?['audioUrl'],
        album: 'Thiền Thức Tỉnh',
        title: params?['audioName'],
        artist: 'Thiền Thức Tỉnh',
        duration: duration,
        artUri: Uri.parse(params?['imageUrl']),
      );
      mediaLibrary.items = [media];
      await AudioServiceBackground.setMediaItem(media!);
      // In this example, we automatically start playing on start.
      await onPlay();
    } catch (e) {
      print('Error: $e');
      await onStop();
    }
  }

  @override
  Future<void> onSkipToQueueItem(String mediaId) async {
    // Then default implementations of onSkipToNext and onSkipToPrevious will
    // delegate to this method.
    final newIndex = queue.indexWhere((item) => item?.id == mediaId);
    if (newIndex == -1) return;
    // During a skip, the player may enter the buffering state. We could just
    // propagate that state directly to AudioService clients but AudioService
    // has some more specific states we could use for skipping to next and
    // previous. This variable holds the preferred state to send instead of
    // buffering during a skip, and it is cleared as soon as the player exits
    // buffering (see the listener in onStart).
    _skipState = newIndex > index
        ? AudioProcessingState.idle
        : AudioProcessingState.idle;
    // This jumps to the beginning of the queue item at newIndex.
    await _player.seek(Duration.zero, index: newIndex);
    // Demonstrate custom events.
    AudioServiceBackground.sendCustomEvent('skip to $newIndex');
  }

  @override
  Future<void> onPlay() => _player.play();

  @override
  Future<void> onPause() => _player.pause();

  @override
  Future<void> onSeekTo(Duration position) => _player.seek(position);

  @override
  Future<void> onFastForward() => _seekRelative(fastForwardInterval);

  @override
  Future<void> onRewind() => _seekRelative(-rewindInterval);

  @override
  Future<void> onSeekForward(bool begin) async => _seekContinuously(begin, 1);

  @override
  Future<void> onSeekBackward(bool begin) async => _seekContinuously(begin, -1);

  @override
  Future<void> onStop() async {
    await _player.dispose();
    await _eventSubscription?.cancel();
    // It is important to wait for this state to be broadcast before we shut
    // down the task. If we don't, the background task will be destroyed before
    // the message gets sent to the UI.
    await _broadcastState();
    // Shut down this task
    await super.onStop();
  }

  /// Jumps away from the current position by [offset].
  Future<void> _seekRelative(Duration offset) async {
    var newPosition = _player.position + offset;
    // Make sure we don't jump out of bounds.
    if (newPosition < Duration.zero) newPosition = Duration.zero;
    if (newPosition > (mediaItem?.duration ?? const Duration())) {
      newPosition = mediaItem?.duration ?? const Duration();
    }
    // Perform the jump via a seek.
    await _player.seek(newPosition);
  }

  /// Begins or stops a continuous seek in [direction]. After it begins it will
  /// continue seeking forward or backward by 10 seconds within the audio, at
  /// intervals of 1 second in app time.
  void _seekContinuously(bool begin, int direction) {
    _seeker?.stop();
    if (begin) {
      _seeker = Seeker(_player, Duration(seconds: 10 * direction),
          const Duration(seconds: 1), mediaItem!)
        ..start();
    }
  }

  /// Broadcasts the current state to all clients.
  Future<void> _broadcastState() async {
    await AudioServiceBackground.setState(
      controls: [
        // MediaControl.skipToPrevious,
        MediaControl.rewind,
        if (_player.playing) MediaControl.pause else MediaControl.play,
        MediaControl.stop,
        // MediaControl.skipToNext,
        MediaControl.fastForward,
      ],
      systemActions: [
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
        // new
        MediaAction.fastForward,
        MediaAction.rewind,
      ],
      androidCompactActions: [0, 1, 3],
      processingState: _getProcessingState(),
      playing: _player.playing,
      position: _player.position,
      bufferedPosition: _player.bufferedPosition,
      speed: _player.speed,
    );
  }

  /// Maps just_audio's processing state into into audio_service's playing
  /// state. If we are in the middle of a skip, we use [_skipState] instead.
  AudioProcessingState? _getProcessingState() {
    if (_skipState != null) return _skipState;
    switch (_player.processingState) {
      case ProcessingState.idle:
        return AudioProcessingState.idle;
      case ProcessingState.loading:
        return AudioProcessingState.loading;
      case ProcessingState.buffering:
        return AudioProcessingState.buffering;
      case ProcessingState.ready:
        return AudioProcessingState.ready;
      case ProcessingState.completed:
        return AudioProcessingState.completed;
      default:
        throw Exception('Invalid state: ${_player.processingState}');
    }
  }
}

/// Provides access to a library of media items. In your app, this could come
/// from a database or web service.
class MediaLibrary {
  List<MediaItem?> items = [];
  // List<MediaItem> _items = <MediaItem>[
  //   MediaItem(
  //     id: "https://s3.amazonaws.com/scifri-episodes/scifri20181123-episode.mp3",
  //     album: "Science Friday",
  //     title: "A Salute To Head-Scratching Science",
  //     artist: "Science Friday and WNYC Studios",
  //     duration: Duration(milliseconds: 5739820),
  //     artUri:
  //     "https://media.wnyc.org/i/1400/1400/l/80/1/ScienceFriday_WNYCStudios_1400.jpg",
  //   ),
  //   MediaItem(
  //     id: "https://s3.amazonaws.com/scifri-segments/scifri201711241.mp3",
  //     album: "Science Friday",
  //     title: "From Cat Rheology To Operatic Incompetence",
  //     artist: "Science Friday and WNYC Studios",
  //     duration: Duration(milliseconds: 2856950),
  //     artUri:
  //     "https://media.wnyc.org/i/1400/1400/l/80/1/ScienceFriday_WNYCStudios_1400.jpg",
  //   ),
  // ];
}

// NOTE: Your entrypoint MUST be a top-level function.
// void _textToSpeechTaskEntrypoint() async {
//   AudioServiceBackground.run(() => TextPlayerTask());
// }

/// This task defines logic for speaking a sequence of numbers using
/// text-to-speech.
// class TextPlayerTask extends BackgroundAudioTask {
//   Tts _tts = Tts();
//   bool _finished = false;
//   Sleeper _sleeper = Sleeper();
//   Completer _completer = Completer();
//   bool _interrupted = false;
//
//   bool get _playing => AudioServiceBackground.state.playing;
//
//   @override
//   Future<void> onStart(Map<String, dynamic> params) async {
//     // flutter_tts resets the AVAudioSession category to playAndRecord and the
//     // options to defaultToSpeaker whenever this background isolate is loaded,
//     // so we need to set our preferred audio session configuration here after
//     // that has happened.
//     final session = await AudioSession.instance;
//     await session.configure(AudioSessionConfiguration.speech());
//     // Handle audio interruptions.
//     session.interruptionEventStream.listen((event) {
//       if (event.begin) {
//         if (_playing) {
//           onPause();
//           _interrupted = true;
//         }
//       } else {
//         switch (event.type) {
//           case AudioInterruptionType.pause:
//           case AudioInterruptionType.duck:
//             if (!_playing && _interrupted) {
//               onPlay();
//             }
//             break;
//           case AudioInterruptionType.unknown:
//             break;
//         }
//         _interrupted = false;
//       }
//     });
//     // Handle unplugged headphones.
//     session.becomingNoisyEventStream.listen((_) {
//       if (_playing) onPause();
//     });
//
//     // Start playing.
//     await _playPause();
//     for (var i = 1; i <= 10 && !_finished;) {
//       AudioServiceBackground.setMediaItem(mediaItem(i));
//       AudioServiceBackground.androidForceEnableMediaButtons();
//       try {
//         await _tts.speak('$i');
//         i++;
//         await _sleeper.sleep(Duration(milliseconds: 300));
//       } catch (e) {
//         // Speech was interrupted
//       }
//       // If we were just paused
//       if (!_finished && !_playing) {
//         try {
//           // Wait to be unpaused
//           await _sleeper.sleep();
//         } catch (e) {
//           // unpaused
//         }
//       }
//     }
//     await AudioServiceBackground.setState(
//       controls: [],
//       processingState: AudioProcessingState.stopped,
//       playing: false,
//     );
//     if (!_finished) {
//       onStop();
//     }
//     _completer.complete();
//   }
//
//   @override
//   Future<void> onPlay() => _playPause();
//
//   @override
//   Future<void> onPause() => _playPause();
//
//   @override
//   Future<void> onStop() async {
//     // Signal the speech to stop
//     _finished = true;
//     _sleeper.interrupt();
//     _tts.interrupt();
//     // Wait for the speech to stop
//     await _completer.future;
//     // Shut down this task
//     await super.onStop();
//   }
//
//   MediaItem mediaItem(int number) => MediaItem(
//       id: 'tts_$number',
//       album: 'Numbers',
//       title: 'Number $number',
//       artist: 'Sample Artist');
//
//   Future<void> _playPause() async {
//     if (_playing) {
//       _interrupted = false;
//       await AudioServiceBackground.setState(
//         controls: [MediaControl.play, MediaControl.stop],
//         processingState: AudioProcessingState.ready,
//         playing: false,
//       );
//       _sleeper.interrupt();
//       _tts.interrupt();
//     } else {
//       final session = await AudioSession.instance;
//       // flutter_tts doesn't activate the session, so we do it here. This
//       // allows the app to stop other apps from playing audio while we are
//       // playing audio.
//       if (await session.setActive(true)) {
//         // If we successfully activated the session, set the state to playing
//         // and resume playback.
//         await AudioServiceBackground.setState(
//           controls: [MediaControl.pause, MediaControl.stop],
//           processingState: AudioProcessingState.ready,
//           playing: true,
//         );
//         _sleeper.interrupt();
//       }
//     }
//   }
// }

/// An object that performs interruptable sleep.
// class Sleeper {
//   Completer _blockingCompleter;
//
//   /// Sleep for a duration. If sleep is interrupted, a
//   /// [SleeperInterruptedException] will be thrown.
//   Future<void> sleep([Duration duration]) async {
//     _blockingCompleter = Completer();
//     if (duration != null) {
//       await Future.any([Future.delayed(duration), _blockingCompleter.future]);
//     } else {
//       await _blockingCompleter.future;
//     }
//     final interrupted = _blockingCompleter.isCompleted;
//     _blockingCompleter = null;
//     if (interrupted) {
//       throw SleeperInterruptedException();
//     }
//   }
//
//   /// Interrupt any sleep that's underway.
//   void interrupt() {
//     if (_blockingCompleter?.isCompleted == false) {
//       _blockingCompleter.complete();
//     }
//   }
// }

// class SleeperInterruptedException {}

/// A wrapper around FlutterTts that makes it easier to wait for speech to
/// complete.
// class Tts {
//   final FlutterTts _flutterTts = new FlutterTts();
//   Completer _speechCompleter;
//   bool _interruptRequested = false;
//   bool _playing = false;
//
//   Tts() {
//     _flutterTts.setCompletionHandler(() {
//       _speechCompleter?.complete();
//     });
//   }
//
//   bool get playing => _playing;
//
//   Future<void> speak(String text) async {
//     _playing = true;
//     if (!_interruptRequested) {
//       _speechCompleter = Completer();
//       await _flutterTts.speak(text);
//       await _speechCompleter.future;
//       _speechCompleter = null;
//     }
//     _playing = false;
//     if (_interruptRequested) {
//       _interruptRequested = false;
//       throw TtsInterruptedException();
//     }
//   }
//
//   Future<void> stop() async {
//     if (_playing) {
//       await _flutterTts.stop();
//       _speechCompleter?.complete();
//     }
//   }
//
//   void interrupt() {
//     if (_playing) {
//       _interruptRequested = true;
//       stop();
//     }
//   }
// }

// class TtsInterruptedException {}

class Seeker {
  final AudioPlayer player;
  final Duration positionInterval;
  final Duration stepInterval;
  final MediaItem mediaItem;
  bool _running = false;

  Seeker(
    this.player,
    this.positionInterval,
    this.stepInterval,
    this.mediaItem,
  );

  Future<void> start() async {
    _running = true;
    while (_running) {
      var newPosition = player.position + positionInterval;
      if (newPosition < Duration.zero) newPosition = Duration.zero;
      if (newPosition > (mediaItem.duration ?? const Duration())) {
        newPosition = mediaItem.duration ?? const Duration();
      }
      await player.seek(newPosition);
      await Future.delayed(stepInterval);
    }
  }

  void stop() {
    _running = false;
  }
}
