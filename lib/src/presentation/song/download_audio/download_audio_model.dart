import 'dart:async';
import 'dart:convert';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';

import '../../../core/network/shared_preferences_manager.dart';

const _keySaveEnableDownload = 'key_save_enable_download';

class DownloadAudioModel extends ChangeNotifier {
  final SharedPreferencesManager _preferencesManager;

  DownloadAudioModel(this._preferencesManager) {
    _getEnableDownload();
  }

  // double? _percentDownloaded;
  final _enableDownload = <String, bool>{};
  final _audioDownloading = <String, LockCachingAudioSource>{};
  final _audioStreamDownloading = <String, Stream<double>>{};
  late final _valueListenerEnableDownload = ValueNotifier(_enableDownload);

  // bool get isDownloaded => _percentDownloaded == 1.0;
  // double get percentDownloaded => _percentDownloaded ?? 0.0;
  ValueNotifier<Map<String, bool>> get listenerEnableDownload =>
      _valueListenerEnableDownload;

  Future<void> _getEnableDownload() async {
    final data = _preferencesManager.getString(_keySaveEnableDownload);
    _enableDownload
      ..clear()
      ..addAll(Map.from(jsonDecode(data ?? '{}')));
    _valueListenerEnableDownload.value = Map.from(_enableDownload);
  }

  String _createKeyAudio(String name, {required String debugLabel}) {
    print('>>>>> [createKeyAudio][$debugLabel]: $name');
    return keyToMd5(name);
  }

  Stream<double>? getStreamPercent(String name, String url) {
    final keyAudio = _createKeyAudio(name, debugLabel: 'getStreamPercent');
    final stream = _audioStreamDownloading[keyAudio];
    if (stream != null) {
      return stream;
    }

    if (isEnableCacheAudio(name: name)) {
      var source = _audioDownloading[keyAudio];

      if (source != null) {
        _audioStreamDownloading[keyAudio] = source.downloadProgressStream;
        return source.downloadProgressStream;
      } else {
        source = LockCachingAudioSource(
          Uri.parse(url),
          tag: keyAudio,
        );
        _audioDownloading[keyAudio] = source;
        _audioStreamDownloading[keyAudio] = source.downloadProgressStream;
        return _audioStreamDownloading[keyAudio];
      }
    }

    return null;
  }

  void enableCacheAudio(String url, String name, {bool enableCache = false}) {
    final keyAudio = _createKeyAudio(name, debugLabel: 'enableCacheAudio');
    _enableDownload[keyAudio] = enableCache;

    _preferencesManager.putString(
      _keySaveEnableDownload,
      jsonEncode(_enableDownload),
    );
    _valueListenerEnableDownload.value = Map.from(_enableDownload);

    notifyListeners();
  }

  bool isEnableCacheAudio({required String name}) {
    final keyAudio = _createKeyAudio(name, debugLabel: 'isEnableCacheAudio');
    return _enableDownload[keyAudio] ?? false;
  }

  Future<LockCachingAudioSource> getAudio(
    Uri uri,
    String name,
  ) async {
    final keyAudio = _createKeyAudio(name, debugLabel: 'getAudio');
    var percent = 0.0;
    late LockCachingAudioSource _sourceCurrent;

    if (_audioDownloading.containsKey(keyAudio) == false) {
      _sourceCurrent = LockCachingAudioSource(
        uri,
        tag: keyAudio,
      );

      await _sourceCurrent.request();

      _audioDownloading.addAll({keyAudio: _sourceCurrent});
    } else {
      _sourceCurrent = _audioDownloading[keyAudio]!;
    }

    percent = await _sourceCurrent.downloadProgressStream.first;

    if (percent != 1.0) {
      _audioStreamDownloading[keyAudio] = _sourceCurrent.downloadProgressStream;
      _audioStreamDownloading[keyAudio]!.listen((value) async {
        print(
            '>>>>> [Download Audio]: ${((await _sourceCurrent.downloadProgressStream.first) * 100).toStringAsFixed(2)}%');
      });
    } else {
      await Future.delayed(const Duration(milliseconds: 500));
      notifyListeners();
    }

    return _sourceCurrent;
  }

  void removeCurrent({required String name}) async {
    final keyAudio = _createKeyAudio(name, debugLabel: 'removeCurrent');
    final sourceCurrent = _audioDownloading[keyAudio];

    if (sourceCurrent != null) {
      final keyAudio = _createKeyAudio(name, debugLabel: 'removeCurrent');
      final isEnableDownload = _enableDownload[keyAudio] ?? false;
      if (isEnableDownload) {
        print('>>>>> [CLEAR]:Don\'t remove: $keyAudio');
        return;
      }

      print('>>>>> [CLEAR]: $keyAudio');

      try {
        try {
          _audioDownloading.removeWhere((key, value) => key == keyAudio);
          // await sourceCurrent.cancelDownload();
          await sourceCurrent.clearCache();
        } catch (e, t) {
          print('Error cancelDownload audio: $e');
          print('Error cancelDownload audio: $t');
        }

        _audioStreamDownloading.removeWhere((key, value) => key == keyAudio);
      } catch (e) {
        print('Error removeCurrent audio: $e');
      }
    }
  }
}
