import 'dart:async';

import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:meditaguru/src/core/constants/general.dart';
import 'package:meditaguru/src/core/utils/retry_function.dart';
import 'package:meditaguru/src/presentation/song/audio_player_service.dart';
import 'package:meditaguru/src/presentation/song/audio_service_2/common.dart';
import 'package:meditaguru/src/presentation/song/download_audio/download_audio_model.dart';
import 'package:rxdart/rxdart.dart';

import '../../../core/services/tracking_log_service.dart';

class AudioPlayerHandler extends BaseAudioHandler
    with <PERSON>ueHandler, SeekHandler {
  final DownloadAudioModel downloadAudioModel;
  final TrackingLogService _trackingLogService;
  late StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  late List<ConnectivityResult> connectivityResult;

  bool _interrupted = false;
  late StreamSubscription? _noiseSubscription;
  late StreamSubscription? _interruptionSubscription;

  final _mediaLibrary = MediaLibrary();
  final _player = AudioPlayer();
  late AudioSession _audioSession;

  late LockCachingAudioSource _streamAudioSource;

  final _hasError = ValueNotifier<bool>(false);
  final _isLoading = ValueNotifier<bool>(false);

  double _downloadProgress = 0.0;

  ValueNotifier<bool> get isLoading => _isLoading;

  ValueNotifier<bool> get hasError => _hasError;

  Stream<PositionData> get positionDataStream =>
      Rx.combineLatest3<Duration, double, Duration?, PositionData>(
          _player.positionStream,
          _streamAudioSource.downloadProgressStream,
          _player.durationStream,
          (position, downloadProgress, reportedDuration) {
        final duration = reportedDuration ?? Duration.zero;
        final bufferedPosition = duration * downloadProgress;
        _downloadProgress = downloadProgress;
        return PositionData(
            position, bufferedPosition, duration, downloadProgress);
      });

  int? get index => _player.currentIndex;

  AudioPlayerHandler(this.downloadAudioModel, this._trackingLogService) {
    _init();
  }

  Future<void> _init() async {
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen(_monitorConnectivity);

    _audioSession = await AudioSession.instance;
    await _audioSession.configure(const AudioSessionConfiguration.speech());

    _noiseSubscription = _audioSession.becomingNoisyEventStream.listen((_) {
      if (_player.playing) {
        pause();
      }
    });

    _audioSession.interruptionEventStream.listen((event) {
      if (event.begin) {
        if (_player.playing) {
          pause();
          _interrupted = true;
        }
      } else {
        if (!_player.playing && _interrupted) {
          play();
        }
        _interrupted = false;
      }
    });

    // Propagate all events from the audio player to AudioService clients.
    _player.playbackEventStream.listen(_broadcastState);
    // In this example, the service stops when reaching the end.
    _player.processingStateStream.listen((state) {
      if (state == ProcessingState.completed) {
        try {
          seek(const Duration(milliseconds: 0));
          pause();
        } catch (e) {
          printLog(e);
        }
      }
    });
  }

  void _monitorConnectivity(List<ConnectivityResult> result) async {
    connectivityResult = result;
    if (!result.contains(ConnectivityResult.none) &&
        _mediaLibrary.items.isNotEmpty) {
      try {
        await _streamAudioSource.clearCache();
      } catch (_) {}
      const back = Duration(milliseconds: 1500);
      final lastDuration = _player.position < back ? back : _player.position;
      await _player.stop();
      await _player.restartProxyServer();
      await _playMediaItemWithDuration(
        _mediaLibrary.items.first!,
        lastDuration - back,
      );
    }
  }

  void resumeApplication() {
    _player.restartProxyServer();
  }

  void _addMediaItem(MediaItem media) {
    mediaItem.add(media);
  }

  Future<void> _playMediaItemWithDuration(
      MediaItem mediaItem, Duration? initialDuration) async {
    final timeStart = DateTime.now();
    _sendLog(
      audioName: mediaItem.title,
      id: mediaItem.id,
      messageText: 'Start play audio',
    );

    try {
      _hasError.value = false;

      // TODO: Wait for this issue fixed https://github.com/ryanheise/just_audio/pull/1398
      _streamAudioSource = LockCachingAudioSource(Uri.parse(mediaItem.id));

      final duration = await retryFunction<Duration>(() async {
        return _player.setAudioSource(
          _streamAudioSource,
          initialPosition: initialDuration,
        );
      }, 10);
      mediaItem = MediaItem(
        id: mediaItem.id,
        album: 'Thiền Thức Tỉnh',
        title: mediaItem.title,
        artist: 'Thiền Thức Tỉnh',
        duration: duration,
        artUri: mediaItem.artUri,
      );

      _mediaLibrary.items = [mediaItem];
      _addMediaItem(mediaItem);

      await play();
    } catch (e, t) {
      printLog('----->>[Audio][playMediaItem][error]: $e');
      _hasError.value = true;
      _sendLogError(
        e: e,
        t: t,
        audioName: mediaItem.title,
        id: mediaItem.id,
        timeStart: timeStart,
        timeEnd: DateTime.now(),
      );

      await stop();
    }
  }

  @override
  Future<void> playMediaItem(MediaItem mediaItem) async {
    await _playMediaItemWithDuration(mediaItem, Duration.zero);
  }

  void _sendLogError({
    dynamic e,
    dynamic t,
    required String audioName,
    required String id,
    required DateTime timeStart,
    required DateTime timeEnd,
  }) async {
    _trackingLogService.sendCustomLog({
      'AudioName': audioName,
      'id': id,
      'enableCache': downloadAudioModel.isEnableCacheAudio(name: audioName),
      'error': e.toString(),
      'stack': t.toString(),
      'timeStart': timeStart.toString(),
      'timeEnd': timeEnd.toString(),
    }, isCheckConnection: true);
  }

  void _sendLog(
      {required String audioName,
      required String id,
      required String messageText}) async {
    _trackingLogService.sendCustomLog({
      'AudioName': audioName,
      'id': id,
      'enableCache': downloadAudioModel.isEnableCacheAudio(name: audioName),
      'message': messageText,
    }, isCheckConnection: true);
  }

  @override
  Future<void> play() => _player.play();

  @override
  Future<void> pause() => _player.pause();

  @override
  Future<void> seek(Duration position) {
    // Prevent seek to not downloaded
    final durationBuffered =
        (_player.duration ?? Duration.zero) * _downloadProgress;
    if (connectivityResult.contains(ConnectivityResult.none) &&
        durationBuffered.inMilliseconds < position.inMilliseconds) {
      return Future.value();
    }
    return _player.seek(position);
  }

  @override
  Future<void> stop() async {
    await _player.stop();
    await super.stop();
  }

  @override
  Future customAction(String name, [Map<String, dynamic>? extras]) async {
    printLog('----->>[Audio][customAction] $name');
    if (name == 'dispose') {
      await _noiseSubscription?.cancel();
      await _interruptionSubscription?.cancel();
      await _connectivitySubscription?.cancel();
      await _player.stop();
      await _player.dispose();
      await super.stop();
      return;
    } else {
      return super.customAction(name, extras);
    }
  }

  /// Broadcasts the current state to all clients.
  void _broadcastState(PlaybackEvent event) {
    printLog(
        'value---------: ----->>[Audio][_broadcastState] ${event.processingState}');

    _isLoading.value = [ProcessingState.buffering, ProcessingState.loading]
        .contains(event.processingState);

    if (event.processingState == ProcessingState.buffering &&
        connectivityResult.contains(ConnectivityResult.none)) {
      _isLoading.value = false;
    }

    final playing = _player.playing;

    printLog('value---------: ----->>[Audio][playing] $playing');

    playbackState.add(playbackState.value.copyWith(
      controls: [
        MediaControl.rewind,
        if (playing) MediaControl.pause else MediaControl.play,
        MediaControl.stop,
        MediaControl.fastForward,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
        MediaAction.fastForward,
        MediaAction.rewind,
      },
      androidCompactActionIndices: const [0, 1, 3],
      processingState: const {
        ProcessingState.idle: AudioProcessingState.idle,
        ProcessingState.loading: AudioProcessingState.loading,
        ProcessingState.buffering: AudioProcessingState.buffering,
        ProcessingState.ready: AudioProcessingState.ready,
        ProcessingState.completed: AudioProcessingState.completed,
      }[_player.processingState]!,
      playing: playing,
      updatePosition: _player.position,
      bufferedPosition: _player.bufferedPosition,
      speed: _player.speed,
      queueIndex: event.currentIndex,
    ));
  }
}
