import 'dart:async';

import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_tts/flutter_tts.dart';
import 'package:just_audio/just_audio.dart';
import 'package:meditaguru/src/core/constants/general.dart';
import 'package:meditaguru/src/presentation/song/audio_player_service.dart';
// import 'package:rxdart/rxdart.dart';

/// An [AudioHandler] for playing a list of podcast episodes.
class AudioPlayerHandler extends BaseAudioHandler
    with <PERSON>ueHand<PERSON>, SeekHandler {
  // ignore: close_sinks
  // final BehaviorSubject<List<MediaItem>> _recentSubject =
  //     BehaviorSubject.seeded(<MediaItem>[]);
  final _mediaLibrary = MediaLibrary();
  final _player = AudioPlayer();
  final _hasError = ValueNotifier<bool>(false);

  ValueNotifier<bool> get hasError => _hasError;

  int? get index => _player.currentIndex;

  AudioPlayerHandler() {
    _init();
  }

  Future<void> _init() async {
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.speech());

    // Load and broadcast the queue
    // queue.add(_mediaLibrary.items[MediaLibrary.albumsRootId]!);
    // For Android 11, record the most recent item so it can be resumed.
    // mediaItem
    //     .whereType<MediaItem>()
    //     .listen((item) => _recentSubject.add([item]));
    // Broadcast media item changes.
    // _player.currentIndexStream.listen((index) {
    //   if (index != null) mediaItem.add(queue.value[index]);
    // });
    // Propagate all events from the audio player to AudioService clients.
    _player.playbackEventStream.listen(_broadcastState);
    // In this example, the service stops when reaching the end.
    _player.processingStateStream.listen((state) {
      if (state == ProcessingState.completed) {
        try {
          seek(const Duration(milliseconds: 0));
          pause();
        } catch (e) {
          printLog(e);
        }
      }
    });
    // try {
    //   // After a cold restart (on Android), _player.load jumps straight from
    //   // the loading state to the completed state. Inserting a delay makes it
    //   // work. Not sure why!
    //   //await Future.delayed(Duration(seconds: 2)); // magic delay
    //   await _player.setAudioSource(ConcatenatingAudioSource(
    //     children: queue.value!
    //         .map((item) => AudioSource.uri(Uri.parse(item.id)))
    //         .toList(),
    //   ));
    // } catch (e) {
    //   // ignore: avoid_print
    //   print("Error: $e");
    // }

    // try {
    //   // await _player.setAudioSource(ConcatenatingAudioSource(
    //   //   children:
    //   //   queue.map((item) => AudioSource.uri(Uri.parse(item.id))).toList(),
    //   // ));
    //   var duration = await _player.setAudioSource(AudioSource.uri(Uri.parse(params['audioUrl']??'')));
    //
    //   media = MediaItem(
    //     id: params['audioUrl']??'',
    //     album: 'Thiền Thức Tỉnh',
    //     title: params['audioName']??'',
    //     artist: 'Thiền Thức Tỉnh',
    //     duration: duration,
    //     artUri: Uri.parse(params['imageUrl']??''),
    //   );
    //   _mediaLibrary.items = [
    //     media
    //   ];
    //   mediaItem.add(media);
    //   // await AudioServiceBackground.setMediaItem(media!);
    //   // In this example, we automatically start playing on start.
    //   play();
    // } catch (e) {
    //   print("Error: $e");
    //   stop();
    // }
  }

  @override
  // ignore: avoid_renaming_method_parameters
  Future<void> playMediaItem(MediaItem media) async {
    try {
      // await _player.setAudioSource(ConcatenatingAudioSource(
      //   children:
      //   queue.map((item) => AudioSource.uri(Uri.parse(item.id))).toList(),
      // ));
      _hasError.value = false;
      var duration =
          await _player.setAudioSource(AudioSource.uri(Uri.parse(media.id)));
      media = MediaItem(
        id: media.id,
        album: 'Thiền Thức Tỉnh',
        title: media.title,
        artist: 'Thiền Thức Tỉnh',
        duration: duration,
        artUri: media.artUri,
      );
      _mediaLibrary.items = [media];
      mediaItem.add(media);
      // await AudioServiceBackground.setMediaItem(media!);
      // In this example, we automatically start playing on start.
      await play();
    } catch (e) {
      print('Error: $e');
      _hasError.value = true;
      await stop();
    }
  }

  @override
  Future<void> play() => _player.play();

  @override
  Future<void> pause() => _player.pause();

  @override
  Future<void> seek(Duration position) => _player.seek(position);

  @override
  Future<void> stop() async {
    await _player.stop();
    await playbackState.firstWhere(
        (state) => state.processingState == AudioProcessingState.idle);
  }

  @override
  Future customAction(String name, [Map<String, dynamic>? extras]) async {
    if (name == 'dispose') {
      await _player.dispose();
      await super.stop();
      return;
    } else {
      return super.customAction(name, extras);
    }
  }

  /// Broadcasts the current state to all clients.
  void _broadcastState(PlaybackEvent event) {
    final playing = _player.playing;
    playbackState.add(playbackState.value.copyWith(
      controls: [
        MediaControl.rewind,
        if (playing) MediaControl.pause else MediaControl.play,
        MediaControl.stop,
        MediaControl.fastForward,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
        MediaAction.fastForward,
        MediaAction.rewind,
      },
      androidCompactActionIndices: const [0, 1, 3],
      processingState: const {
        ProcessingState.idle: AudioProcessingState.idle,
        ProcessingState.loading: AudioProcessingState.loading,
        ProcessingState.buffering: AudioProcessingState.buffering,
        ProcessingState.ready: AudioProcessingState.ready,
        ProcessingState.completed: AudioProcessingState.completed,
      }[_player.processingState]!,
      playing: playing,
      updatePosition: _player.position,
      bufferedPosition: _player.bufferedPosition,
      speed: _player.speed,
      queueIndex: event.currentIndex,
    ));
  }
}
