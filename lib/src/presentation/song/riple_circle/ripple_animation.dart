import 'package:flutter/material.dart';

import 'circle_painter.dart';
import 'curve_wave.dart';

class RipplesAnimation extends StatefulWidget {
  const RipplesAnimation({
    Key? key,
    this.size = 360,
    this.color = Colors.red,
    required this.child,
    this.isGlow = false,
  }) : super(key: key);
  final double size;
  final Color color;
  final Widget child;
  final bool isGlow;

  @override
  _RipplesAnimationState createState() => _RipplesAnimationState();
}

class _RipplesAnimationState extends State<RipplesAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 5000),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _button() {
    return Center(
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            widget.isGlow
                ? BoxShadow(
                    color: const Color(0xFFF6FC9C).withOpacity(0.5),
                    offset: const Offset(-1.0, 1.0),
                    blurRadius: 30.0,
                  )
                : const BoxShadow(),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(widget.size),
          child: DecoratedBox(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                // tileMode: TileMode.mirror,
                colors: <Color>[
                  widget.color,
                  Color.lerp(widget.color, Colors.black, .05) ?? widget.color
                ],
              ),
            ),
            child: ScaleTransition(
              scale: Tween(begin: widget.isGlow ? 0.95 : 1.0, end: 1.0).animate(
                // scale: Tween(begin: 0.95, end: 1.0).animate(
                CurvedAnimation(
                  parent: _controller,
                  curve: const CurveWave(),
                ),
              ),
              child: widget.child,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CustomPaint(
        painter: CirclePainter(
          _controller,
          color: widget.color,
        ),
        child: SizedBox(
          width: widget.size,
          height: widget.size,
          child: _button(),
        ),
      ),
    );
  }
}
