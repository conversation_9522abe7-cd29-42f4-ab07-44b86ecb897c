import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/app_delegate.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/widget_wrapper.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'core/routes/route_observer.dart';
import 'core/wrappers/toast_message/toast_message.dart';
import 'route.dart';

class Application extends StatefulWidget {
  final List<SingleChildWidget> providers;

  const Application({super.key, required this.providers});
  @override
  _ApplicationState createState() => _ApplicationState();
}

class _ApplicationState extends State<Application> {
  FirebaseAnalyticsAbs? firebaseAnalyticsAbs;

  @override
  void initState() {
    super.initState();

    try {
      firebaseAnalyticsAbs = FirebaseAnalyticsWapper();
    } catch (e) {
      FirebaseAnalyticsWapper().analytics.logEvent(
        name: 'Bugs',
        parameters: {
          'name': e.toString(),
        },
      );
    }

    // OneSignalWapper()..init();
  }

  @override
  void dispose() {
    audioHandler?.customAction('dispose');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    injector<AccountBloc>().initUXCam();
    return Provider<AppBloc>(
      create: (_) => injector<AppBloc>(),
      dispose: (_, bloc) => bloc.dispose(),
      child: MultiProvider(
        providers: widget.providers,
        child: StreamBuilder<AppState>(
          stream: injector<AppBloc>().appController,
          initialData: injector<AppBloc>().appModel,
          builder: (context, snap) {
            return _myApp();
          },
        ),
      ),
    );
  }

  Widget _myApp() {
    return WidgetWrapper(
      child: MaterialApp(
        navigatorKey: AppBloc.navigatorKey,
        debugShowCheckedModeBanner: false,
        title: 'Thiền Thức Tỉnh',
        localizationsDelegates: [
          S.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          DefaultCupertinoLocalizations.delegate,
        ],
        supportedLocales: S.delegate.supportedLocales,
        locale: Locale(injector<AppBloc>().appModel.locale, ''),
        navigatorObservers: [
          MyRouteObserver(),
          ...?firebaseAnalyticsAbs?.getMNavigatorObservers()
        ],
        theme: getTheme(context),
        onGenerateRoute: Routes.getRouteGenerate,
        initialRoute: '/',
        builder: (context, child) {
          return toastBuilder(context, child!);
        },
      ),
    );
  }

  /// Build the App Theme
  ThemeData getTheme(context) {
    return ThemeData(
      primaryColor: AppColors.primaryColor,
      fontFamily: 'MyFont3',
    );
  }
}
