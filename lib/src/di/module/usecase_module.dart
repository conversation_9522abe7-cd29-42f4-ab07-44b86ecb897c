import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/app_setting_usecase.dart';
import 'package:meditaguru/src/domain/usecase/category_usecase.dart';
import 'package:meditaguru/src/domain/usecase/daily_inspiration_usecase.dart';
import 'package:meditaguru/src/domain/usecase/lesson_usecase.dart';

import '../../domain/repository/app_configs_repository.dart';
import '../../domain/usecase/featured_stories_usecase.dart';
import '../../domain/usecase/user_usecase.dart';

class UseCaseModule extends DIModule {
  @override
  Future<void> provides() async {
    injector
      ..registerLazySingleton<AppSettingUseCase>(() => AppSettingUseCaseImpl(
            shared: injector<SharedPreferencesManager>(),
            appConfigsRepository: injector<AppConfigsRepository>(),
          ))
      ..registerFactory<LessonUsecase>(() => LessonUsecase(injector.get()))
      ..registerFactory<DailyInspirationUsecase>(
          () => DailyInspirationUsecase(injector.get(), injector.get()))
      ..registerFactory<CategoryUsecase>(() => CategoryUsecase(injector.get()))
      ..registerFactory<FeaturedStoriesUsecase>(
          () => FeaturedStoriesUsecase(injector.get()))
      ..registerFactory<UserUsecase>(() => UserUsecase(injector.get()));
  }
}
