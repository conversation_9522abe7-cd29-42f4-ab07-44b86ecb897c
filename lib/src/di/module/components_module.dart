import 'package:meditaguru/src/core/network/shared_preferences_manager.dart';
import 'package:meditaguru/src/core/services/firebase/push_noti/local_push.dart';
import 'package:meditaguru/src/di/injection/injection.dart';

import '../../core/services/device_info_service.dart';
import '../../data/datasource/local/local_stograge.dart';

class ComponentsModule extends DIModule {
  @override
  Future<void> provides() async {
    await DeviceInfoService.getDeviceInfo();
    injector
      ..registerSingleton(SharedPreferencesManager())
      ..registerLazySingleton<LocalPushService>(LocalPushServiceImpl.new)
      ..registerSingleton<DeviceInfoService>(DeviceInfoService())
      ..registerSingleton<LocalStorageDataSource>(LocalStorageDataSource());

    await injector<LocalPushService>().init();
    await injector<SharedPreferencesManager>().init();
  }
}
