import 'package:meditaguru/src/data/repository/lesson_repository.impl.dart';
import 'package:meditaguru/src/data/repository/user_repository.impl.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/repository/category_repository.dart';
import 'package:meditaguru/src/domain/repository/daily_inspiration_repository.dart';
import 'package:meditaguru/src/domain/usecase/medita_usecase.dart';

import '../../data/repository/app_configs_repository.impl.dart';
import '../../data/repository/category_repository.impl.dart';
import '../../data/repository/daily_inspiration_repository.impl.dart';
import '../../data/repository/featured_stories_repository.impl.dart';
import '../../domain/repository/app_configs_repository.dart';
import '../../domain/repository/featured_stories_repository.dart';
import '../../domain/repository/lesson_repository.dart';
import '../../domain/repository/user_repository.dart';

class RepoModule extends DIModule {
  @override
  Future<void> provides() async {
    injector
      ..registerLazySingleton<UserRepo>(() => UserRepoImpl(injector.get()))
      ..registerLazySingleton<MeditaUsecase>(MeditaUsecaseImpl.new)
      ..registerFactory<LessonRepository>(
          () => LessonRepositoryImpl(injector.get()))
      ..registerFactory<DailyInspirationRepository>(
          () => DailyInspirationRepositoryImpl(injector.get()))
      ..registerFactory<CategoryRepository>(
          () => CategoryRepositoryImpl(injector.get()))
      ..registerFactory<FeaturedStoriesRepository>(
          () => FeaturedStoriesRepositoryImpl(injector.get()))
      ..registerFactory<AppConfigsRepository>(
          () => AppConfigsRepositoryImpl(injector.get()));
  }
}
