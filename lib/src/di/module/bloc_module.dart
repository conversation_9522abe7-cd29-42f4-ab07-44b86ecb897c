import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/app_setting_usecase.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:meditaguru/src/presentation/shared/categories/categories_model.dart';
import 'package:meditaguru/src/presentation/shared/daily_inspiration/daily_inspiration_model.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_bloc.dart';

import '../../core/services/firebase/firebase_remote_service_impl.dart';
import '../../data/datasource/local/local_stograge.dart';

class BlocModule extends DIModule {
  @override
  Future<void> provides() async {
    injector
      ..registerLazySingleton<AppBloc>(() => AppBloc(
          appSettingUseCase: injector<AppSettingUseCase>(),
          firebaseRemoteServices: injector<FirebaseRemoteServices>(),
          localStorageDataSource: injector<LocalStorageDataSource>()))
      ..registerLazySingleton<LoadingBloc>(LoadingBloc.new)
      ..registerLazySingleton<NavigationBloc>(NavigationBloc.new)
      ..registerLazySingleton<CategoriesModel>(
        () => CategoriesModel(injector.get()),
      )
      ..registerLazySingleton<DailyInspirationModel>(
        () => DailyInspirationModel(injector.get()),
      )
      ..registerLazySingleton<AccountBloc>(AccountBloc.new);
  }
}
