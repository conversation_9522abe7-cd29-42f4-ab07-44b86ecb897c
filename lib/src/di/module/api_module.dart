import 'dart:async';

import 'package:meditaguru/src/data/datasource/firestore/firestore_daily_inspiration.dart';
import 'package:meditaguru/src/data/datasource/firestore/firestore_user.dart';
import 'package:meditaguru/src/di/injection/injection.dart';

import '../../core/services/firebase/firebase_remote_service_impl.dart';
import '../../core/services/tracking_log_service.dart';
import '../../data/datasource/firestore/firestore_app_config.dart';
import '../../data/datasource/firestore/firestore_category.dart';
import '../../data/datasource/firestore/firestore_featured_stories.dart';
import '../../data/datasource/firestore/firestore_lesson.dart';

class ApiModule extends DIModule {
  @override
  Future<void> provides() async {
    final trackingLogService = TrackingLogService(
      'https://icfssefepmyncnoyauhz.supabase.co/functions/v1/logs',
    );

    injector
      ..registerSingleton<TrackingLogService>(trackingLogService) // temp code
      ..registerFactory<FirestoreLessonDataSource>(
          FirestoreLessonDataSource.new)
      ..registerFactory<FirestoreCategoryDataSource>(
          FirestoreCategoryDataSource.new)
      ..registerFactory<FirestoreFeaturedStoriesDataSource>(
          FirestoreFeaturedStoriesDataSource.new)
      ..registerFactory<FirestoreDailyInspirationDataSource>(
          FirestoreDailyInspirationDataSource.new)
      ..registerFactory<FirebaseRemoteServices>(FirebaseRemoteServicesImpl.new)
      ..registerFactory<FirestoreUserDataSource>(FirestoreUserDataSource.new)
      ..registerFactory<FirestoreAppConfigsDataSource>(
          FirestoreAppConfigsDataSource.new);
  }
}
