import 'package:get_it/get_it.dart';
import 'package:meditaguru/src/di/module/api_module.dart';
import 'package:meditaguru/src/di/module/bloc_module.dart';
import 'package:meditaguru/src/di/module/components_module.dart';
import 'package:meditaguru/src/di/module/repo_module.dart';
import 'package:meditaguru/src/di/module/usecase_module.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:meditaguru/src/presentation/shared/navigation/navigation_bloc.dart';

final injector = GetIt.instance;

abstract class DIModule {
  Future<void> provides();
}

class Injection {
  static Future inject() async {
    await ComponentsModule().provides();
    await ApiModule().provides();
    await RepoModule().provides();
    await UseCaseModule().provides();
    await BlocModule().provides();
  }

  static Future prepareData() async {
    await injector<AppBloc>().init();
    await injector<NavigationBloc>().init();
  }
}
