import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/presentation/auth/forget/forgetPassword.dart';
import 'package:meditaguru/src/presentation/auth/login/login.dart';
import 'package:meditaguru/src/presentation/auth/login/twitterLogin.dart';
import 'package:meditaguru/src/presentation/auth/signup/signup.dart';
import 'package:meditaguru/src/presentation/auth/signup/signup_social.dart';
import 'package:meditaguru/src/presentation/breathing_exercise/breathing_exercise/breathing_exercise_screen.dart';
import 'package:meditaguru/src/presentation/choose_card/choose_card_screen.dart';
import 'package:meditaguru/src/presentation/dashboard_old/dashboard_screen.dart';
import 'package:meditaguru/src/presentation/detail_category_meditaion/detail_category_meditaion_screen.dart';
import 'package:meditaguru/src/presentation/detail_course/detail_course_screen.dart';
import 'package:meditaguru/src/presentation/get_messages/screen_get_messages.dart';
import 'package:meditaguru/src/presentation/html_detail/html_detail_screen.dart';
import 'package:meditaguru/src/presentation/invite_friends/invite_firends.dart';
import 'package:meditaguru/src/presentation/lesson/lesson_details_screen.dart';
import 'package:meditaguru/src/presentation/payment/payment_success_screen.dart';
import 'package:meditaguru/src/presentation/payment/subscription.dart';
import 'package:meditaguru/src/presentation/profile/delete_account.dart';
import 'package:meditaguru/src/presentation/profile/edit_profile.dart';
import 'package:meditaguru/src/presentation/song/audio_player_background.dart';
import 'package:meditaguru/src/presentation/stories/stories.dart';
import 'package:meditaguru/src/presentation/terms_and_condition/terms_condition_screen.dart';
import 'package:meditaguru/src/presentation/video/video_player.dart';
import 'package:meditaguru/src/presentation/welcome/welcome_screen.dart';

import 'app_init.dart';
import 'core/utils.dart';
import 'presentation/breathing_exercise/breathing_exercise_details/breathing_exercise_details_screen.dart';
import 'presentation/breathing_exercise/breathing_exercise_intro/breathing_exercise_intro_screen.dart';
import 'presentation/choose_card/card_detail_screen.dart';
import 'presentation/dashboard/dashboard/dashboard_screen.dart';
import 'presentation/shared/account/account_bloc.dart';

class Routes {
  static Route getRouteGenerate(RouteSettings settings) {
    var args = (settings.arguments as Map?) ?? {};
    switch (settings.name) {
      case '/':
        return _buildRouteFade(
          settings,
          const Scaffold(body: AppInit()),
        );
      case DashboardScreen.routeName:
        return _buildRouteFade(
          settings,
          DashboardScreen.newInstance(),
        );
      case OldDashboardScreen.routeName:
        return _buildRouteFade(
          settings,
          OldDashboardScreen.newInstance(),
        );
      case LoginScreen.routeName:
        return _buildRouteFade(
          settings,
          LoginScreen.newInstace(),
        );
      case TwitterLoginScreen.routeName:
        return _buildRouteFade(
            settings,
            TwitterLoginScreen(
              consumerKey: AccountBloc.twitterConsumerKey,
              consumerSecret: AccountBloc.twitterConsumerSecret,
              oauthCallbackHandler: AccountBloc.twitterCallbackHandler,
            ));
      case WelcomeScreen.routeName:
        return _buildRouteFade(settings, WelcomeScreen());
      case SignupScreen.routeName:
        return _buildRouteFade(settings, SignupScreen.newInstance());
      case SocialSignupScreen.routeName:
        return _buildRouteFade(
            settings, SocialSignupScreen.newInstance(args['user'], args['displayName']));
      case EditProfileScreen.routeName:
        return _buildRouteFade(settings, EditProfileScreen());
      case Subscription.routeName:
        return _buildRouteFade(settings, Subscription.newInstance());
      // case RouteList.songScreen:
      //   return _buildRouteFade(
      //       settings,
      //       AudioPlayerDemo(
      //         audioName: args['audioName'],
      //         url: args['url'],
      //       )
      //   );
      case AudioPlayerBackgroundScreen.routeName:
        return _buildRouteFade(
            settings,
            AudioPlayerBackgroundScreen(
              audioName: args['audioName'],
              url: args['url'],
              imageUrl: args['imageUrl'],
              description: args['description'],
              backgroundImage: args['backgroundImage'],
            ));
      case BumbleBeeRemoteVideo.routeName:
        return _buildRouteFade(
            settings,
            BumbleBeeRemoteVideo(
              videoName: args['videoName'],
              url: args['url'],
              description: args['description'],
            ));
      case ForgetPassword.routeName:
        return _buildRouteFade(settings, ForgetPassword.newInstance());
      case TermsAndCondition.routeName:
        return _buildRouteFade(settings, TermsAndCondition());
      case InviteFirendsScreen.routeName:
        return _buildRouteFade(settings, InviteFirendsScreen());
      case Stories.routeName:
        return _buildRouteFade(settings, Stories(args['storyItems']));
      case DetailCategoryMeditaionScreen.routeName:
        return _buildRouteFade(
          settings,
          DetailCategoryMeditaionScreen(
            category: args['category'],
          ),
        );

      case BreathingExerciseScreen.routeName:
        return _buildRouteFade(settings, const BreathingExerciseScreen());
      case BreathingExerciseDetailsScreen.routeName:
        return _buildRouteFade(
            settings, const BreathingExerciseDetailsScreen());
      case BreathingExerciseIntroScreen.routeName:
        return _buildRouteFade(settings, const BreathingExerciseIntroScreen());
      case LessonDetailsScreen.routeName:
        return _buildRouteFade(
          settings,
          LessonDetailsScreen.newInstance(
              id: args['id'],
              coverImage: args['coverImage'],
              name: args['name'],
              desc: args['description']),
        );
      case PaymentSuccessScreen.routeName:
        return _buildRouteFade(
            settings, PaymentSuccessScreen(message: args['message']));
      case HtmlDetailScreen.routeName:
        return _buildRouteFade(
          settings,
          HtmlDetailScreen(
            news: HtmlDetail(
                image: StringUtils.getAssetPath('assets/img/bg/banner.png'),
                title: 'Thiền Thức Tỉnh',
                content: args['content'] ?? ''),
          ),
        );
      case DeleteAccountScreen.routeName:
        return _buildRouteFade(settings, const DeleteAccountScreen());
      case ScreenGetMessages.routeName:
        return _buildRouteFade(settings, const ScreenGetMessages());
      case ChooseCardScreen.routeName:
        return _buildRouteFade(
          settings,
          const ChooseCardScreen(),
        );
      case DetailCourseScreen.routeName:
        return _buildRouteFade(
          settings,
          DetailCourseScreen(
            course: args['course'],
            category: args['category'],
            isPremium: args['isPremium'],
          ),
        );
      case CardDetailScreen.routeName:
        final heroTag = args['heroTag'];
        return _buildRouteFade(
            settings,
            CardDetailScreen(
              heroTag: heroTag,
            ));
      default:
        return _errorRoute();
    }
  }

  static Route _errorRoute() {
    return MaterialPageRoute(builder: (_) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: const Center(
          child: Text('Page not found'),
        ),
      );
    });
  }

  static PageRoute<dynamic> _buildRouteFade(
    RouteSettings settings,
    Widget builder,
  ) {
    if (isIos || isAndroid) {
      return CupertinoPageRoute(
        builder: (context) => builder,
        settings: settings,
      );
    } else {
      return _FadedTransitionRoute(
        widget: builder,
        settings: settings,
      );
    }
  }
}

class _FadedTransitionRoute extends PageRouteBuilder {
  final Widget widget;

  @override
  final RouteSettings settings;

  _FadedTransitionRoute({required this.widget, required this.settings})
      : super(
            settings: settings,
            pageBuilder: (BuildContext context, Animation<double> animation,
                Animation<double> secondaryAnimation) {
              return widget;
            },
            transitionDuration: const Duration(milliseconds: 100),
            transitionsBuilder: (BuildContext context,
                Animation<double> animation,
                Animation<double> secondaryAnimation,
                Widget child) {
              return FadeTransition(
                opacity: CurvedAnimation(
                  parent: animation,
                  // curve: Curves.easeOut,
                  curve: Curves.linearToEaseOut,
                  reverseCurve: Curves.easeInToLinear,
                ),
                child: child,
              );
            });
}
