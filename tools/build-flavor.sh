#!/bin/sh

PROD="lib/main_production.dart"
STAGE="lib/main_staging.dart"
DEV="lib/main.dart"

FLAVOR=${DEV}

# run build by target flavor
#flutter run --target $FLAVOR

# apk
flutter build apk --target $FLAVOR --release --no-sound-null-safety

# appbundle
flutter build appbundle --target $FLAVOR --release --no-sound-null-safety

# ios
flutter build ios --target $FLAVOR --release --no-sound-null-safety

# web
#flutter build web --web-renderer html --target $FLAVOR --release --no-sound-null-safety
# deploy: firebase deploy