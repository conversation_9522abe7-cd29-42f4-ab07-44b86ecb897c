#!/bin/bash
set -e

API_KEY='886X532FJ6'
API_ISSUER='03398f8e-dd7c-4fd3-ac66-b31af7c42a93'

echo "Starting to build app......... !!!"
fvm flutter build ipa --flavor prod
echo "Starting to upload app......... !!!"
xcrun altool --upload-app --type ios -f build/ios/ipa/*.ipa --apiKey $API_KEY --apiIssuer $API_ISSUER

echo "Build done at $(date '+%F %r') !!!"

echo "Starting to upload dSYM......... !!!"
ios/Pods/FirebaseCrashlytics/upload-symbols -gsp ../config/GoogleService-Info-Prod.plist -p ios ../../build/ios/archive/*.xcarchive/dSYMs
