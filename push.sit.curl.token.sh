# shellscript
DEVICE_TOKEN="fyb9dMI-QrehKrLayzrutc:APA91bGCzz7-aIeEqN7Y58BxqrI1GM14KabuDzf_w69NRUyQBDCID1YYrnsngVQmX60kSkQDzDStcnGtA83urytJLKHcN9mUmfGJ8Y-9QU97rwqfpp70Q7YdPZVfx6GkKUggleW8OkVS"
FIREBASE_SERVER_TOKEN=""

# fZVLFsooTGak5mrkw6ZjaL:APA91bH47TPEAvNz37ygcyfHMbYJi923SEQB9GYXfBchThWnJ3j4PK5dQSEgA-CWdpXuoEpYo7fJeLmsDggO9tnOj1u6svbQzIOdfko7l84nMK5S7tok8qYLrGeuaTQuP6qDt6jojVSj
curl --location --request POST 'https://fcm.googleapis.com/fcm/send' \
--header 'Content-Type: application/json' \
--header 'Authorization: key=AAAA0tOc0dM:APA91bECPB5mKtbuiNEI9DdUL5PzjtyGtKDJsAoxKs8fvirYrDpRtq4LNq6zaxuAv0FGvDOKnkzm9PlyOZ8YQVjf_Dp0YuD-hi-HCG4AREJjqCAj5NOhVU1dHcy47AxgUiuTiGPEv2yx' \
--data-raw '{
  "registration_ids": ["'$DEVICE_TOKEN'"],
  "notification": {
    "title": "Say hello",
    "body": "Hello Welcome to Thiền Thức Tỉnh",
    "sound": "default",
    "badge": 0
  },
  "click_action": "FLUTTER_NOTIFICATION_CLICK",
  "data": {
    "type": "Assignment",
    "url": "Assignment/111"
  },
  "priority": "high",
  "content_available": true
}'

