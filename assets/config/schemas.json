{"categories": {"__meta__": {"createdBy": "LAXZXySoA2RDZaD4ka6hEgxZwQk1", "createdDate": {"seconds": 1579863907, "nanoseconds": 601000000}, "docId": "QyhrXVQCdi8092PuDsOh", "env": "production", "fl_id": "categories", "lastModifiedBy": "LAXZXySoA2RDZaD4ka6hEgxZwQk1", "lastModifiedDate": {"seconds": 1579864692, "nanoseconds": 614000000}}, "description": "", "enabled": true, "fields": [{"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "^Field is required"}, "uniqueKey": "EfqDexyb"}], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576230227248, "key": "name", "show": true, "title": "Name", "type": "text"}, {"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "Cover image is required"}, "uniqueKey": "af3bF9yq"}], "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576230256469, "key": "coverImage", "limit": 1, "mediaTypes": ["images"], "show": false, "title": "Cover Image", "type": "media"}, {"description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576230829838, "key": "audios", "layout": "list", "options": [{"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "Audio Name is required"}, "uniqueKey": "S0rDoufun"}], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576231007457, "key": "audioName", "show": false, "title": "Audio Name", "type": "text"}, {"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "Audio Description is required"}, "uniqueKey": "zvvEKvgV"}], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576231413851, "key": "audioDescription", "show": false, "title": "Audio Description", "type": "text"}, {"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "Audio cover image is required"}, "uniqueKey": "36aEg4HS"}], "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576231055032, "key": "audioCoverImage", "limit": 1, "mediaTypes": ["images"], "show": false, "title": "Audio Cover Image", "type": "media"}, {"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "Mp3 file is required"}, "uniqueKey": "ytvtOHyy"}], "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576231198798, "key": "audioFile", "limit": 1, "mediaTypes": ["files"], "show": false, "title": "Audio file", "type": "media"}, {"constraints": [], "defaultValue": true, "description": "Turn it off if you're not integrating payment gateway", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1579774900674, "key": "isPaid", "show": false, "title": "Is Paid?", "type": "boolean"}], "overviewFields": ["audioName"], "overviewFieldsSeparator": " | ", "show": true, "title": "Audios", "type": "repeater"}], "group": "Categories", "icon": "", "id": "categories", "sortable": true, "title": "Categories", "type": "collection"}, "users": {"__meta__": {"createdBy": "LAXZXySoA2RDZaD4ka6hEgxZwQk1", "createdDate": {"seconds": 1579863907, "nanoseconds": 602000000}, "docId": "cZ841gkRRkjhYsrMpK4H", "env": "production", "fl_id": "users"}, "description": "", "enabled": true, "fields": [{"constraints": [], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1577944157488, "key": "email", "show": true, "title": "Email", "type": "text"}, {"constraints": [], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1577944156258, "key": "name", "show": true, "title": "Name", "type": "text"}, {"constraints": [], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1577944159986, "key": "phone", "show": true, "title": "Phone", "type": "text"}, {"constraints": [], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1577944414037, "key": "photoUrl", "show": false, "title": "Picture", "type": "text"}, {"description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1578570021059, "key": "transaction", "layout": "table", "options": [{"constraints": [], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1578570027955, "key": "transactionId", "show": false, "title": "TransactionId", "type": "text"}, {"constraints": [], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1578570026200, "key": "amount", "show": false, "title": "Amount", "type": "text"}, {"constraints": [], "defaultValue": "2020-01-09T17:15:23+05:30", "description": "", "displayFormat": "dd/MM/yyyy", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1578570197244, "key": "subscribedAt", "show": false, "title": "Subscribed At", "type": "date"}], "overviewFields": ["subscribedAt", "transactionId", "amount"], "overviewFieldsSeparator": " - ", "show": true, "title": "Transactions", "type": "repeater"}], "group": "users", "icon": "", "id": "users", "sortable": true, "title": "users", "type": "collection"}, "featuredStories": {"__meta__": {"createdBy": "LAXZXySoA2RDZaD4ka6hEgxZwQk1", "createdDate": {"seconds": 1579863907, "nanoseconds": 598000000}, "docId": "hw8DCgBKG4bbOsMPAi6D", "env": "production", "fl_id": "featuredStories"}, "description": "", "enabled": true, "fields": [{"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "is": "", "maximum": "", "message": "^Name is required", "minimum": "", "tooLong": "is too long (maximum is %{count} characters)", "tooShort": "is too short (minimum is %{count} characters)", "wrongLength": "is the wrong length (should be %{count} characters)"}, "uniqueKey": "YrX9ZHug"}, {"rule": "length", "ruleValue": {"is": "", "maximum": 20, "minimum": 1, "tooLong": "is too long (maximum is %{count} characters)", "tooShort": "is too short (minimum is %{count} characters)", "wrongLength": "is the wrong length (should be %{count} characters)"}, "uniqueKey": "JKTxIAmN"}], "defaultValue": "", "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576222646060, "key": "name", "show": true, "title": "Name", "type": "text"}, {"constraints": [{"rule": "presence", "ruleValue": {"allowEmpty": false, "message": "^Field is required"}, "uniqueKey": "WIQXVIe9"}], "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576222959069, "key": "coverImage", "limit": 1, "mediaTypes": ["images"], "show": false, "title": "Cover Image", "type": "media"}, {"description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576662566820, "key": "storyItems", "layout": "table", "options": [{"constraints": [], "defaultValue": "", "description": "(If any)", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576662648396, "key": "caption", "show": false, "title": "Caption", "type": "text"}, {"constraints": [], "description": "", "gridColumns": {"lg": 12, "md": 12, "sm": 12, "xs": 12}, "hidden": false, "id": 1576662802816, "key": "storyImage", "limit": 1, "mediaTypes": ["images"], "show": false, "title": "image", "type": "media"}], "show": false, "title": "Story Items", "type": "repeater"}], "group": "Featured Stories", "icon": "", "id": "featuredStories", "sortable": true, "title": "Featured Stories", "type": "collection"}}